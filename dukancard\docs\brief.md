Project Brief: Dukancard (Next.js)

## Executive Summary
Dukancard is a web application designed to empower users to create and manage interactive digital product cards, fostering community engagement and facilitating online transactions. It addresses the need for businesses and individuals to establish a dynamic digital presence for their products, offering a streamlined platform for display, subscription management, and customer interaction. Its key value proposition lies in providing an all-in-one solution for digital product showcasing, community building, and integrated payment processing.

## Problem Statement
In today's digital economy, many businesses and individuals struggle to effectively showcase their products and services online in an engaging and easily manageable format. Existing solutions often present a fragmented experience, requiring multiple platforms for product display, customer interaction, and subscription management. This leads to increased operational overhead, inconsistent branding, and missed opportunities for community building around their offerings. Simultaneously, customers often feel disconnected from local businesses, relying on impersonal, large e-commerce platforms where sellers are unknown and deliveries are delayed. This disconnect hinders the formation of local communities and trust between buyers and sellers. Furthermore, the lack of a unified, intuitive platform that serves both businesses and customers hinders their ability to scale and adapt to evolving market demands, ultimately impacting their reach and revenue potential.

## Proposed Solution
Dukancard offers a unified digital platform that empowers local businesses to create and manage interactive digital product cards, streamlining their online presence and fostering direct engagement with customers. By providing a dedicated space for product display, subscription management, and community interaction, Dukancard enables businesses to overcome the challenges of fragmented online tools and build stronger brand identities. For customers, Dukancard provides a transparent and localized shopping experience, allowing them to discover and connect with trusted local businesses, fostering a sense of community and reducing reliance on impersonal e-commerce giants. This solution will succeed by bridging the gap between local businesses and their communities, offering a personalized and efficient platform for both product showcasing and customer connection. **Crucially, Dukancard is designed as a hyperlocal social commerce platform, where users can post, like, and comment on content, fostering a vibrant local community around businesses and products, much like a social media platform.**

## Target Users

### Primary User Segment: Local Businesses (Merchants)
*   **Demographic/Firmographic Profile:** Small to medium-sized businesses (SMBs) primarily located in India, including local shops, restaurants, and service providers. These businesses are often already engaged with online platforms like JustDial for lead generation or Amazon/Zomato for sales.
*   **Current Behaviors and Workflows:** These businesses currently rely on a mix of traditional walk-in customers, phone orders, and existing online platforms. They actively seek ways to expand their digital reach and streamline their sales processes.
*   **Specific Needs and Pain Points:** A major pain point is the high commission fees imposed by large e-commerce and food delivery platforms, which significantly cut into their profits. They also experience frustration with lead generation platforms like JustDial, where leads often don't convert into actual sales. There's a strong desire for more direct customer engagement and community building, rather than being just another anonymous seller on a large marketplace.
*   **Goals They're Trying To Achieve:** Their primary goals are to increase profitability by reducing commission costs, improve lead conversion rates, build a loyal local customer base, and establish a strong, direct connection to their community. They aim to efficiently manage their online presence and facilitate quick, local deliveries.

### Secondary User Segment: Local Customers
*   **Demographic/Firmographic Profile:** Consumers in India who prefer to shop locally and are increasingly accustomed to quick commerce and convenient delivery options. They are often tech-savvy and value transparency in their purchasing decisions.
*   **Current Behaviors and Workflows:** These customers currently use a mix of traditional in-store shopping and large e-commerce platforms. They are increasingly seeking faster delivery times and a more personal connection to the businesses they patronize.
*   **Specific Needs and Pain Points:** A key pain point is the lack of transparency regarding sellers on large e-commerce platforms, leading to uncertainty about product quality and origin. They also face long delivery times and a lack of direct engagement with local businesses. There's a desire to support local economies and build community ties through their purchasing habits.
*   **Goals They're Trying To Achieve:** Their primary goals are to easily discover and connect with trusted local businesses, receive products quickly (same-day or 1-2 day delivery), and contribute to their local community. They seek a more personalized and reliable shopping experience that fosters trust and transparency.

## Goals & Success Metrics

### Business Objectives
- Increase Merchant Onboarding: Achieve a 25% month-over-month growth in active local business sign-ups for the first six months post-launch.
- Drive Local Transactions: Facilitate an average of 500 daily transactions across all active merchants within the first three months of reaching target merchant density in a locality.
- Enhance Customer Engagement: Achieve a 40% monthly active user rate among registered customers, with an average of 3 interactions (e.g., product views, likes, comments, orders) per session, within six months of launch.
- Reduce Merchant Costs: Enable participating merchants to reduce their average commission expenditure on third-party platforms by 15% within their first year of using Dukancard.

### User Success Metrics
- Merchant Product Card Creation: 80% of newly onboarded merchants successfully create and publish at least one product card within 7 days of signing up.
- Customer Local Discovery: 60% of active customers utilize the local search or community feed features to discover new local businesses or products each month.
- Repeat Customer Engagement: Achieve a 30% month-over-month retention rate for customers who have placed at least one order through Dukancard.
- Merchant-Customer Interaction: Increase the average number of direct messages or comments between merchants and customers by 20% month-over-month.

### Key Performance Indicators (KPIs)
- **Active Merchant Count:** Total number of merchants with at least one active product card and one transaction in the last 30 days.
    *   *Target:* 1,000 active merchants within 6 months.
- **Gross Merchandise Value (GMV) - Local:** Total value of goods and services transacted through Dukancard by local customers.
    *   *Target:* ₹50,00,000 (50 Lakhs INR) GMV per month within 9 months.
- **Customer Acquisition Cost (CAC):** Cost to acquire a new active customer.
    *   *Target:* Below ₹400 per active customer.
- **Merchant Lifetime Value (LTV):** Average revenue generated by a merchant over their engagement with Dukancard.
    *   *Target:* LTV:CAC ratio of 3:1 within 12 months.
- **Average Order Value (AOV):** The average value of each transaction on the platform.
    *   *Target:* ₹1200 AOV within 6 months.
- **City Coverage:** Number of cities where Dukancard is fully operational with a minimum of 50 active merchants.
    *   *Target:* 3 cities within 12 months.

## MVP Scope

### Core Features (Must Have)
- **User Authentication & Authorization:** Allows users (both merchants and customers) to securely sign up, log in, and manage their basic profiles.
    *   *Rationale:* This is a foundational requirement for any personalized application, enabling secure access and differentiation between user roles.
- **User Onboarding Flow:** Guides new users through the initial setup process, including role selection (business/customer) and essential profile completion.
    *   *Rationale:* A smooth onboarding experience is critical for user adoption and ensuring users can quickly start utilizing the platform's core features.
- **Digital Card Creation & Management (for Merchants):** Enables merchants to create, edit, publish, and manage their digital product cards, including adding product details and images.
    *   *Rationale:* This is the central value proposition for businesses, allowing them to showcase their offerings online.
- **Product Variants:** Support for defining different variations of a product (e.g., size, color) without complex inventory tracking.
    *   *Rationale:* Allows merchants to offer a wider range of products and caters to diverse customer needs within the MVP.
- **Product Display & Viewing (for Customers):** Allows customers to browse and view digital product cards, including product details, images, and merchant information.
    *   *Rationale:* This is the core experience for customers, enabling them to discover local products and businesses.
- **Subscription Management:** Provides functionality for users to manage their subscription plans, including viewing current status and upgrading/downgrading.
    *   *Rationale:* Essential for the application's business model and for users to access premium features.
- **User Dashboards (Business & Customer):** Dedicated dashboards for merchants to manage their cards, products, and interactions, and for customers to track their activities and discoveries.
    *   *Rationale:* Provides a centralized hub for users to interact with the platform and manage their specific roles.
- **Community Feed System:** Enables businesses and customers to connect through posts, comments, and other interactions, fostering a local social commerce environment.
    *   *Rationale:* This is a key differentiator, promoting local engagement and community building.
- **Razorpay Payment Integration:** Facilitates secure payment processing for subscriptions and potentially direct product purchases.
    *   *Rationale:* Crucial for monetizing the platform and enabling transactions.

### Out of Scope for MVP
- **Advanced Analytics & Reporting for Merchants:** Detailed sales reports, customer demographics, or advanced performance metrics beyond basic dashboard views.
    *   *Rationale:* While valuable, these can be complex to implement and are not critical for the initial core functionality of card creation and transaction.
- **In-App Chat/Direct Messaging between Users:** Real-time, one-to-one chat functionality between customers and merchants.
    *   *Rationale:* The community feed provides initial interaction. Direct messaging can be added in a later phase to enhance communication.
- **Inventory Management:** Real-time tracking and management of product stock levels.
    *   *Rationale:* While product variants are included, full inventory management adds significant complexity and can be handled externally or in a later phase.
- **Referral Programs or Loyalty Features:** Functionality to incentivize user referrals or reward loyal customers.
    *   *Rationale:* Growth and retention strategies can be implemented post-MVP once the core value proposition is proven.
- **Multi-language Support:** Support for languages other than English (or the primary language of India).
    *   *Rationale:* Focus on the primary target audience first; localization can be a post-MVP enhancement.

### MVP Success Criteria
- **Successful User Onboarding:** 90% of new users (both merchants and customers) successfully complete the onboarding flow and reach their respective dashboards.
- **Core Feature Adoption:** At least 70% of active merchants successfully create and publish their first digital product card within 48 hours of onboarding.
- **Basic Transaction Flow:** The Razorpay payment integration successfully processes at least 100 test transactions (subscriptions or product purchases) without critical errors.
- **Platform Stability:** The application maintains an uptime of 99.5% during the first month post-launch, with no critical bugs reported in the core user flows (authentication, onboarding, card viewing/creation).
- **Initial Community Engagement:** The community feed shows at least 50 unique posts and 200 interactions (likes/comments) within the first month of launch.

## Post-MVP Vision

### Phase 2 Features
- **Delivery Functionality:** Integration with local delivery services or a built-in delivery management system to facilitate same-day or 1-2 day delivery for products.
- **Advertisement Platform:** Functionality for businesses to run targeted advertisements within the Dukancard platform, allowing them to promote their products or services to selected localities.

### Long-term Vision
- **Digitalization of Entire Business Sector:** Expand platform capabilities to encompass a wider range of business operations, moving beyond just product showcasing to full digital transformation for local businesses.
- **Revolutionize Local Shopping & Buying Behavior:** Establish Dukancard as the primary platform for local commerce, shifting consumer habits away from large e-commerce platforms towards direct engagement with local businesses.
- **Eliminate Middleman Friction:** Continuously develop features and business models that reduce reliance on traditional intermediaries, empowering local businesses and fostering direct relationships with customers.

### Expansion Opportunities
- **Geographic Expansion:** Roll out Dukancard to more cities across India and potentially other regions.
- **New Business Verticals:** Adapt the platform to cater to specific niches or industries beyond general product sales (e.g., services, events).
- **Enhanced Social Features:** Introduce more robust social networking capabilities to further strengthen community ties.

## Technical Considerations

### Platform Requirements
- **Target Platforms:** Web Responsive (Desktop, Tablet, Mobile Browsers)
- **Browser/OS Support:** Modern evergreen browsers (Chrome, Firefox, Edge, Safari) on their latest stable versions. No specific OS-level requirements beyond browser compatibility.
- **Performance Requirements:**
    *   Page load times: Under 2 seconds for initial load (LCP) on a 3G network.
    *   Interaction to Next Paint (INP): Under 200ms.
    *   Responsiveness: Smooth user experience across various screen sizes and device types.

### Technology Preferences
- **Frontend:** Next.js (Framework), React (UI Library), Tailwind CSS (Styling), Shadcn UI (UI Components), Framer Motion (Animations)
- **Backend:** Supabase (Database, Authentication, and other backend services)
- **Database:** Supabase (PostgreSQL)
- **Hosting/Infrastructure:** Cloud Supabase (Backend), Google Cloud Run (Frontend Hosting)
- **Other Key Libraries/Tools:** React Hook Form (Forms), Zod (Validation), Jest (Unit/Integration Testing), Playwright (E2E Testing), Razorpay (Payments), Upstash Ratelimit (Rate Limiting)

### Architecture Considerations
- **Repository Structure:** Polyrepo (separate repositories for `dukancard` and `dukancard-app`).
- **Service Architecture:**
    *   Frontend: Next.js application hosted on Google Cloud Run.
    *   Backend: Supabase for database, authentication, and real-time services. Internal API routes within Next.js (`app/api`) will handle specific business logic and interactions with Supabase.
- **Integration Requirements:**
    *   Supabase: Core backend services (Auth, Database, Realtime).
    *   Razorpay: Payment gateway integration.
    *   Upstash: Rate limiting.
- **Security/Compliance:**
    *   Leverage Supabase's built-in authentication and Row Level Security (RLS) for data access control.
    *   Implement secure API practices for internal and external integrations.

## Constraints & Assumptions

### Constraints
- **Budget:** Self-funded, with limited financial resources. Currently utilizing a **Redis free plan** and a **Supabase Pro plan**. Operational costs are a significant consideration, and careful management is required.
- **Timeline:** MVP has already been launched. Future development will focus on iterative enhancements and addressing technical debt within existing operational constraints.
- **Resources:** Very small development team, currently a solo developer leveraging AI agents for accelerated development. This necessitates a focus on highly efficient workflows and leveraging existing tools/services.
- **Technical:**
    *   Backend development is strictly limited to Supabase functionalities (Database, Auth, Realtime, Edge Functions). No custom backend servers or services are to be developed.
    *   Reliance on existing open-source tools and free-tier services where feasible, while acknowledging current paid services (Supabase Pro).

### Key Assumptions
- **Market Demand:** There is a significant and growing demand among local Indian businesses for a platform that reduces reliance on high-commission e-commerce giants and fosters direct customer engagement.
- **User Adoption:** Both local businesses and customers in target cities will be willing to adopt a new platform for social commerce, given its value proposition (cost savings for businesses, local connection and quick delivery for customers).
- **Supabase Scalability:** Supabase's Pro plan will adequately support initial user growth and transaction volumes without significant performance bottlenecks or unexpected costs.
- **Razorpay Integration Stability:** The Razorpay payment gateway will provide reliable and secure transaction processing for both subscriptions and product purchases.
- **AI Agent Efficiency:** The continued use of AI agents will significantly accelerate development and maintenance, compensating for the small development team size.
- **Local Delivery Infrastructure:** Adequate local delivery infrastructure (either third-party services or a community-driven model) will be available and cost-effective to support the 1-2 day delivery promise in target cities.
- **Regulatory Compliance:** The platform will comply with all relevant Indian e-commerce, data privacy, and payment regulations without requiring extensive legal overhead in the initial phases.

## Risks & Open Questions

### Key Risks
- **Low User Adoption (Businesses & Customers):** Despite the value proposition, businesses may be hesitant to switch from established platforms, and customers may not readily adopt a new local commerce platform.
    *   *Impact:* Failure to achieve critical mass, leading to an unsustainable ecosystem.
- **Supabase Cost Escalation:** While currently on a Pro plan, unexpected increases in usage or changes in Supabase pricing could lead to unsustainable costs.
    *   *Impact:* Increased operational costs, potential service disruption, or need for re-platforming.
- **Competition from Established Players:** Large e-commerce and local service platforms may introduce features or pricing strategies that directly compete with Dukancard's offerings, making it difficult to gain market share.
    *   *Impact:* Stifled growth, reduced market penetration.
- **Delivery Infrastructure Challenges:** Difficulty in establishing reliable and cost-effective local delivery partnerships or building an efficient in-house delivery network in target cities.
    *   *Impact:* Inability to fulfill the 1-2 day delivery promise, leading to customer dissatisfaction.
- **Regulatory Changes:** Evolving e-commerce, data privacy, or payment regulations in India could require significant platform adjustments, leading to unexpected development costs and delays.
    *   *Impact:* Legal non-compliance, operational disruption, increased costs.
- **AI Agent Dependency & Limitations:** Over-reliance on AI agents for development might lead to unforeseen complexities, limitations in handling nuanced coding tasks, or difficulty in debugging AI-generated code.
    *   *Impact:* Slower development, increased technical debt, quality issues.

### Open Questions
- **User Acquisition Strategy:** What are the most effective and cost-efficient channels for acquiring both local businesses and customers in target cities?
- **Monetization Expansion:** Beyond subscriptions, what are potential future monetization strategies (e.g., transaction fees, premium features for businesses, advertising models) that align with the platform's values and financial constraints?
- **Community Moderation:** What are the best practices and tools for effectively moderating user-generated content in the community feed to ensure a positive and safe environment?
- **Scalability Beyond Supabase:** At what point will the current Supabase Pro plan become a bottleneck, and what are the alternative backend solutions or architectural changes required for significant scaling?
- **Delivery Partner Integration:** What are the specific requirements and challenges of integrating with various local delivery partners in India, and how can this be streamlined?

### Areas Needing Further Research
- **Competitive Landscape Deep Dive:** A more in-depth analysis of direct and indirect competitors in the Indian hyperlocal social commerce space, including their strengths, weaknesses, and market share.
- **User Behavior Analytics:** Detailed analysis of user engagement patterns within the community feed and product card interactions to identify areas for optimization and new feature development.
- **Regulatory Compliance Evolution:** Ongoing monitoring of changes in Indian e-commerce, data privacy (e.g., DPDP Act), and payment regulations to ensure continuous compliance.
- **AI Agent Workflow Optimization:** Research into advanced techniques and best practices for maximizing the efficiency and quality of development using AI agents, including potential for automated testing and deployment.

## Appendices

### A. Research Summary
(To be populated with summaries of market research, competitive analysis, user interviews, or technical feasibility studies as they become available.)

### B. Stakeholder Input
(To be populated with key feedback and decisions from stakeholders.)

### C. References
(To be populated with relevant links and documents, e.g., links to Supabase documentation, Razorpay API docs, etc.)