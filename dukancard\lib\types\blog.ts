/**
 * Blog-related TypeScript interfaces and types
 */

import { Blogs } from "@/types/supabase";

export type Blog = Blogs;

export type BlogStatus = "draft" | "published" | "archived";

export interface BlogListItem {
  id?: string;
  title: string;
  slug?: string | null;
  excerpt?: string | null;
  featured_image_url?: string | null;
  author_name: string;
  categories: string[] | null;
  tags: string[] | null;
  reading_time_minutes?: number | null;
  published_at?: string | null;
}

export interface BlogSearchParams {
  query?: string;
  sort?: "newest" | "oldest";
  page?: number;
  limit?: number;
}

export interface BlogSearchResult {
  blogs: BlogListItem[];
  total: number;
  page: number;
  totalPages: number;
  hasMore: boolean;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  limit: number;
}

export interface BlogMetadata {
  title: string;
  description: string;
  image?: string | null;
  url: string;
  publishedTime?: string | null;
  author: string;
  tags?: string[];
  categories?: string[];
}

export interface CreateBlogData {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featured_image_url?: string;
  author_name?: string;
  author_email?: string;
  status?: BlogStatus;
  meta_title?: string;
  meta_description?: string;
  categories?: string[];
  tags?: string[];
  published_at?: string;
}

export interface UpdateBlogData extends Partial<CreateBlogData> {
  id: string;
}
