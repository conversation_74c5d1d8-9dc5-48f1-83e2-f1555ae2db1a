"use server";

import { createClient } from "@/utils/supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";

import { getCustomerPostImagePath } from "@/lib/utils/storage-paths";

export interface CustomerPostMediaUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * Upload and process image for customer post
 * Future-proof structure: {userId}/posts/{year}/{month}/{postId}/image_0_{timestamp}.webp
 */
export async function uploadCustomerPostImage(
  formData: FormData,
  postId: string,
  postCreatedAt?: string
): Promise<CustomerPostMediaUploadResult> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const userId = user.id;
  const imageFile = formData.get("imageFile") as File | null;

  if (!imageFile) {
    return { success: false, error: "No image file provided." };
  }

  // Validate file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
  if (!allowedTypes.includes(imageFile.type)) {
    return {
      success: false,
      error: "Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed."
    };
  }

  // Validate file size (15MB limit)
  const maxSize = 15 * 1024 * 1024; // 15MB
  if (imageFile.size > maxSize) {
    return {
      success: false,
      error: "File size exceeds 15MB limit."
    };
  }

  // Validate that the post belongs to the user
  const { data: existingPost, error: postError } = await supabase
    .from('customer_posts')
    .select('id, customer_id')
    .eq('id', postId)
    .eq('customer_id', userId)
    .single();

  if (postError || !existingPost) {
    return {
      success: false,
      error: "Post not found or you don't have permission to upload images for this post."
    };
  }

  try {
    // Create scalable path structure for billions of users
    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const bucketName = "customers";
    const imagePath = getCustomerPostImagePath(userId, postId, 0, timestamp, postCreatedAt);

    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    // Use admin client for storage operations to bypass RLS
    const adminSupabase = await createClient() as SupabaseClient<Database>;

    // Upload to Supabase Storage using admin client
    const { error: uploadError } = await adminSupabase.storage
      .from(bucketName)
      .upload(imagePath, fileBuffer, {
        contentType: imageFile.type, // Use original file type (already compressed)
        upsert: true
      });

    if (uploadError) {
      console.error("Customer Post Image Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError.message}`,
      };
    }

    // Get the public URL using admin client
    const { data: urlData } = adminSupabase.storage
      .from(bucketName)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    return {
      success: true,
      url: urlData.publicUrl,
    };

  } catch (error) {
    console.error("Error processing customer post image:", error);
    return {
      success: false,
      error: "Failed to process image. Please try a different image."
    };
  }
}
