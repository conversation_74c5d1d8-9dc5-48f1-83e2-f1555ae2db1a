"use server";

import { cancelSubscription as razorpayCancelSubscription } from "@/lib/razorpay/services/subscription";
import { SubscriptionAction, ActionDetails, ActionResponse } from "../types";
import {
  getUserAndProfile,
  revalidateSubscriptionPaths,
  createErrorResponse,
  createSuccessResponse
} from "../utils";

/**
 * Manage subscription with custom action
 * @param action The action to perform
 * @param actionDetails Additional action details
 * @returns The management result
 */
export async function manageSubscription(
  action: SubscriptionAction,
  actionDetails?: ActionDetails
): Promise<ActionResponse> {
  // Get user and profile
  const { user, profile, error } = await getUserAndProfile("has_active_subscription");

  if (error) {
    return createErrorResponse(error);
  }

  // Get subscription from payment_subscriptions table
  const supabase = await import("@/utils/supabase/server").then(mod => mod.createClient());

  // First check for active subscriptions
  const { data: activeSubscription, error: activeSubscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("razorpay_subscription_id, subscription_status")
    .eq("business_profile_id", user?.id || "")
    .eq("subscription_status", "active")
    .order("created_at", { ascending: false })
    .limit(1)
    .maybeSingle();

  if (activeSubscriptionError) {
    console.error("Error fetching active subscription:", activeSubscriptionError);
    return createErrorResponse("Error fetching subscription details");
  }

  // If no active subscription, check for authenticated subscriptions
  let subscription = activeSubscription;

  if (!subscription) {
    const { data: authSubscription, error: authSubscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("razorpay_subscription_id, subscription_status")
      .eq("business_profile_id", user?.id || "")
      .eq("subscription_status", "authenticated")
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    if (authSubscriptionError) {
      console.error("Error fetching authenticated subscription:", authSubscriptionError);
      return createErrorResponse("Error fetching subscription details");
    }

    subscription = authSubscription;
  }

  // If profile is null or user doesn't have a subscription ID, return error
  if (!profile || !subscription?.razorpay_subscription_id) {
    return createErrorResponse("User does not have an active or authenticated subscription");
  }

  // Handle different subscription actions
  let result: ActionResponse;

  if (action === "pause") {
    // Not implemented yet for Razorpay
    result = await createSuccessResponse({
      message: "Pause action not implemented for Razorpay yet"
    });
  } else if (action === "resume") {
    // Not implemented yet for Razorpay
    result = await createSuccessResponse({
      message: "Resume action not implemented for Razorpay yet"
    });
  } else if (action === "cancel") {
    // Check if this is an authenticated subscription first
    if (subscription.subscription_status === 'authenticated') {
      console.log("[SUBSCRIPTION_DEBUG] Handling cancellation for authenticated subscription in manageSubscription using pause endpoint");

      // For authenticated subscriptions, we'll use the pause endpoint which will cancel it
      // According to Razorpay docs: "If you pause a Subscription in the authenticated state, it goes to the cancelled state"
      const { pauseSubscription } = await import("@/lib/razorpay/services/subscription");
      const pauseResult = await pauseSubscription(subscription.razorpay_subscription_id, "now", true);

      if (!pauseResult.success) {
        console.error("[SUBSCRIPTION_DEBUG] Error pausing authenticated subscription:", pauseResult.error);

        // Even if the API call fails, we'll still update our database
        console.log("[SUBSCRIPTION_DEBUG] Proceeding with database update despite API failure");
      } else {
        console.log("[SUBSCRIPTION_DEBUG] Successfully paused/cancelled authenticated subscription");
      }

      // Update the subscription status in the database
      const { error: updateError } = await supabase
        .from("payment_subscriptions")
        .update({
          subscription_status: "cancelled",
          cancellation_requested_at: new Date().toISOString()
        })
        .eq("razorpay_subscription_id", subscription.razorpay_subscription_id);

      if (updateError) {
        console.error("Error updating subscription status:", updateError);
        result = await createErrorResponse("Failed to update subscription status");
      } else {
        // FIXED: Don't immediately revoke access for authenticated subscription cancellations
        // The webhook handler will update has_active_subscription when Razorpay confirms the cancellation
        // This prevents premature access revocation and maintains consistency with webhook-driven flow
        console.log("[SUBSCRIPTION_MANAGE] Skipping immediate has_active_subscription update - will be handled by webhook");

        result = await createSuccessResponse({
          message: "Your future subscription has been cancelled successfully."
        });
      }
    } else {
      // For active subscriptions, proceed with Razorpay API call
      const cancelResult = await razorpayCancelSubscription(
        subscription.razorpay_subscription_id,
        actionDetails?.cancelAtCycleEnd ? true : false
      );

      if (!cancelResult.success) {
        // For errors, return the error message
        result = await createSuccessResponse({
          message: typeof cancelResult.error === 'string'
            ? cancelResult.error
            : cancelResult.error
              ? JSON.stringify(cancelResult.error)
              : "Failed to cancel subscription"
        });
      } else {
      // FIXED: Only record cancellation request, let webhook handler update status
      // This maintains consistency with webhook-driven flow and prevents race conditions
      const { error: updateError } = await supabase
        .from("payment_subscriptions")
        .update({
          cancellation_requested_at: new Date().toISOString()
          // Removed direct subscription_status update - webhook handler will set the correct status
        })
        .eq("razorpay_subscription_id", subscription.razorpay_subscription_id);

      if (updateError) {
        console.error("Error updating subscription status:", updateError);
        // Continue anyway - the webhook handler will update the status
      }

      result = await createSuccessResponse({
        message: actionDetails?.cancelAtCycleEnd
          ? "Your subscription will be cancelled at the end of the current billing cycle."
          : "Your subscription has been cancelled immediately."
      });
      }
    }
  } else if (action === "change_plan") {
    // Not implemented here - use changePlan, changePlanWithManage, or switchAuthenticatedSubscription instead
    result = await createSuccessResponse({
      message: "Please use the appropriate function to change your subscription plan."
    });
  } else {
    result = await createSuccessResponse({
      message: `Unsupported action: ${action}`
    });
  }

  // Revalidate paths
  revalidateSubscriptionPaths();

  return result;
}
