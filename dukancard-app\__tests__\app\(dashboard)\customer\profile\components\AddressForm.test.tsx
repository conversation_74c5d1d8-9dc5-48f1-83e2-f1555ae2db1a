import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import AddressForm from '../../../../../../app/(dashboard)/customer/profile/components/AddressForm';
import { updateCustomerAddress } from '@/lib/actions/customer/profileActions';
import { useTheme } from '@/src/hooks/useTheme';
import { Alert } from 'react-native';

// Mock external modules and components
jest.mock('@/lib/actions/customer/profileActions');
jest.mock('@/src/hooks/useTheme');
jest.mock('react-native', () => {
  const ReactNative = jest.requireActual('react-native');
  return {
    ...ReactNative,
    Alert: {
      alert: jest.fn(),
    },
  };
});

describe('AddressForm', () => {
  const mockTheme = {
    spacing: { xs: 4, sm: 8, md: 16, lg: 24, xl: 32 },
    typography: { fontSize: { xs: 10, sm: 12, base: 16, lg: 20 } },
    colors: {
      textPrimary: '#000',
      textSecondary: '#666',
      primary: '#D4AF37',
      error: '#ff0000',
      border: '#ccc',
      card: '#f0f0f0',
      muted: '#eee',
    },
    borderRadius: { md: 8, lg: 12 },
  };

  beforeEach(() => {
    (useTheme as jest.Mock).mockReturnValue(mockTheme);
    (updateCustomerAddress as jest.Mock).mockResolvedValue({ success: true });
    (Alert.alert as jest.Mock).mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with initial data', () => {
    const initialData = {
      address: '123 Main St',
      pincode: '123456',
      city: 'Test City',
      state: 'Test State',
      locality: 'Test Locality',
    };
    const { getByDisplayValue } = render(<AddressForm initialData={initialData} />);

    expect(getByDisplayValue('123 Main St')).toBeDefined();
    expect(getByDisplayValue('123456')).toBeDefined();
    expect(getByDisplayValue('Test City')).toBeDefined();
    expect(getByDisplayValue('Test State')).toBeDefined();
    expect(getByDisplayValue('Test Locality')).toBeDefined();
  });

  it('renders correctly without initial data', () => {
    const { getByPlaceholderText } = render(<AddressForm />);

    expect(getByPlaceholderText('e.g., House/Flat No., Street Name')).toBeDefined();
    expect(getByPlaceholderText('e.g., 751001')).toBeDefined();
    expect(getByPlaceholderText('Auto-filled from Pincode')).toBeDefined();
    expect(getByPlaceholderText('Enter your locality')).toBeDefined();
  });

  it('shows validation errors for empty required fields on submit', async () => {
    const { getByText, getByPlaceholderText } = render(<AddressForm />);
    fireEvent.press(getByText('Update Address'));

    await waitFor(() => {
      expect(getByText('Pincode is required')).toBeDefined();
      expect(getByText('City is required')).toBeDefined();
      expect(getByText('State is required')).toBeDefined();
      expect(getByText('Locality is required')).toBeDefined();
    });
  });

  it('shows validation error for invalid pincode format', async () => {
    const { getByText, getByPlaceholderText } = render(<AddressForm />);
    fireEvent.changeText(getByPlaceholderText('e.g., 751001'), '123');
    fireEvent.press(getByText('Update Address'));

    await waitFor(() => {
      expect(getByText('Must be a valid 6-digit pincode')).toBeDefined();
    });
  });

  it('shows validation error for address exceeding 100 characters', async () => {
    const longAddress = 'a'.repeat(101);
    const { getByText, getByPlaceholderText } = render(<AddressForm />);
    fireEvent.changeText(getByPlaceholderText('e.g., House/Flat No., Street Name'), longAddress);
    fireEvent.press(getByText('Update Address'));

    await waitFor(() => {
      expect(getByText('Address cannot exceed 100 characters')).toBeDefined();
    });
  });

  it('calls updateCustomerAddress on valid form submission', async () => {
    const { getByText, getByPlaceholderText, getAllByPlaceholderText } = render(<AddressForm />);

    fireEvent.changeText(getByPlaceholderText('e.g., 751001'), '123456');
    fireEvent.changeText(screen.getAllByPlaceholderText('Auto-filled from Pincode')[0], 'Test City');
    fireEvent.changeText(screen.getAllByPlaceholderText('Auto-filled from Pincode')[1], 'Test State');
    fireEvent.changeText(getByPlaceholderText('Enter your locality'), 'Test Locality');
    fireEvent.changeText(getByPlaceholderText('e.g., House/Flat No., Street Name'), '123 Main St');

    fireEvent.press(getByText('Update Address'));

    await waitFor(() => {
      expect(updateCustomerAddress).toHaveBeenCalledTimes(1);
      const formData = (updateCustomerAddress as jest.Mock).mock.calls[0][1];
      expect(formData.get('address')).toBe('123 Main St');
      expect(formData.get('pincode')).toBe('123456');
      expect(formData.get('city')).toBe('Test City');
      expect(formData.get('state')).toBe('Test State');
      expect(formData.get('locality')).toBe('Test Locality');
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Address updated successfully!');
    });
  });

  it('shows error alert if updateCustomerAddress fails', async () => {
    (updateCustomerAddress as jest.Mock).mockResolvedValue({ success: false, message: 'Failed to update' });
    const { getByText, getByPlaceholderText } = render(<AddressForm />);

    fireEvent.changeText(getByPlaceholderText('e.g., 751001'), '123456');
    fireEvent.changeText(screen.getAllByPlaceholderText('Auto-filled from Pincode')[0], 'Test City');
    fireEvent.changeText(screen.getAllByPlaceholderText('Auto-filled from Pincode')[1], 'Test State');
    fireEvent.changeText(getByPlaceholderText('Enter your locality'), 'Test Locality');

    fireEvent.press(getByText('Update Address'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to update');
    });
  });

  it('shows generic error alert on unexpected error during submission', async () => {
    (updateCustomerAddress as jest.Mock).mockRejectedValue(new Error('Network error'));
    const { getByText, getByPlaceholderText } = render(<AddressForm />);

    fireEvent.changeText(getByPlaceholderText('e.g., 751001'), '123456');
    fireEvent.changeText(screen.getAllByPlaceholderText('Auto-filled from Pincode')[0], 'Test City');
    fireEvent.changeText(screen.getAllByPlaceholderText('Auto-filled from Pincode')[1], 'Test State');
    fireEvent.changeText(getByPlaceholderText('Enter your locality'), 'Test Locality');

    fireEvent.press(getByText('Update Address'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'An unexpected error occurred. Please try again.');
    });
  });
});
