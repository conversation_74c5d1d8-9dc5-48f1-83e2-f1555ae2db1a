import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ResultsList } from '@/src/components/discovery/ResultsList';
import { useTheme } from '@/src/hooks/useTheme';

// Mock dependencies
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#C29D5B',
    },
  }),
}));

describe('ResultsList', () => {
  const mockBusinesses = [
    { id: '1', business_name: 'Business 1' },
    { id: '2', business_name: 'Business 2' },
  ];
  const mockProducts = [
    { id: '3', name: 'Product 1' },
    { id: '4', name: 'Product 2' },
  ];

  it('renders a list of business cards when viewType is "cards"', () => {
    const { getByText } = render(
      <ResultsList
        viewType="cards"
        businesses={mockBusinesses as any}
        products={[]}
        isLoading={false}
        isLoadingMore={false}
        hasMore={false}
        onLoadMore={() => {}}
        onRefresh={() => {}}
        onBusinessPress={() => {}}
        onProductPress={() => {}}
      />
    );

    expect(getByText('Business 1')).toBeTruthy();
    expect(getByText('Business 2')).toBeTruthy();
  });

  it('renders a grid of product cards when viewType is "products"', () => {
    const { getByText } = render(
      <ResultsList
        viewType="products"
        businesses={[]}
        products={mockProducts as any}
        isLoading={false}
        isLoadingMore={false}
        hasMore={false}
        onLoadMore={() => {}}
        onRefresh={() => {}}
        onBusinessPress={() => {}}
        onProductPress={() => {}}
      />
    );

    expect(getByText('Product 1')).toBeTruthy();
    expect(getByText('Product 2')).toBeTruthy();
  });

  it('displays a loading skeleton when isLoading is true', () => {
    const { getByTestId } = render(
      <ResultsList
        viewType="cards"
        businesses={[]}
        products={[]}
        isLoading={true}
        isLoadingMore={false}
        hasMore={false}
        onLoadMore={() => {}}
        onRefresh={() => {}}
        onBusinessPress={() => {}}
        onProductPress={() => {}}
      />
    );

    expect(getByTestId('loading-skeleton')).toBeTruthy();
  });

  it('calls onLoadMore when the end of the list is reached', () => {
    const onLoadMore = jest.fn();
    const { getByTestId } = render(
      <ResultsList
        viewType="cards"
        businesses={mockBusinesses as any}
        products={[]}
        isLoading={false}
        isLoadingMore={false}
        hasMore={true}
        onLoadMore={onLoadMore}
        onRefresh={() => {}}
        onBusinessPress={() => {}}
        onProductPress={() => {}}
      />
    );

    const flatList = getByTestId('results-flatlist');
    fireEvent.scroll(flatList, { nativeEvent: { contentOffset: { y: 500 }, contentSize: { height: 500, width: 300 }, layoutMeasurement: { height: 200, width: 300 } } });


    expect(onLoadMore).toHaveBeenCalled();
  });
});
