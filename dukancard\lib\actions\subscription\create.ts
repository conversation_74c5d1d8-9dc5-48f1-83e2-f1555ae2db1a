"use server";

import { createSubscription as createRazorpaySubscription } from "@/lib/razorpay/services/subscription";
import { PlanCycle, PlanId, ActionResponse } from "./types";
import {
  getUserAndProfile,
  isUserOnTrial,
  revalidateSubscriptionPaths,
  createErrorResponse,
  createSuccessResponse,
  // doesSubscriptionBelongToUser is not used in this file
} from "./utils";
import { getSubscriptionRazorpayPlanId } from "@/lib/config/plans";

/**
 * Helper function to create a new subscription
 * @param userId The user ID
 * @param planId The plan ID
 * @param planCycle The plan cycle
 * @param trialEndDate Optional trial end date
 * @returns The subscription result
 */
async function createNewSubscription(
  userId: string,
  planId: PlanId,
  planCycle: PlanCycle,
  trialEndDate?: Date
): Promise<ActionResponse> {
  // Get the Razorpay plan ID based on the plan type and cycle
  let razorpayPlanId: string;
  try {
    razorpayPlanId = getSubscriptionRazorpayPlanId(planId, planCycle);
  } catch (error) {
    return createErrorResponse(
      error instanceof Error ? error.message : "Invalid plan selected"
    );
  }

  // Get user profile for customer creation
  const supabase = await import("@/utils/supabase/server").then((mod) =>
    mod.createClient()
  );
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return createErrorResponse("User not authenticated");
  }

  const { data: profile, error: profileError } = await supabase
    .from("business_profiles")
    .select("business_name, contact_email, phone, trial_end_date")
    .eq("id", userId)
    .single();

  if (profileError) {
    console.error("Error fetching profile:", profileError);
    return createErrorResponse("Error fetching user profile");
  }

  // Create or find Razorpay customer
  let customerId = null;
  const customerEmail = profile.contact_email || user.email || "";

  if (customerEmail) {
    const { findCustomerByEmail, createCustomer } = await import(
      "@/lib/razorpay/services/customer"
    );

    // Check if customer already exists
    const existingCustomer = await findCustomerByEmail(customerEmail);

    if (existingCustomer.success && existingCustomer.data) {
      customerId = existingCustomer.data.id;
    } else {
      // Create new customer
      const newCustomer = await createCustomer({
        name:
          profile.business_name || user.user_metadata?.full_name || "Customer",
        email: customerEmail,
        contact: profile.phone || "",
        notes: {
          user_id: userId,
          business_name: profile.business_name || "",
        },
      });

      if (newCustomer.success && newCustomer.data) {
        customerId = newCustomer.data.id;
      } else {
        console.error("Failed to create customer:", newCustomer.error);
        // Continue without customer ID
      }
    }
  }

  // Check if we need to set a future start date for trial users
  const subscriptionParams = {
    plan_id: razorpayPlanId,
    total_count: planCycle === "monthly" ? 120 : 10, // 10 years (120 months for monthly, 10 years for yearly)
    customer_notify: true,
    notes: {
      business_profile_id: userId,
      plan_type: planId,
      plan_cycle: planCycle,
    },
    start_at: undefined as number | undefined,
    ...(customerId && { customer_id: customerId }),
  };

  // If user is on trial, set start_at to trial end date
  if (trialEndDate && trialEndDate > new Date()) {
    // Convert trial end date to Unix timestamp (seconds)
    const startAt = Math.floor(trialEndDate.getTime() / 1000);
    subscriptionParams.start_at = startAt;
  }

  // Create the subscription in Razorpay
  const createResult = await createRazorpaySubscription(subscriptionParams);

  if (!createResult.success) {
    console.error(`Error creating subscription: ${createResult.error}`);
    return createErrorResponse(String(createResult.error || "Unknown error"));
  }

  // We'll no longer create a subscription record here
  // The record will be created or updated by the webhook handler
  // when the subscription is authorized or activated

  return createSuccessResponse({
    ...createResult.data,
    requires_authorization: true,
    message:
      "Please complete payment authorization to activate your subscription.",
  });
}

/**
 * Create a new subscription
 * @param planId The plan ID
 * @param planCycle The plan cycle
 * @returns The subscription result
 */
export async function createSubscription(
  planId: PlanId,
  planCycle: PlanCycle
): Promise<ActionResponse> {
  // Get user and profile
  const { user, profile, error } = await getUserAndProfile(
    "has_active_subscription, trial_end_date"
  );

  if (error) {
    return createErrorResponse(error);
  }

  // Check if user has an active subscription
  if (profile && profile.has_active_subscription) {
    // Check if the user has a halted subscription that needs to be resumed
    const supabase = await import("@/utils/supabase/server").then((mod) =>
      mod.createClient()
    );
    const { data: haltedSubscription, error: subscriptionError } =
      await supabase
        .from("payment_subscriptions")
        .select("subscription_status")
        .eq("business_profile_id", user?.id || "")
        .eq("subscription_status", "halted")
        .maybeSingle();

    if (subscriptionError) {
      console.error(
        "Error checking for halted subscription:",
        subscriptionError
      );
    }

    // If the user has a halted subscription, tell them to resume it first
    if (haltedSubscription) {
      return createErrorResponse(
        "You have a paused subscription. Please resume your existing subscription before creating a new one."
      );
    }

    // Otherwise, they have an active subscription
    return createErrorResponse("User already has an active subscription");
  }

  // Check if the user is in trial period
  const trialEndDate = profile?.trial_end_date
    ? new Date(profile.trial_end_date)
    : null;
  const isInTrial = profile ? isUserOnTrial(profile) : false;

  // Create the subscription
  if (!user) {
    return createErrorResponse("User not found");
  }

  const result = await createNewSubscription(
    user.id,
    planId,
    planCycle,
    isInTrial && trialEndDate ? trialEndDate : undefined
  );

  // Revalidate paths
  revalidateSubscriptionPaths();

  // If the subscription was created successfully, ensure it includes the requires_authorization flag
  if (result.success && result.data) {
    // Add requires_authorization flag if it doesn't exist
    if (!("requires_authorization" in result.data)) {
      return createSuccessResponse({
        ...result.data,
        requires_authorization: true,
        message:
          "Please complete payment authorization to activate your subscription.",
      });
    }
    return result;
  }

  // Handle specific error cases
  if (!result.success && result.error) {
    console.error(`Error creating subscription: ${result.error}`);

    // Check if the error contains information about an existing subscription
    if (
      typeof result.error === "string" &&
      (result.error.includes("subscription_already_exists") ||
        result.error.includes("SUBSCRIPTION_ALREADY_EXIST"))
    ) {
      // If we have data despite the error, return it as a success
      if (result.data) {
        return createSuccessResponse({
          ...result.data,
          requires_authorization: true,
          message:
            "Please complete payment authorization for your existing subscription.",
        });
      }
    }
  }

  return result;
}

/**
 * Create new subscription and cancel old one after activation (for UPI/eMandate)
 * @param subscriptionId The current subscription ID
 * @param newPlanId The new plan ID
 * @param newPlanCycle The new plan cycle
 * @returns The subscription result
 */
export async function cancelAndCreateSubscription(
  subscriptionId: string,
  newPlanId: PlanId,
  newPlanCycle: PlanCycle
): Promise<ActionResponse> {
  // Get user
  const { user, error } = await getUserAndProfile();

  if (error) {
    return createErrorResponse(error);
  }

  // Ensure user exists
  if (!user) {
    return createErrorResponse("User not found");
  }

  // Get subscription from payment_subscriptions table
  const supabase = await import("@/utils/supabase/server").then((mod) =>
    mod.createClient()
  );
  const { data: subscription, error: subscriptionError } = await supabase
    .from("payment_subscriptions")
    .select("razorpay_subscription_id, subscription_status")
    .eq("business_profile_id", user.id)
    .eq("razorpay_subscription_id", subscriptionId)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription:", subscriptionError);
    return createErrorResponse("Error fetching subscription details");
  }

  // Verify that the subscription belongs to the user
  if (!subscription?.razorpay_subscription_id) {
    return createErrorResponse("Subscription does not belong to user");
  }

  // Get user profile for customer creation and trial check
  const { data: profile, error: profileError } = await supabase
    .from("business_profiles")
    .select("business_name, contact_email, phone, trial_end_date")
    .eq("id", user.id)
    .single();

  if (profileError) {
    console.error("Error fetching profile:", profileError);
    return createErrorResponse("Error fetching user profile");
  }

  // Check if user is on trial
  const trialEndDate = profile?.trial_end_date
    ? new Date(profile.trial_end_date)
    : null;
  const isInTrial = trialEndDate && trialEndDate > new Date();

  // Create or find Razorpay customer (MISSING LOGIC - FIXED)
  let customerId = null;
  const customerEmail = profile.contact_email || user.email || "";

  if (customerEmail) {
    const { findCustomerByEmail, createCustomer } = await import(
      "@/lib/razorpay/services/customer"
    );

    // Check if customer already exists
    const existingCustomer = await findCustomerByEmail(customerEmail);

    if (existingCustomer.success && existingCustomer.data) {
      customerId = existingCustomer.data.id;
      console.log("[CANCEL_AND_CREATE] Found existing customer:", customerId);
    } else {
      // Create new customer
      const newCustomer = await createCustomer({
        name: profile.business_name || "Customer",
        email: customerEmail,
        contact: profile.phone || "",
        notes: {
          user_id: user.id,
          business_name: profile.business_name || "",
        },
      });

      if (newCustomer.success && newCustomer.data) {
        customerId = newCustomer.data.id;
        console.log("[CANCEL_AND_CREATE] Created new customer:", customerId);
      } else {
        console.error("Failed to create customer:", newCustomer.error);
        // Continue without customer ID
      }
    }
  }

  // For all users, we follow the improved flow:
  // 1. Create the new subscription first
  // 2. Wait for it to become active (via webhook)
  // 3. Only then cancel the old subscription

  // Get the Razorpay plan ID based on the plan type and cycle
  let razorpayPlanId: string;
  try {
    razorpayPlanId = getSubscriptionRazorpayPlanId(newPlanId, newPlanCycle);
  } catch (error) {
    return createErrorResponse(
      error instanceof Error ? error.message : "Invalid plan selected"
    );
  }

  // Prepare subscription parameters
  const subscriptionParams = {
    plan_id: razorpayPlanId,
    total_count: newPlanCycle === "monthly" ? 120 : 10, // 10 years (120 months for monthly, 10 years for yearly)
    customer_notify: true,
    notes: {
      business_profile_id: user.id,
      old_subscription_id: subscription.razorpay_subscription_id,
      plan_type: newPlanId,
      plan_cycle: newPlanCycle,
      is_plan_switch: "true", // Critical flag for webhook handler to detect plan switches
    },
    start_at: undefined as number | undefined,
    ...(customerId && { customer_id: customerId }),
  };

  // If user is on trial, set start_at to trial end date
  if (isInTrial && trialEndDate) {
    // Convert trial end date to Unix timestamp (seconds)
    const startAt = Math.floor(trialEndDate.getTime() / 1000);
    subscriptionParams.start_at = startAt;
  }

  // Create a new subscription with the new plan
  const createResult = await createRazorpaySubscription(subscriptionParams);

  if (!createResult.success || !createResult.data?.id) {
    console.error("Error creating new subscription:", createResult.error);
    return createErrorResponse("Could not create new subscription");
  }

  // We'll no longer create a subscription record here
  // The record will be created or updated by the webhook handler
  // when the subscription is authorized or activated

  // Revalidate paths
  revalidateSubscriptionPaths();

  // Return the subscription details for payment authorization
  // We'll only cancel the old subscription after the new one is active
  // This happens via the webhook handler for subscription.activated
  return createSuccessResponse({
    message:
      "New subscription created. Please complete the payment authorization. Your previous subscription will be cancelled automatically after successful activation.",
    subscription_id: createResult.data.id,
    // Use short_url for Razorpay
    short_url: createResult.data.short_url,
    requires_authorization: true,
  });
}
