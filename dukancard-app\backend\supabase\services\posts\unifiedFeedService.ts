import { supabase } from "@/lib/supabase";
import { Tables } from "../../../../src/types/supabase";
import { PostgrestResponse } from "@supabase/supabase-js";

export type BusinessProfiles = Tables<"business_profiles">;
export type CustomerProfiles = Tables<"customer_profiles">;

export class UnifiedFeedService {
  static async getUnifiedPosts(query: any, from: number, to: number) {
    return await query
      .order("created_at", { ascending: false })
      .range(from, to);
  }

  static async getUserSubscriptions(
    userId: string
  ): Promise<PostgrestResponse<{ business_profile_id: string }>> {
    return await supabase
      .from("subscriptions")
      .select("business_profile_id")
      .eq("user_id", userId);
  }

  static async getCustomerProfile(
    userId: string
  ): Promise<PostgrestResponse<CustomerProfiles>> {
    return await supabase
      .from("customer_profiles_public")
      .select("city_slug, state_slug, locality_slug")
      .eq("id", userId)
      .single();
  }

  static async getBusinessProfile(
    userId: string
  ): Promise<PostgrestResponse<BusinessProfiles>> {
    return await supabase
      .from("business_profiles")
      .select("city_slug, state_slug, locality_slug, pincode")
      .eq("id", userId)
      .single();
  }
}
