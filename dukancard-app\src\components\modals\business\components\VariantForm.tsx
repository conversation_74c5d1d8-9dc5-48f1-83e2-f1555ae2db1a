import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Switch,
  Image,
} from "react-native";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Package,
  Camera,
  X,
  Plus,
  Trash2,
  ChevronDown,
  ArrowLeft,
  Tag,
  Settings,
  DollarSign,
  ImageIcon,
  ToggleLeft,
} from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { useToast } from "@/src/components/ui/Toast";
import { createManageProductsModalStyles } from "@/styles/modals/business/manage-products-modal";
import { Tables } from "@dukancard-types/supabase";
import { Json } from "@/src/types/supabase";
import ImagePickerBottomSheet, {
  ImagePickerBottomSheetRef,
} from "@/src/components/pickers/ImagePickerBottomSheet";
import VariantTypeBottomSheet, {
  VariantTypeBottomSheetRef,
} from "@/src/components/pickers/VariantTypeBottomSheet";
import ColorPickerBottomSheet, {
  ColorPickerBottomSheetRef,
} from "@/src/components/pickers/ColorPickerBottomSheet";
import {
  compressImageUltraAggressive,
  toBase64DataUrl,
} from "@/src/utils/imageCompression";
// Remove unused toast import - using useToast hook instead
import {
  getAllVariantTypes,
  getPredefinedOptionsForType,
  PredefinedVariantOption,
} from "@/src/constants/predefinedVariants";

// Zod schema for variant form
const literalSchema = z.union([z.string(), z.number(), z.boolean(), z.null()]);
type Literal = z.infer<typeof literalSchema>;
type Json = Literal | { [key: string]: Json } | Json[];
const JsonSchema: z.ZodType<Json> = z.lazy(() =>
  z.union([literalSchema, z.array(JsonSchema), z.record(JsonSchema)]),
);

// Zod schema for variant form
const variantFormSchema = z.object({
  variant_name: z.string().min(1, "Variant name is required"),
  variant_values: z
    .record(z.string(), JsonSchema)
    .refine(
      (values) => Object.keys(values).length > 0,
      "At least one variant property is required",
    ),
  base_price: z.number().min(0).optional().nullable(),
  discounted_price: z.number().min(0).optional().nullable(),
  is_available: z.boolean().optional().default(true),
  images: z.array(z.string()).optional().default([]),
  featured_image_index: z.number().int().min(0).optional().default(0),
});

type VariantFormData = z.infer<typeof variantFormSchema>;

interface VariantType {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  is_predefined: boolean;
  sort_order: number;
}

interface VariantFormProps {
  variant?: Tables<'product_variants'> | null;
  onSubmit: (data: VariantFormData) => Promise<void>;
  onCancel: () => void;
  loading: boolean;
}

export default function VariantForm({
  variant,
  onSubmit,
  onCancel,
  loading,
}: VariantFormProps) {
  const theme = useTheme();
  const toast = useToast();
  const styles = createManageProductsModalStyles(theme);

  // Refs for bottom sheets
  const imagePickerRef = useRef<ImagePickerBottomSheetRef>(null);
  const variantTypePickerRef = useRef<VariantTypeBottomSheetRef>(null);
  const colorPickerRef = useRef<ColorPickerBottomSheetRef>(null);

  // State management
  const [images, setImages] = useState<string[]>(variant?.images || []);
  const [featuredImageIndex, setFeaturedImageIndex] = useState(
    variant?.featured_image_index || 0,
  );
  const [variantValues, setVariantValues] = useState<Record<string, Json>>(
    variant?.variant_values || {},
  );
  const [imageProcessing, setImageProcessing] = useState(false);
  const [currentEditingType, setCurrentEditingType] = useState<string>("");

  // Form setup
  const formMethods = useForm<any>({
    resolver: zodResolver(variantFormSchema),
    defaultValues: {
      variant_name: variant?.variant_name || "",
      variant_values: variant?.variant_values || {},
      base_price: variant?.base_price || undefined,
      discounted_price: variant?.discounted_price || undefined,
      is_available: variant?.is_available ?? true,
      images: variant?.images || [],
      featured_image_index: variant?.featured_image_index || 0,
    },
    mode: "onChange",
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = formMethods;

  // Update form when variant values change
  useEffect(() => {
    setValue("variant_values", variantValues);
  }, [variantValues, setValue]);

  // Update form when images change
  useEffect(() => {
    setValue("images", images);
    setValue("featured_image_index", featuredImageIndex);
  }, [images, featuredImageIndex, setValue]);

  // Image handling
  const handleImageSelect = async (imageUris: string[]) => {
    if (imageUris.length === 0) return;

    setImageProcessing(true);
    try {
      const compressedImages: string[] = [];

      for (const uri of imageUris) {
        const compressedResult = await compressImageUltraAggressive(uri);
        if (compressedResult.base64) {
          const base64 = toBase64DataUrl(compressedResult.base64);
          compressedImages.push(base64);
        }
      }

      setImages((prev) => [...prev, ...compressedImages]);
      toast.success(
        "Success",
        `${compressedImages.length} image(s) added successfully`,
      );
    } catch (error) {
      console.error("Error processing images:", error);
      toast.error("Error", "Failed to process images");
    } finally {
      setImageProcessing(false);
    }
  };

  const removeImage = (index: number) => {
    setImages((prev) => prev.filter((_, i) => i !== index));
    if (featuredImageIndex >= index && featuredImageIndex > 0) {
      setFeaturedImageIndex((prev: number) => prev - 1);
    }
  };

  const setAsFeatured = (index: number) => {
    setFeaturedImageIndex(index);
  };

  // Variant type/value management
  const addVariantValue = () => {
    if (Object.keys(variantValues).length >= 5) {
      toast.error("Error", "Maximum of 5 variant types allowed per product");
      return;
    }

    // Find the first unused predefined type
    const usedTypes = Object.keys(variantValues).map((k) => k.toLowerCase());
    const availableType = getAllVariantTypes().find(
      (type) => !usedTypes.includes(type.name.toLowerCase()),
    );

    const newKey = availableType
      ? availableType.name
      : `type_${Object.keys(variantValues).length + 1}`;
    setVariantValues((prev) => ({ ...prev, [newKey]: "" }));
  };

  const removeVariantValue = (key: string) => {
    setVariantValues((prev) => {
      const newValues = { ...prev };
      delete newValues[key];
      return newValues;
    });
  };

  const updateVariantType = (oldKey: string, newType: VariantType) => {
    if (oldKey === newType.name) return;

    // Check for duplicate keys (case-insensitive)
    const existingKeys = Object.keys(variantValues)
      .filter((k) => k !== oldKey)
      .map((k) => k.trim().toLowerCase());

    if (existingKeys.includes(newType.name.toLowerCase())) {
      toast.error("Error", "Variant type already exists");
      return;
    }

    setVariantValues((prev) => {
      const newValues = { ...prev };
      const value = newValues[oldKey];
      delete newValues[oldKey];
      newValues[newType.name] = value;
      return newValues;
    });
  };

  const updateVariantValue = (key: string, value: string) => {
    const trimmedValue = value.trim();
    if (trimmedValue.length > 50) {
      toast.error("Error", "Variant value cannot exceed 50 characters");
      return;
    }

    setVariantValues((prev) => ({ ...prev, [key]: trimmedValue }));
  };

  const handleVariantTypeSelect = (type: VariantType) => {
    updateVariantType(currentEditingType, type);
    setCurrentEditingType("");
  };

  const handleColorSelect = (color: PredefinedVariantOption) => {
    updateVariantValue(currentEditingType, color.value);
    setCurrentEditingType("");
  };

  const openVariantTypePicker = (key: string) => {
    setCurrentEditingType(key);
    variantTypePickerRef.current?.open();
  };

  const openColorPicker = (key: string) => {
    setCurrentEditingType(key);
    colorPickerRef.current?.open();
  };

  const onFormSubmit = async (data: VariantFormData) => {
    try {
      const submitData = {
        ...data,
        variant_values: variantValues,
        images,
        featured_image_index: featuredImageIndex,
      };
      await onSubmit(submitData);
    } catch (error) {
      console.error("Error submitting variant form:", error);
      toast.error("Error", "Failed to save variant");
    }
  };

  return (
    <View style={styles.variantModalContainer}>
      {/* Enhanced Header with Back Navigation */}
      <View style={styles.variantModalHeader}>
        <TouchableOpacity onPress={onCancel} style={styles.backButton}>
          <ArrowLeft size={24} color={theme.isDark ? "#FFF" : "#000"} />
        </TouchableOpacity>
        <Text style={styles.variantModalTitle}>
          {variant ? "Edit Variant" : "Add Variant"}
        </Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Scrollable Form Content */}
      <ScrollView
        style={styles.variantScrollView}
        contentContainerStyle={styles.variantScrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.variantFormContainer}>
          {/* Enhanced Variant Details Section */}
          <View style={styles.variantSection}>
            <View style={styles.variantSectionHeader}>
              <View style={styles.variantSectionIconContainer}>
                <Tag size={20} color={theme.colors.primary} />
              </View>
              <View style={styles.variantSectionTitleContainer}>
                <Text style={styles.variantSectionTitle}>Variant Details</Text>
                <Text style={styles.variantSectionDescription}>
                  Basic information about this product variant
                </Text>
              </View>
            </View>

            <View style={styles.variantInputContainer}>
              <Text style={styles.variantInputLabel}>
                Variant Name <Text style={styles.requiredIndicator}>*</Text>
              </Text> 
              <Controller
                control={control}
                name="variant_name"
                render={({ field: { onChange, value } }) => (
                  <View style={styles.variantInputWrapper}>
                    <TextInput
                      style={[
                        styles.variantTextInput,
                        errors.variant_name && styles.variantInputError,
                      ]}
                      placeholder="e.g., Red Large, 64GB Blue, Cotton Medium"
                      placeholderTextColor={theme.colors.mutedForeground}
                      value={value}
                      onChangeText={onChange}
                      editable={!loading}
                      maxLength={100}
                    />
                    <Text style={styles.variantCharacterCount}>
                      {value?.length || 0}/100
                    </Text>
                  </View>
                )}
              />
              {errors.variant_name && (
                <Text style={styles.variantErrorText}>
                  {String(errors.variant_name.message)}
                </Text>
              )}
              <Text style={styles.variantInputHint}>
                A descriptive name that combines all variant properties (e.g.,
                &quot;Red Large T-Shirt&quot;)
              </Text>
            </View>
          </View>

          {/* Enhanced Variant Properties Section */}
          <View style={styles.variantSection}>
            <View style={styles.variantSectionHeader}>
              <View style={styles.variantSectionIconContainer}>
                <Settings size={20} color={theme.colors.primary} />
              </View>
              <View style={styles.variantSectionTitleContainer}>
                <Text style={styles.variantSectionTitle}>
                  Variant Properties
                </Text>
                <Text style={styles.variantSectionDescription}>
                  Define the characteristics that make this variant unique
                </Text>
              </View>
            </View>

            <TouchableOpacity
              style={[
                styles.enhancedAddPropertyButton,
                (Object.keys(variantValues).length >= 5 || loading) &&
                  styles.addPropertyButtonDisabled,
              ]}
              onPress={addVariantValue}
              disabled={Object.keys(variantValues).length >= 5 || loading}
              activeOpacity={0.7}
            >
              <View style={styles.addPropertyButtonContent}>
                <View style={styles.addPropertyIconContainer}>
                  <Plus
                    size={20}
                    color={
                      Object.keys(variantValues).length >= 5 || loading
                        ? theme.colors.mutedForeground
                        : theme.colors.primary
                    }
                  />
                </View>
                <View style={styles.addPropertyTextContainer}>
                  <Text
                    style={[
                      styles.enhancedAddPropertyButtonText,
                      (Object.keys(variantValues).length >= 5 || loading) &&
                        styles.addPropertyButtonTextDisabled,
                    ]}
                  >
                    Add Property
                  </Text>
                  <Text
                    style={[
                      styles.addPropertyButtonSubtext,
                      (Object.keys(variantValues).length >= 5 || loading) &&
                        styles.addPropertyButtonTextDisabled,
                    ]}
                  >
                    {Object.keys(variantValues).length}/5 properties • Size,
                    Color, Material, etc.
                  </Text>
                </View>
              </View>
            </TouchableOpacity>

            <View style={styles.variantsList}>
              {Object.entries(variantValues).map(([key, value]) => {
                const variantType = getAllVariantTypes().find(
                  (type) => type.name === key,
                );
                const isColorType = key.toLowerCase() === "color";
                const predefinedValues = getPredefinedOptionsForType(key);

                return (
                  <View key={key} style={styles.variantRow}>
                    {/* Variant Type Selector */}
                    <TouchableOpacity
                      style={[styles.variantTypeButton, { flex: 1 }]}
                      onPress={() => openVariantTypePicker(key)}
                      disabled={loading}
                    >
                      <Text style={styles.variantTypeButtonText}>
                        {variantType?.display_name || key}
                      </Text>
                      <ChevronDown
                        size={16}
                        color={theme.colors.mutedForeground}
                      />
                    </TouchableOpacity>

                    {/* Variant Value Input/Selector */}
                    {isColorType && predefinedValues.length > 0 ? (
                      <TouchableOpacity
                        style={[styles.variantValueButton, { flex: 1 }]}
                        onPress={() => openColorPicker(key)}
                        disabled={loading}
                      >
                        <View style={styles.colorValueDisplay}>
                          {value &&
                            predefinedValues.find((opt) => opt.value === value)
                              ?.color_code && (
                              <View
                                style={[
                                  styles.colorSwatch,
                                  {
                                    backgroundColor: predefinedValues.find(
                                      (opt) => opt.value === value,
                                    )?.color_code,
                                  },
                                ]}
                              />
                            )}
                          <Text style={styles.variantValueButtonText}>
                            {value
                              ? predefinedValues.find(
                                  (opt) => opt.value === value,
                                )?.display_value || value
                              : "Select color..."}
                          </Text>
                        </View>
                        <ChevronDown
                          size={16}
                          color={theme.colors.mutedForeground}
                        />
                      </TouchableOpacity>
                    ) : (
                      <TextInput
                        style={[styles.variantValueInput, { flex: 1 }]}
                        placeholder={`Enter ${key.toLowerCase()}...`}
                        value={String(value)}
                        onChangeText={(text) => updateVariantValue(key, text)}
                        editable={!loading}
                      />
                    )}

                    {/* Remove Button */}
                    <TouchableOpacity
                      style={styles.removeButton}
                      onPress={() => removeVariantValue(key)}
                      disabled={loading}
                    >
                      <X size={16} color={theme.colors.destructive} />
                    </TouchableOpacity>
                  </View>
                );
              })}

              {Object.keys(variantValues).length === 0 && (
                <View style={styles.emptyState}>
                  <Text style={styles.emptyStateText}>
                    No variant properties added yet. Click &quot;Add
                    Property&quot; to start.
                  </Text>
                </View>
              )}
            </View>

            <Text style={styles.inputDescription}>
              Add up to 5 variant properties (e.g., color: red, size: large)
            </Text>
          </View>

          {/* Enhanced Pricing Section */}
          <View style={styles.variantSection}>
            <View style={styles.variantSectionHeader}>
              <View style={styles.variantSectionIconContainer}>
                <Text style={styles.currencyIcon}>₹</Text>
              </View>
              <View style={styles.variantSectionTitleContainer}>
                <Text style={styles.variantSectionTitle}>Variant Pricing</Text>
                <Text style={styles.variantSectionDescription}>
                  Set specific pricing for this variant (optional - inherits
                  from main product if not set)
                </Text>
              </View>
            </View>

            <View style={styles.priceRow}>
              <View style={styles.priceInput}>
                <Text style={styles.variantInputLabel}>Base Price</Text>
                <Controller
                  control={control}
                  name="base_price"
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      style={[
                        styles.textInput,
                        errors.base_price && styles.inputError,
                      ]}
                      placeholder="0.00"
                      value={value ? value.toString() : ""}
                      onChangeText={(text) =>
                        onChange(text ? parseFloat(text) : undefined)
                      }
                      keyboardType="numeric"
                      editable={!loading}
                    />
                  )}
                />
                <Text style={styles.inputDescription}>
                  Leave empty to use product&apos;s base price
                </Text>
              </View>

              <View style={styles.priceInput}>
                <Text style={styles.inputLabel}>Discounted Price (₹)</Text>
                <Controller
                  control={control}
                  name="discounted_price"
                  render={({ field: { onChange, value } }) => (
                    <TextInput
                      style={[
                        styles.textInput,
                        errors.discounted_price && styles.inputError,
                      ]}
                      placeholder="0.00"
                      value={value ? value.toString() : ""}
                      onChangeText={(text) =>
                        onChange(text ? parseFloat(text) : undefined)
                      }
                      keyboardType="numeric"
                      editable={!loading}
                    />
                  )}
                />
                <Text style={styles.inputDescription}>
                  Optional discounted price for this variant
                </Text>
              </View>
            </View>

            {/* Enhanced Availability Section */}
            <View style={styles.variantSection}>
              <View style={styles.variantSectionHeader}>
                <View style={styles.variantSectionIconContainer}>
                  <Settings size={20} color={theme.colors.primary} />
                </View>
                <View style={styles.variantSectionTitleContainer}>
                  <Text style={styles.variantSectionTitle}>Availability</Text>
                  <Text style={styles.variantSectionDescription}>
                    Control whether this variant is available for purchase
                  </Text>
                </View>
              </View>

              <Controller
                control={control}
                name="is_available"
                render={({ field: { onChange, value } }) => (
                  <View style={styles.enhancedAvailabilityContainer}>
                    <View style={styles.enhancedSwitchRow}>
                      <View style={styles.availabilityIconContainer}>
                        <View
                          style={[
                            styles.availabilityStatusDot,
                            {
                              backgroundColor: value
                                ? "#22C55E"
                                : theme.colors.destructive,
                            },
                          ]}
                        />
                      </View>
                      <View style={styles.enhancedSwitchLabelContainer}>
                        <Text style={styles.enhancedSwitchLabel}>
                          Available for purchase
                        </Text>
                        <Text style={styles.availabilityDescription}>
                          {value
                            ? "Customers can see and purchase this variant"
                            : "Variant will be hidden from customers"}
                        </Text>
                      </View>
                      <View style={styles.switchContainer}>
                        <Switch
                        value={value}
                        onValueChange={onChange}
                        disabled={loading}
                        trackColor={{
                          false: theme.colors.muted,
                          true: "#22C55E",
                        }}
                          thumbColor={
                            value ? "#FFFFFF" : theme.colors.mutedForeground
                          }
                        />
                      </View>
                    </View>
                  </View>
                )}
              />
            </View>

            {/* Enhanced Images Section */}
            <View style={styles.variantSection}>
              <View style={styles.variantSectionHeader}>
                <View style={styles.variantSectionIconContainer}>
                  <Camera size={20} color={theme.colors.primary} />
                </View>
                <View style={styles.variantSectionTitleContainer}>
                  <Text style={styles.variantSectionTitle}>Variant Images</Text>
                  <Text style={styles.variantSectionDescription}>
                    Add specific images for this variant (optional - uses main
                    product images if not set)
                  </Text>
                </View>
              </View>

              {images.length > 0 && (
                <View style={styles.imageGrid}>
                  {images.map((image, index) => (
                    <View key={index} style={styles.imageContainer}>
                      <Image
                        source={{ uri: image }}
                        style={styles.imagePreview}
                      />

                      {/* Featured Badge */}
                      {index === featuredImageIndex && (
                        <View style={styles.featuredBadge}>
                          <Text style={styles.featuredBadgeText}>Featured</Text>
                        </View>
                      )}

                      {/* Image Actions */}
                      <View style={styles.imageActions}>
                        {index !== featuredImageIndex && (
                          <TouchableOpacity
                            style={styles.imageActionButton}
                            onPress={() => setAsFeatured(index)}
                            disabled={loading}
                          >
                            <Text style={styles.imageActionText}>
                              Set Featured
                            </Text>
                          </TouchableOpacity>
                        )}

                        <TouchableOpacity
                          style={[
                            styles.imageActionButton,
                            styles.removeImageButton,
                          ]}
                          onPress={() => removeImage(index)}
                          disabled={loading}
                        >
                          <X size={16} color={theme.colors.destructive} />
                        </TouchableOpacity>
                      </View>
                    </View>
                  ))}
                </View>
              )}

              <TouchableOpacity
                style={styles.addImageButton}
                onPress={() => imagePickerRef.current?.present()}
                disabled={loading || imageProcessing}
              >
                <Camera size={20} color={theme.colors.primary} />
                <Text style={styles.addImageButtonText}>
                  {imageProcessing ? "Processing..." : "Add Images"}
                </Text>
              </TouchableOpacity>

              <Text style={styles.inputDescription}>
                Add up to 5 images for this variant. First image will be
                featured by default.
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Sticky Action Buttons */}
      <View style={styles.variantStickyButtonContainer}>
        <TouchableOpacity
          style={[styles.variantCancelButton]}
          onPress={onCancel}
          disabled={loading}
          activeOpacity={0.7}
        >
          <Text style={styles.variantCancelButtonText}>Cancel</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.variantSubmitButton,
            loading && styles.variantSubmitButtonDisabled,
          ]}
          onPress={handleSubmit(onFormSubmit)}
          disabled={loading}
          activeOpacity={0.7}
        >
          <Text style={styles.variantSubmitButtonText}>
            {loading ? "Saving..." : variant ? "Update Variant" : "Add Variant"}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Bottom Sheets */}
      <VariantTypeBottomSheet
        ref={variantTypePickerRef}
        selectedType={currentEditingType}
        onSelect={handleVariantTypeSelect}
        onClose={() => setCurrentEditingType("")}
      />

      <ColorPickerBottomSheet
        ref={colorPickerRef}
        selectedColor={variantValues[currentEditingType]}
        onSelect={handleColorSelect}
        onClose={() => setCurrentEditingType("")}
      />

      <ImagePickerBottomSheet
        ref={imagePickerRef}
        onCameraPress={() => {
          /* Handle camera */
        }}
        onGalleryPress={() => {
          /* Handle gallery */
        }}
      />
    </View>
  );
}
