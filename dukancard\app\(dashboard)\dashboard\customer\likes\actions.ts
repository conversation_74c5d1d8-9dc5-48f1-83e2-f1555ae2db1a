import { createClient } from '@/utils/supabase/server';


// Define interfaces for the expected data structure
interface BusinessProfileDataForLike {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  locality: string | null;
}

export interface LikeWithProfile {
  id: string;
  business_profiles: BusinessProfileDataForLike | null;
}

interface LikesResult {
  items: LikeWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Fetch customer likes with pagination and search
 */
export async function fetchCustomerLikes(
  userId: string,
  page: number = 1,
  limit: number = 12, // Optimized for 3-column grid
  searchTerm: string = ""
): Promise<LikesResult> {
  const supabase = await createClient();
  

  try {
    // Build the query with proper joins and filtering
    let query = supabase
      .from('likes')
      .select(`
        id,
        business_profiles!inner (
          id,
          business_name,
          business_slug,
          logo_url,
          city,
          state,
          locality
        )
      `)
      .eq('user_id', userId);

    // Apply search filter if provided
    if (searchTerm) {
      query = query.ilike('business_profiles.business_name', `%${searchTerm}%`);
    }

    // Get total count for pagination with proper join for search
    let countQuery = supabase
      .from('likes')
      .select(`
        id,
        business_profiles!inner (
          id,
          business_name
        )
      `, { count: 'exact', head: true })
      .eq('user_id', userId);

    // Apply search filter to count query if provided
    if (searchTerm) {
      countQuery = countQuery.ilike('business_profiles.business_name', `%${searchTerm}%`);
    }

    const { count: totalCount, error: countError } = await countQuery;

    if (countError) {
      throw new Error("Failed to get total count");
    }

    // If no likes, return empty result
    if (!totalCount || totalCount === 0) {
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: page
      };
    }

    // Apply pagination to the query
    const from = (page - 1) * limit;
    query = query.range(from, from + limit - 1);

    const { data: likesWithProfiles, error: likesError } = await query;

    if (likesError) {
      throw new Error("Failed to fetch likes");
    }

    // Transform the data to match LikeWithProfile interface
    const transformedItems: LikeWithProfile[] = (likesWithProfiles || []).map((item: { id: string; business_profiles: any; }) => ({
      id: item.id,
      business_profiles: Array.isArray(item.business_profiles)
        ? item.business_profiles[0] || null
        : item.business_profiles
    }));

    const hasMore = totalCount > from + limit;

    return {
      items: transformedItems,
      totalCount,
      hasMore,
      currentPage: page
    };
  } catch (error) {
    console.error('Error in fetchCustomerLikes:', error);
    throw error;
  }
}
