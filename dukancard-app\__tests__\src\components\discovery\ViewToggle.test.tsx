import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ViewToggle } from '@/src/components/discovery/ViewToggle';
import { useTheme } from '@/src/hooks/useTheme';

// Mock the useTheme hook
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      background: '#ffffff',
      cardBackground: '#f0f0f0',
      border: '#cccccc',
      shadow: '#000000',
      primary: '#C29D5B',
      primaryForeground: '#ffffff',
      textSecondary: '#888888',
    },
  }),
}));

describe('ViewToggle', () => {
  it('renders correctly with "cards" view active', () => {
    const { getByText } = render(
      <ViewToggle viewType="cards" onViewChange={() => {}} />
    );
    expect(getByText('Business Cards')).toBeTruthy();
    expect(getByText('Products')).toBeTruthy();
  });

  it('renders correctly with "products" view active', () => {
    const { getByText } = render(
      <ViewToggle viewType="products" onViewChange={() => {}} />
    );
    expect(getByText('Business Cards')).toBeTruthy();
    expect(getByText('Products')).toBeTruthy();
  });

  it('calls onViewChange with "products" when the products button is pressed', () => {
    const onViewChange = jest.fn();
    const { getByText } = render(
      <ViewToggle viewType="cards" onViewChange={onViewChange} />
    );
    fireEvent.press(getByText('Products'));
    expect(onViewChange).toHaveBeenCalledWith('products');
  });

  it('calls onViewChange with "cards" when the business cards button is pressed', () => {
    const onViewChange = jest.fn();
    const { getByText } = render(
      <ViewToggle viewType="products" onViewChange={onViewChange} />
    );
    fireEvent.press(getByText('Business Cards'));
    expect(onViewChange).toHaveBeenCalledWith('cards');
  });

  it('does not call onViewChange when the active button is pressed again', () => {
    const onViewChange = jest.fn();
    const { getByText } = render(
      <ViewToggle viewType="cards" onViewChange={onViewChange} />
    );
    fireEvent.press(getByText('Business Cards'));
    expect(onViewChange).not.toHaveBeenCalled();
  });

  it('disables the buttons when the disabled prop is true', () => {
    const onViewChange = jest.fn();
    const { getByText } = render(
      <ViewToggle viewType="cards" onViewChange={onViewChange} disabled={true} />
    );
    fireEvent.press(getByText('Products'));
    expect(onViewChange).not.toHaveBeenCalled();
  });
});
