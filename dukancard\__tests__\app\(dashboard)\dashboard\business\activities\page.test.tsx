import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import BusinessActivitiesPage from '../../../../../../app/(dashboard)/dashboard/business/activities/page';
import { createClient } from '@/utils/supabase/server';
import { getBusinessActivities, getUnreadActivitiesCount } from '@/lib/actions/activities';
import { redirect } from 'next/navigation';

// Mock external modules
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn(() => Promise.resolve({ data: { user: { id: 'user123' } }, error: null })),
    },
  })),
}));

jest.mock('@/lib/actions/activities', () => ({
  getBusinessActivities: jest.fn(() => Promise.resolve({
    activities: [],
    count: 0,
    error: null,
  })),
  getUnreadActivitiesCount: jest.fn(() => Promise.resolve({ count: 0 })),
}));

jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
}));

// Mock the client component
jest.mock('../../../../../../app/(dashboard)/dashboard/business/activities/components/ActivitiesPageClient', () => {
  const MockActivitiesPageClient = ({ initialActivities, totalCount, unreadCount, businessProfileId }: any) => (
    <div data-testid="activities-page-client">
      <p>Initial Activities: {initialActivities.length}</p>
      <p>Total Count: {totalCount}</p>
      <p>Unread Count: {unreadCount}</p>
      <p>Business Profile ID: {businessProfileId}</p>
    </div>
  );
  MockActivitiesPageClient.displayName = 'MockActivitiesPageClient';
  return MockActivitiesPageClient;
});

describe('BusinessActivitiesPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders ActivitiesPageClient with correct props when user is logged in', async () => {
    (createClient as jest.Mock).mockReturnValue({
      auth: {
        getUser: jest.fn(() => Promise.resolve({ data: { user: { id: 'user123' } }, error: null })),
      },
    });
    (getBusinessActivities as jest.Mock).mockResolvedValue({
      activities: [{ id: '1', activity_type: 'like', created_at: '2025-07-19T10:00:00Z', is_read: false, user_profile: null, business_profile_id: 'biz1', user_id: 'user1', post_id: null, post_type: null }],
      count: 1,
      error: null,
    });
    (getUnreadActivitiesCount as jest.Mock).mockResolvedValue({ count: 5 });

    render(await BusinessActivitiesPage());

    await waitFor(() => {
      expect(screen.getByTestId('activities-page-client')).toBeInTheDocument();
      expect(screen.getByText('Initial Activities: 1')).toBeInTheDocument();
      expect(screen.getByText('Total Count: 1')).toBeInTheDocument();
      expect(screen.getByText('Unread Count: 5')).toBeInTheDocument();
      expect(screen.getByText('Business Profile ID: user123')).toBeInTheDocument();
    });
  });

  it('redirects to login if user is not logged in', async () => {
    (createClient as jest.Mock).mockReturnValue({
      auth: {
        getUser: jest.fn(() => Promise.resolve({ data: { user: null }, error: { message: 'Auth error' } })),
      },
    });

    render(await BusinessActivitiesPage());

    await waitFor(() => {
      expect(redirect).toHaveBeenCalledWith('/login?message=Please log in to view your business activities.');
    });
  });

  it('handles error during activity fetch gracefully', async () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    (createClient as jest.Mock).mockReturnValue({
      auth: {
        getUser: jest.fn(() => Promise.resolve({ data: { user: { id: 'user123' } }, error: null })),
      },
    });
    (getBusinessActivities as jest.Mock).mockResolvedValue({
      activities: null,
      count: null,
      error: new Error('Failed to fetch activities'),
    });
    (getUnreadActivitiesCount as jest.Mock).mockResolvedValue({ count: 0 });

    render(await BusinessActivitiesPage());

    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error fetching activities:', expect.any(Error));
      expect(screen.getByText('Initial Activities: 0')).toBeInTheDocument();
      expect(screen.getByText('Total Count: 0')).toBeInTheDocument();
    });

    consoleErrorSpy.mockRestore();
  });
});
