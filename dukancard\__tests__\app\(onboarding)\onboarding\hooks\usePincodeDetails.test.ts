import { renderHook, waitFor, act } from '@testing-library/react';
import { usePincodeDetails } from '@/app/(onboarding)/onboarding/hooks/usePincodeDetails';
import { toast } from 'sonner';
import { getPincodeDetails } from '@/lib/actions/location';

// Mock external modules
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
    warning: jest.fn(),
  },
}));
jest.mock('@/lib/actions/location', () => ({
  getPincodeDetails: jest.fn(),
}));

describe('usePincodeDetails', () => {
  const mockFormSetValue = jest.fn();
  const mockForm = {
    setValue: mockFormSetValue,
  } as any; // Mock only necessary parts of UseFormReturn

  beforeEach(() => {
    jest.clearAllMocks();
    // Default mock for successful pincode details fetch
    (getPincodeDetails as jest.Mock).mockResolvedValue({
      city: 'Test City',
      state: 'Test State',
      localities: ['Test Locality 1', 'Test Locality 2'],
    });
  });

  it('should be in a non-loading state initially', () => {
    const { result } = renderHook(() => usePincodeDetails({
      form: mockForm,
    }));

    expect(result.current.isPincodeLoading).toBe(false);
    expect(result.current.availableLocalities).toEqual([]);
  });

  it('should call getPincodeDetails and update form fields on handlePincodeChange with 6 digits', async () => {
    const { result } = renderHook(() => usePincodeDetails({
      form: mockForm,
    }));

    await act(async () => {
      await result.current.handlePincodeChange('123456');
    });

    expect(getPincodeDetails).toHaveBeenCalledTimes(1);
    expect(getPincodeDetails).toHaveBeenCalledWith('123456');
    expect(result.current.isPincodeLoading).toBe(false);
    expect(mockFormSetValue).toHaveBeenCalledWith('locality', '');
    expect(mockFormSetValue).toHaveBeenCalledWith('city', '');
    expect(mockFormSetValue).toHaveBeenCalledWith('state', '');
    expect(mockFormSetValue).toHaveBeenCalledWith('city', 'Test City', { shouldValidate: true });
    expect(mockFormSetValue).toHaveBeenCalledWith('state', 'Test State', { shouldValidate: true });
    expect(result.current.availableLocalities).toEqual(['Test Locality 1', 'Test Locality 2']);
    expect(toast.success).toHaveBeenCalledWith('City and State auto-filled. Please select your locality.');
  });

  it('should not call getPincodeDetails if pincode is not 6 digits', async () => {
    const { result } = renderHook(() => usePincodeDetails({
      form: mockForm,
    }));

    await act(async () => {
      await result.current.handlePincodeChange('123');
    });

    expect(getPincodeDetails).not.toHaveBeenCalled();
    expect(result.current.isPincodeLoading).toBe(false);
  });

  it('should auto-select locality if only one is available', async () => {
    (getPincodeDetails as jest.Mock).mockResolvedValueOnce({
      city: 'Single City',
      state: 'Single State',
      localities: ['Single Locality'],
    });

    const { result } = renderHook(() => usePincodeDetails({
      form: mockForm,
    }));

    await act(async () => {
      await result.current.handlePincodeChange('111111');
    });

    expect(mockFormSetValue).toHaveBeenCalledWith('locality', 'Single Locality', { shouldValidate: true, shouldDirty: true });
  });

  it('should call toast.error on handlePincodeChange error', async () => {
    (getPincodeDetails as jest.Mock).mockResolvedValueOnce({
      error: 'Pincode not found',
    });

    const { result } = renderHook(() => usePincodeDetails({
      form: mockForm,
    }));

    await act(async () => {
      await result.current.handlePincodeChange('999999');
    });

    expect(toast.error).toHaveBeenCalledWith('Pincode not found');
    expect(result.current.isPincodeLoading).toBe(false);
  });

  it('should fetch details on initial load if initialPincode is provided', async () => {
    const { result } = renderHook(() => usePincodeDetails({
      form: mockForm,
      initialPincode: '222222',
    }));

    await waitFor(() => {
      expect(getPincodeDetails).toHaveBeenCalledTimes(1);
      expect(getPincodeDetails).toHaveBeenCalledWith('222222');
      expect(result.current.isPincodeLoading).toBe(false);
      expect(mockFormSetValue).toHaveBeenCalledWith('city', 'Test City', { shouldValidate: true });
      expect(mockFormSetValue).toHaveBeenCalledWith('state', 'Test State', { shouldValidate: true });
      expect(result.current.availableLocalities).toEqual(['Test Locality 1', 'Test Locality 2']);
    });
  });

  it('should validate and set initialLocality if it exists in fetched localities', async () => {
    (getPincodeDetails as jest.Mock).mockResolvedValueOnce({
      city: 'Initial City',
      state: 'Initial State',
      localities: ['Locality A', 'Initial Locality', 'Locality B'],
    });

    const { result } = renderHook(() => usePincodeDetails({
      form: mockForm,
      initialPincode: '333333',
      initialLocality: 'Initial Locality',
    }));

    await waitFor(() => {
      expect(mockFormSetValue).toHaveBeenCalledWith('locality', 'Initial Locality', { shouldValidate: true, shouldDirty: true });
    });
  });

  it('should clear locality and show warning if initialLocality is not in fetched localities', async () => {
    (getPincodeDetails as jest.Mock).mockResolvedValueOnce({
      city: 'Mismatch City',
      state: 'Mismatch State',
      localities: ['Locality X', 'Locality Y'],
    });

    const { result } = renderHook(() => usePincodeDetails({
      form: mockForm,
      initialPincode: '444444',
      initialLocality: 'Non-existent Locality',
    }));

    await waitFor(() => {
      expect(mockFormSetValue).toHaveBeenCalledWith('locality', '', { shouldValidate: true, shouldDirty: true });
      expect(toast.warning).toHaveBeenCalledWith('The locality "Non-existent Locality" is not available for pincode 444444. Please select a valid locality.');
    });
  });

  it('should call toast.error on initial load fetch error', async () => {
    (getPincodeDetails as jest.Mock).mockResolvedValueOnce({
      error: 'Initial fetch failed',
    });

    const { result } = renderHook(() => usePincodeDetails({
      form: mockForm,
      initialPincode: '555555',
    }));

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to fetch details for pincode 555555: Initial fetch failed');
      expect(result.current.isPincodeLoading).toBe(false);
    });
  });

  it('should show warning if no localities found for pincode on initial load', async () => {
    (getPincodeDetails as jest.Mock).mockResolvedValueOnce({
      city: 'No Locality City',
      state: 'No Locality State',
      localities: [],
    });

    const { result } = renderHook(() => usePincodeDetails({
      form: mockForm,
      initialPincode: '666666',
    }));

    await waitFor(() => {
      expect(result.current.isPincodeLoading).toBe(false);
    }, { timeout: 3000 });

    await waitFor(() => {
      expect(toast.warning).toHaveBeenCalledWith('No localities found for pincode 666666.');
    }, { timeout: 1000 });
  });
});
