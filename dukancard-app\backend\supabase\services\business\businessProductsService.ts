import { supabase } from "@/src/config/supabase";
import { Tables, TablesInsert, TablesUpdate } from "@dukancard-types/supabase";
import { addProductFormSchema, updateProductFormSchema, ProductServiceData } from "./schemas";
import { decode } from "base64-arraybuffer";
import { VariantFormData } from "./variantService";

export type ProductsServices = Tables<"products_services">;
export type ProductsServicesInsert = TablesInsert<"products_services">;
export type ProductsServicesUpdate = TablesUpdate<"products_services">;
export type ProductVariants = Tables<"product_variants">;

// Product filters interface to match Next.js
export interface ProductFilters {
  searchTerm?: string;
  filterAvailable?: boolean;
  hasVariants?: boolean;
  productType?: 'physical' | 'service';
  priceRange?: {
    min?: number;
    max?: number;
  };
}

// Product sort options to match Next.js
export type ProductSortBy =
  | "created_asc"
  | "created_desc"
  | "updated_asc"
  | "updated_desc"
  | "price_asc"
  | "price_desc"
  | "name_asc"
  | "name_desc"
  | "variant_count_asc"
  | "variant_count_desc";

// Legacy type for backward compatibility
type ProductSortOption =
  | "newest"
  | "oldest"
  | "name_asc"
  | "name_desc"
  | "price_asc"
  | "price_desc"
  | "available_first"
  | "unavailable_first";

// Next.js compatible function signature
export async function getProductServices(
  page: number = 1,
  limit: number = 10,
  filters: ProductFilters = {},
  sortBy: ProductSortBy = "created_desc"
): Promise<{
  data?: ProductsServices[];
  count?: number;
  error?: string;
}> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { error: "User not authenticated." };
    }

    const offset = (page - 1) * limit;
    let query = supabase
      .from("products_services")
      .select("*", { count: "exact" })
      .eq("business_id", user.id);

    // Apply filters
    if (filters.searchTerm?.trim()) {
      query = query.ilike("name", `%${filters.searchTerm.trim()}%`);
    }

    if (filters.filterAvailable !== undefined) {
      query = query.eq("is_available", filters.filterAvailable);
    }

    if (filters.productType) {
      query = query.eq("product_type", filters.productType);
    }

    if (filters.priceRange?.min !== undefined) {
      query = query.gte("base_price", filters.priceRange.min);
    }

    if (filters.priceRange?.max !== undefined) {
      query = query.lte("base_price", filters.priceRange.max);
    }

    // Apply sorting
    switch (sortBy) {
      case "created_desc":
        query = query.order("created_at", { ascending: false });
        break;
      case "created_asc":
        query = query.order("created_at", { ascending: true });
        break;
      case "updated_desc":
        query = query.order("updated_at", { ascending: false });
        break;
      case "updated_asc":
        query = query.order("updated_at", { ascending: true });
        break;
      case "name_asc":
        query = query.order("name", { ascending: true });
        break;
      case "name_desc":
        query = query.order("name", { ascending: false });
        break;
      case "price_asc":
        query = query.order("base_price", { ascending: true });
        break;
      case "price_desc":
        query = query.order("base_price", { ascending: false });
        break;
      default:
        query = query.order("created_at", { ascending: false });
    }

    const { data, error, count } = await query.range(offset, offset + limit - 1);

    if (error) {
      return { error: error.message };
    }

    return {
      data: data || [],
      count: count || 0,
    };
  } catch (error) {
    return { error: "Failed to fetch products" };
  }
}

// Legacy function for backward compatibility
export async function getBusinessProducts(
  page: number = 1,
  limit: number = 10,
  searchTerm?: string,
  sortBy: ProductSortOption = "newest"
): Promise<{
  success: boolean;
  data?: ProductsServices[];
  hasMore?: boolean;
  error?: string;
}> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: "Authentication required" };
    }

    const offset = (page - 1) * limit;
    let query = supabase
      .from("products_services")
      .select("*", { count: "exact" })
      .eq("business_id", user.id);

    if (searchTerm?.trim()) {
      query = query.ilike("name", `%${searchTerm.trim()}%`);
    }

    switch (sortBy) {
      case "newest":
        query = query.order("created_at", { ascending: false });
        break;
      case "oldest":
        query = query.order("created_at", { ascending: true });
        break;
      case "name_asc":
        query = query.order("name", { ascending: true });
        break;
      case "name_desc":
        query = query.order("name", { ascending: false });
        break;
      case "price_asc":
        query = query
          .order("discounted_price", { ascending: true, nullsFirst: false })
          .order("base_price", { ascending: true, nullsFirst: false });
        break;
      case "price_desc":
        query = query
          .order("discounted_price", { ascending: false, nullsFirst: false })
          .order("base_price", { ascending: false, nullsFirst: false });
        break;
      case "available_first":
        query = query.order("is_available", { ascending: false });
        break;
      case "unavailable_first":
        query = query.order("is_available", { ascending: true });
        break;
    }

    const { data, error, count } = await query.range(
      offset,
      offset + limit - 1
    );

    if (error) {
      return { success: false, error: error.message };
    }

    return {
      success: true,
      data: data || [],
      hasMore: (count || 0) > offset + limit,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch products",
    };
  }
}

export async function addProductService(
  productData: Omit<
    ProductsServicesInsert,
    "id" | "business_id" | "created_at" | "updated_at" | "slug"
  > & {
    images: string[]; // Expecting base64 images
    variants?: VariantFormData[]; // Optional variants to create
    removedOriginalIndices?: number[]; // Not used for add, but included for consistency
  }
): Promise<{ success: boolean; error?: string; data?: ProductsServices }> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: "User not authenticated." };
    }

    const validatedFields = addProductFormSchema.safeParse(productData);
    if (!validatedFields.success) {
      return {
        success: false,
        error: JSON.stringify(validatedFields.error.flatten().fieldErrors),
      };
    }

    const dataToInsert: ProductsServicesInsert = {
      ...validatedFields.data,
      business_id: user.id,
    };

    const { data: insertedProduct, error: insertError } = await supabase
      .from("products_services")
      .insert(dataToInsert)
      .select()
      .single();

    if (insertError) {
      // Enhanced error handling similar to Next.js
      if (insertError.message?.includes('Cannot make product available') &&
          insertError.message?.includes('reached the limit')) {
        return {
          success: false,
          error: insertError.message, // Use the database error message directly as it's user-friendly
        };
      }

      // Handle other specific errors
      if (insertError.message?.includes('duplicate key')) {
        return { success: false, error: "A product with this name already exists." };
      }

      if (insertError.message?.includes('violates check constraint')) {
        return { success: false, error: "Invalid product data. Please check your inputs." };
      }

      return { success: false, error: insertError.message };
    }

    if (productData.images && productData.images.length > 0) {
      const imageUrls: string[] = [];
      for (let i = 0; i < productData.images.length; i++) {
        const image = productData.images[i];
        const fileExt = image.split(";")[0].split("/")[1];
        const filePath = `${user.id}/${insertedProduct.id}/product_image_${i}.${fileExt}`;
        const { error: uploadError } = await supabase.storage
          .from("business")
          .upload(filePath, decode(image), {
            contentType: `image/${fileExt}`,
            upsert: true,
          });

        if (uploadError) {
          console.error("Image upload error:", uploadError);
          continue; // Or handle more gracefully
        }
        const { data: urlData } = supabase.storage
          .from("business")
          .getPublicUrl(filePath);
        imageUrls.push(urlData.publicUrl);
      }

      const featuredImageIndex = productData.featured_image_index || 0;
      const { data: updatedProduct, error: updateError } = await supabase
        .from("products_services")
        .update({
          images: imageUrls,
          image_url: imageUrls[featuredImageIndex],
          featured_image_index: featuredImageIndex,
        })
        .eq("id", insertedProduct.id)
        .select()
        .single();

      if (updateError) {
        return { success: false, error: updateError.message };
      }
      return { success: true, data: updatedProduct };
    }

    // Handle variants if provided
    if (productData.variants && productData.variants.length > 0) {
      const { addProductVariant } = await import("./variantService");

      for (const variantData of productData.variants) {
        try {
          const variantResult = await addProductVariant(insertedProduct.id, variantData);
          if (!variantResult.success) {
            console.error("Failed to create variant:", variantResult.error);
            // Continue with other variants even if one fails
          }
        } catch (variantError) {
          console.error("Error creating variant:", variantError);
          // Continue with other variants
        }
      }
    }

    return { success: true, data: insertedProduct };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to add product",
    };
  }
}

export async function updateProductService(
  itemId: string,
  productData: Partial<ProductsServicesUpdate> & {
    images?: string[];
    variants?: VariantFormData[]; // Optional variants to update/create
    removedOriginalIndices?: number[]; // Track removed original images for cleanup
  }
): Promise<{ success: boolean; error?: string; data?: ProductsServices }> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: "User not authenticated." };
    }

    const validatedFields = updateProductFormSchema.safeParse(productData);
    if (!validatedFields.success) {
      return {
        success: false,
        error: JSON.stringify(validatedFields.error.flatten().fieldErrors),
      };
    }

    const dataToUpdate: Omit<ProductsServicesUpdate, "created_at"> = {
      ...validatedFields.data,
      updated_at: new Date().toISOString(),
    };

    if (productData.images && productData.images.length > 0) {
      const imageUrls: string[] = [];
      for (let i = 0; i < productData.images.length; i++) {
        const image = productData.images[i];
        if (image.startsWith("data:image")) {
          // New base64 image
          const fileExt = image.split(";")[0].split("/")[1];
          const filePath = `${
            user.id
          }/${itemId}/product_image_${Date.now()}_${i}.${fileExt}`;
          const { error: uploadError } = await supabase.storage
            .from("business")
            .upload(filePath, decode(image), {
              contentType: `image/${fileExt}`,
              upsert: true,
            });

          if (uploadError) {
            console.error("Image upload error:", uploadError);
            continue;
          }
          const { data: urlData } = supabase.storage
            .from("business")
            .getPublicUrl(filePath);
          imageUrls.push(urlData.publicUrl);
        } else {
          // Existing URL
          imageUrls.push(image);
        }
      }
      dataToUpdate.images = imageUrls;
      if (
        productData.featured_image_index !== undefined &&
        productData.featured_image_index !== null
      ) {
        dataToUpdate.image_url =
          imageUrls[productData.featured_image_index] || null;
      }
    }

    const { data: updatedProduct, error: updateError } = await supabase
      .from("products_services")
      .update(dataToUpdate)
      .eq("id", itemId)
      .eq("business_id", user.id)
      .select()
      .single();

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    // Handle variants if provided
    if (productData.variants && productData.variants.length > 0) {
      const { addProductVariant, updateProductVariant } = await import("./variantService");

      for (const variantData of productData.variants) {
        try {
          // Check if variant has an ID (existing) or is new
          if ('id' in variantData && variantData.id) {
            // Update existing variant
            const variantResult = await updateProductVariant(variantData.id as string, variantData);
            if (!variantResult.success) {
              console.error("Failed to update variant:", variantResult.error);
            }
          } else {
            // Create new variant
            const variantResult = await addProductVariant(itemId, variantData);
            if (!variantResult.success) {
              console.error("Failed to create variant:", variantResult.error);
            }
          }
        } catch (variantError) {
          console.error("Error processing variant:", variantError);
        }
      }
    }

    return { success: true, data: updatedProduct };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update product",
    };
  }
}

export async function deleteProductService(
  itemId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: "User not authenticated." };
    }

    // First, get the product to find all associated images
    const { data: product, error: fetchError } = await supabase
      .from("products_services")
      .select("images")
      .eq("id", itemId)
      .eq("business_id", user.id)
      .single();

    if (fetchError) {
      return { success: false, error: "Product not found." };
    }

    if (product && product.images && product.images.length > 0) {
      const filesToDelete = product.images.map((imageUrl: string) => {
        const url = new URL(imageUrl);
        const pathParts = url.pathname.split("/");
        const storagePath = pathParts
          .slice(pathParts.indexOf("business") + 1)
          .join("/");
        return storagePath;
      });

      const { error: deleteImagesError } = await supabase.storage
        .from("business")
        .remove(filesToDelete);

      if (deleteImagesError) {
        console.error("Error deleting images:", deleteImagesError);
        // Don't block deletion of the product itself
      }
    }

    const { error: deleteError } = await supabase
      .from("products_services")
      .delete()
      .eq("id", itemId)
      .eq("business_id", user.id);

    if (deleteError) {
      return { success: false, error: deleteError.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to delete product",
    };
  }
}
