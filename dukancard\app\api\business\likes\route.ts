import { createClient } from '@/utils/supabase/server';

import { NextRequest, NextResponse } from 'next/server';

// Define interfaces for the expected data structure
interface BusinessProfileDataForLike {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface CustomerProfileDataForLike {
  id: string;
  name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface BusinessLikeReceived {
  id: string;
  user_id: string;
  customer_profiles?: CustomerProfileDataForLike | null;
  business_profiles?: BusinessProfileDataForLike | null;
  profile_type: 'customer' | 'business';
}

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  perPage: number;
}

export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = 12; // Optimized for 3-column grid (1x12, 2x6, 3x4)

    // Use admin client for cross-table queries
    const supabase = await createClient();

    // Get total count first for pagination
    const { count: totalCount, error: countError } = await supabase
      .from('likes')
      .select('id', { count: 'exact', head: true })
      .eq('business_profile_id', user.id);

    if (countError) {
      return NextResponse.json(
        { error: 'Failed to count likes' },
        { status: 500 }
      );
    }

    if (!totalCount || totalCount === 0) {
      return NextResponse.json({
        likes: [],
        pagination: {
          currentPage: page,
          totalPages: 0,
          totalCount: 0,
          perPage
        }
      });
    }

    // Get paginated likes (database-level pagination)
    const from = (page - 1) * perPage;
    const { data: paginatedLikes, error: likesError } = await supabase
      .from('likes')
      .select('id, user_id')
      .eq('business_profile_id', user.id)
      .range(from, from + perPage - 1);

    if (likesError) {
      return NextResponse.json(
        { error: 'Failed to fetch likes' },
        { status: 500 }
      );
    }

    if (!paginatedLikes || paginatedLikes.length === 0) {
      return NextResponse.json({
        likes: [],
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalCount / perPage),
          totalCount,
          perPage
        }
      });
    }

    // Get user IDs for the paginated results only
    const userIds = paginatedLikes.map((like: { user_id: string }) => like.user_id);

    // Fetch customer and business profiles for paginated results only
    const [customerProfiles, businessProfiles] = await Promise.all([
      supabase
        .from('customer_profiles')
        .select('id, name, email, avatar_url')
        .in('id', userIds),
      supabase
        .from('business_profiles')
        .select('id, business_name, business_slug, logo_url, city, state, pincode, address_line')
        .in('id', userIds)
    ]);

    // Create maps for easy lookup
    const customerProfilesMap = new Map(
      customerProfiles.data?.map((profile: CustomerProfileDataForLike) => [profile.id, profile]) || []
    );
    const businessProfilesMap = new Map(
      businessProfiles.data?.map((profile: BusinessProfileDataForLike) => [profile.id, profile]) || []
    );

    // Combine likes with their corresponding profiles
    const processedLikes = paginatedLikes
      .map((like: { id: string; user_id: string }) => {
        const customerProfile = customerProfilesMap.get(like.user_id);
        const businessProfile = businessProfilesMap.get(like.user_id);

        if (customerProfile) {
          return {
            id: like.id,
            user_id: like.user_id,
            customer_profiles: customerProfile,
            profile_type: 'customer' as const
          };
        } else if (businessProfile) {
          return {
            id: like.id,
            user_id: like.user_id,
            business_profiles: businessProfile,
            profile_type: 'business' as const
          };
        }
        return null;
      })
      .filter((item: BusinessLikeReceived | null): item is BusinessLikeReceived => item !== null);

    const totalPages = Math.ceil(totalCount / perPage);

    const paginationData: PaginationData = {
      currentPage: page,
      totalPages,
      totalCount,
      perPage
    };

    return NextResponse.json({
      likes: processedLikes,
      pagination: paginationData
    });

  } catch (error) {
    console.error('Error fetching business likes received:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
