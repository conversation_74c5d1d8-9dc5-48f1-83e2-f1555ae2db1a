import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";
import { TABLES, COLUMNS } from "@/lib/supabase/constants";
import { Database } from "@/types/supabase";

export async function GET() {
  try {
    const supabase = await createClient();
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { isBusiness: false, error: "Not authenticated" },
        { status: 401 }
      );
    }
    
    // Check if the user has a business profile
    const { data: businessProfile, error: profileError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, user.id)
      .maybeSingle();
      
    if (profileError) {
      console.error("Error checking business profile:", profileError);
      return NextResponse.json(
        { isBusiness: false, error: "Database error" },
        { status: 500 }
      );
    }
    
    // Return whether the user is a business or not
    return NextResponse.json({ isBusiness: !!businessProfile });
    
  } catch (error) {
    console.error("Unexpected error in check-user-type API:", error);
    return NextResponse.json(
      { isBusiness: false, error: "Server error" },
      { status: 500 }
    );
  }
}
