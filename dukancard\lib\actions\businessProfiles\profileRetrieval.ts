"use server";

import { createClient } from "@/utils/supabase/server";
import {
  BusinessProfilePublicData,
  BusinessProfileWithProducts,
} from "./types";

/**
 * Securely fetch a business profile by slug using the service role key
 * This bypasses RLS and ensures sensitive data is not exposed to the client
 */
export async function getSecureBusinessProfileBySlug(slug: string): Promise<{
  data?: BusinessProfilePublicData;
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  try {
    // Use the admin client with service role key to bypass RLS
    const supabase = await createClient();

    // Fetch the business profile with subscription data - don't filter by status to allow testing
    const { data: profileData, error: profileError } = await supabase
      .from("business_profiles")
      .select(
        `
        *,
        payment_subscriptions!business_profile_id (
          plan_id,
          subscription_status
        )
      `
      )
      .eq("business_slug", slug)
      .maybeSingle();

    if (profileError) {
      console.error("Secure Fetch Error:", profileError);
      return {
        error: `Failed to fetch business profile: ${profileError.message}`,
      };
    }

    if (!profileData) {
      return { error: "Profile not found." };
    }

    const safeData: BusinessProfilePublicData = {
      ...profileData,
      subscription_status:
        profileData.payment_subscriptions?.subscription_status || null,
      plan_id: profileData.payment_subscriptions?.plan_id || null,
    };

    return { data: safeData };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfileBySlug:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetch a business profile with products by slug using the service role key
 */
export async function getSecureBusinessProfileWithProductsBySlug(
  slug: string
): Promise<{
  data?: BusinessProfileWithProducts;
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  try {
    // Use the admin client with service role key to bypass RLS
    const supabase = await createClient();

    // Fetch the business profile with products - don't filter by status to allow testing
    const { data: profileData, error: profileError } = await supabase
      .from("business_profiles")
      .select(
        `
        *,
        products_services (
          id, name, description, base_price, discounted_price, is_available, image_url, created_at, updated_at, product_type
        )
      `
      )
      .eq("business_slug", slug)
      .maybeSingle();

    if (profileError) {
      console.error("Secure Fetch Error:", profileError);
      return {
        error: `Failed to fetch business profile: ${profileError.message}`,
      };
    }

    if (!profileData) {
      return { error: "Profile not found." };
    }

    const safeData: BusinessProfileWithProducts = {
      ...profileData,
      products_services: profileData.products_services || [],
    };

    return { data: safeData };
  } catch (e) {
    console.error(
      "Exception in getSecureBusinessProfileWithProductsBySlug:",
      e
    );
    return { error: "An unexpected error occurred." };
  }
}
