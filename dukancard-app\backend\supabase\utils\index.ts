/**
 * Centralized exports for all backend utilities
 * These are backend-specific utilities for data processing, validation, etc.
 */

// Address and Location Utils
export {
  formatAddress,
  validateAddress as validateAddressFormat,
  type AddressData
} from './addressUtils';
export {
  getAddressValidationMessage,
  getMissingAddressFields,
  isCustomerAddressComplete,
  cleanAddressData,
  isValidPincode,
  type CustomerAddressData
} from './addressValidation';
export { calculateDistance } from './locationUtils';

// Business Utils
export { validateBusinessSlug } from './businessSlugValidation';
export { generateSlug, validateSlugFormat, isReservedSlug } from './slugUtils';

// Profile and Validation Utils
export {
  isValidIndianMobile as validateIndianMobileProfile,
  isValidPincode as validatePincodeProfile,
  validateEmail as validateEmailProfile,
} from './profileValidation';


// Storage Utils
export * from './storage-paths';

// Error Handling Utils
export { handleSupabaseError, isAuthError, isDatabaseError, SupabaseError } from './supabaseErrorHandler';
