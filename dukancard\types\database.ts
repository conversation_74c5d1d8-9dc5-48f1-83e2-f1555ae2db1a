/**
 * Database type helpers and commonly used table types
 * This file provides type aliases for the auto-generated supabase types
 */

import { Database } from './supabase';

// Helper type to extract table types
export type Tables<T extends keyof Database['public']['Tables'], K extends keyof Database['public']['Tables'][T] = 'Row'> = Database['public']['Tables'][T][K];

// Commonly used table row types
export type BusinessProfilesRow = Tables<'business_profiles', 'Row'>;
export type CustomerProfilesRow = Tables<'customer_profiles', 'Row'>;
export type Blogs = Tables<'blogs', 'Row'>;
export type ProductVariants = Tables<'product_variants', 'Row'>;

// Insert and Update types for commonly used tables
export type BusinessProfilesInsert = Tables<'business_profiles', 'Insert'>;
export type BusinessProfilesUpdate = Tables<'business_profiles', 'Update'>;
export type CustomerProfilesInsert = Tables<'customer_profiles', 'Insert'>;
export type CustomerProfilesUpdate = Tables<'customer_profiles', 'Update'>;
export type ProductVariantsInsert = Tables<'product_variants', 'Insert'>;
export type ProductVariantsUpdate = Tables<'product_variants', 'Update'>;

// Re-export Database and Json types for convenience
export type { Database, Json } from './supabase';
