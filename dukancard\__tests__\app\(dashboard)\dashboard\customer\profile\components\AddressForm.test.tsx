import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

import { updateCustomerAddress } from '@/app/(dashboard)/dashboard/customer/profile/actions';
import { useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { usePincodeDetails } from '@/app/(dashboard)/dashboard/customer/profile/components/hooks/usePincodeDetails';
import AddressForm from '@/app/(dashboard)/dashboard/customer/profile/components/AddressForm';

jest.mock('@/app/(dashboard)/dashboard/customer/profile/components/hooks/usePincodeDetails', () => ({
  usePincodeDetails: jest.fn(),
}));

// Mock react-hook-form's useForm hook
let mockFormValues: any = {};
let mockFormErrors: Record<string, any> = {};
let mockTriggerResult = true;

jest.mock('react-hook-form', () => ({
  useForm: jest.fn(() => ({
    register: jest.fn((name: string) => ({
      name,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        mockFormValues = { ...mockFormValues, [name]: e.target.value };
      },
      onBlur: () => {
        // Simulate validation on blur if needed
      },
    })),
    handleSubmit: jest.fn((cb) => (e: React.FormEvent) => {
      e.preventDefault();
      return cb(mockFormValues);
    }),
    control: { // Simplified mock control for FormField
      _defaultValues: {},
      register: jest.fn(),
      unregister: jest.fn(),
      getFieldState: jest.fn(() => ({ invalid: false, isDirty: false, isTouched: false, error: undefined })),
      _names: {},
      _formState: {},
      _subjects: {},
      getValues: jest.fn(() => mockFormValues),
      _get: jest.fn(),
      _set: jest.fn(),
      _updateFormState: jest.fn(),
      _removeUnmounted: jest.fn(),
    },
    formState: { errors: mockFormErrors }, // Reference the mutable object
    getValues: jest.fn(() => mockFormValues),
    trigger: jest.fn(() => mockTriggerResult),
    reset: jest.fn((values?: any) => {
      mockFormValues = values || {};
      mockFormErrors = {};
      mockTriggerResult = true;
    }),
  })),
  zodResolver: jest.fn(() => jest.fn()),
}));

// Mock next/navigation's useSearchParams hook
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(() => new URLSearchParams()),
}));



// Mock server actions
jest.mock('@/app/(dashboard)/dashboard/customer/profile/actions', () => ({
  updateCustomerAddress: jest.fn(),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

describe('AddressForm', () => {
  const initialProps = {
    initialData: {
      address: '123 Main St',
      pincode: '123456',
      city: 'Anytown',
      state: 'Anystate',
      locality: 'Anylocality',
    },
    hideSubmitButton: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockFormValues = { ...initialProps.initialData };
    mockFormErrors = {};
    mockTriggerResult = true;

    // Reset useForm mock for each test
    (useForm as jest.Mock).mockReturnValue({
      register: jest.fn((name: string) => ({
        name,
        onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
          mockFormValues = { ...mockFormValues, [name]: e.target.value };
        },
        onBlur: () => {},
      })),
      handleSubmit: jest.fn((cb) => (e: React.FormEvent) => {
        e.preventDefault();
        return cb(mockFormValues);
      }),
      control: { // Simplified mock control for FormField
        _defaultValues: {},
        register: jest.fn(),
        unregister: jest.fn(),
        getFieldState: jest.fn(() => ({ invalid: false, isDirty: false, isTouched: false, error: undefined })),
        _names: {},
        _formState: {},
        _subjects: {},
        getValues: jest.fn(() => mockFormValues),
        _get: jest.fn(),
        _set: jest.fn(),
        _updateFormState: jest.fn(),
        _removeUnmounted: jest.fn(),
      },
      formState: { errors: mockFormErrors },
      getValues: jest.fn(() => mockFormValues),
      trigger: jest.fn(() => mockTriggerResult),
      reset: jest.fn((values?: any) => {
        mockFormValues = values || {};
        mockFormErrors = {};
        mockTriggerResult = true;
      }),
    });

    (updateCustomerAddress as jest.Mock).mockResolvedValue({ success: true, message: 'Address updated successfully!' });
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams());
    (usePincodeDetails as jest.Mock).mockReturnValue({
      isPincodeLoading: false,
      availableLocalities: ['Locality A', 'Locality B'],
      handlePincodeChange: jest.fn(),
    });

    // Mock HTMLFormElement.prototype.submit
    jest.spyOn(HTMLFormElement.prototype, 'submit').mockImplementation(() => {});
  });

  it('renders correctly with initial data', () => {
    render(<AddressForm {...initialProps} />);

    expect(screen.getByLabelText(/Address \(Optional\)/i)).toHaveValue('123 Main St');
    expect(screen.getByLabelText(/Pincode \*/i)).toHaveValue('123456');
    expect(screen.getByLabelText(/City \*/i)).toHaveValue('Anytown');
    expect(screen.getByLabelText(/State \*/i)).toHaveValue('Anystate');
    expect(screen.getByLabelText(/Locality \/ Area \*/i)).toHaveTextContent('Anylocality');
    expect(screen.getByRole('button', { name: /Update Address/i })).toBeInTheDocument();
  });

  it('hides submit button when hideSubmitButton is true', () => {
    render(<AddressForm {...initialProps} hideSubmitButton={true} />);
    expect(screen.queryByRole('button', { name: /Update Address/i })).not.toBeInTheDocument();
  });

  it('updates input values', () => {
    render(<AddressForm {...initialProps} />);
    const addressInput = screen.getByLabelText(/Address \(Optional\)/i);
    fireEvent.change(addressInput, { target: { value: '456 New St' } });
    expect(addressInput).toHaveValue('456 New St');
  });

  it('shows client-side validation error for empty required fields', async () => {
    mockFormValues = { address: '', pincode: '', city: '', state: '', locality: '' };
    mockFormErrors = {
      pincode: { message: 'Pincode is required' },
      city: { message: 'City is required' },
      state: { message: 'State is required' },
      locality: { message: 'Locality is required' },
    };
    mockTriggerResult = false;

    render(<AddressForm {...initialProps} />);
    const pincodeInput = screen.getByLabelText(/Pincode \*/i);
    fireEvent.change(pincodeInput, { target: { value: '' } });
    fireEvent.blur(pincodeInput);

    await waitFor(() => {
      expect(screen.getByText('Pincode is required')).toBeInTheDocument();
      expect(screen.getByText('City is required')).toBeInTheDocument();
      expect(screen.getByText('State is required')).toBeInTheDocument();
      expect(screen.getByText('Locality is required')).toBeInTheDocument();
    });
    expect(screen.getByRole('button', { name: /Update Address/i })).toBeDisabled();
  });

  it('calls handlePincodeChange when pincode input length is 6', () => {
    const mockHandlePincodeChange = jest.fn();
    (usePincodeDetails as jest.Mock).mockReturnValue({
      isPincodeLoading: false,
      availableLocalities: ['Locality A', 'Locality B'],
      handlePincodeChange: mockHandlePincodeChange,
    });

    render(<AddressForm {...initialProps} />);
    const pincodeInput = screen.getByLabelText(/Pincode \*/i);
    fireEvent.change(pincodeInput, { target: { value: '12345' } });
    expect(mockHandlePincodeChange).not.toHaveBeenCalled();

    fireEvent.change(pincodeInput, { target: { value: '123456' } });
    expect(mockHandlePincodeChange).toHaveBeenCalledWith('123456');
  });

  it('disables locality select when no available localities', () => {
    (usePincodeDetails as jest.Mock).mockReturnValue({
      isPincodeLoading: false,
      availableLocalities: [],
      handlePincodeChange: jest.fn(),
    });

    render(<AddressForm {...initialProps} />);
    const localitySelect = screen.getByLabelText(/Locality \/ Area \*/i);
    expect(localitySelect).toBeDisabled();
    expect(screen.getByText('Enter Pincode first')).toBeInTheDocument();
  });

  it('shows loading spinner when pincode is loading', () => {
    (usePincodeDetails as jest.Mock).mockReturnValue({
      isPincodeLoading: true,
      availableLocalities: [],
      handlePincodeChange: jest.fn(),
    });

    render(<AddressForm {...initialProps} />);
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });
});