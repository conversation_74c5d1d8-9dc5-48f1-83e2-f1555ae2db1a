// Mock for camera utils

export interface CameraCapabilities {
  hasCamera: boolean;
  hasPermission: boolean | null;
  isSecureContext: boolean;
  supportedConstraints: MediaTrackSupportedConstraints | null;
  error?: string;
}

export interface CameraDevice {
  deviceId: string;
  label: string;
  kind: 'videoinput';
  groupId: string;
}

export function isCameraSupported(): boolean {
  return true;
}

export function isSecureContext(): boolean {
  return true;
}

export async function getCameraDevices(): Promise<CameraDevice[]> {
  return [
    {
      deviceId: 'mock-camera-1',
      label: 'Mock Camera 1',
      kind: 'videoinput',
      groupId: 'mock-group-1'
    }
  ];
}

export async function checkCameraPermission(): Promise<PermissionState | null> {
  return 'granted';
}

export async function requestCameraAccess(
  constraints: MediaStreamConstraints = { video: true }
): Promise<{ stream: MediaStream; capabilities: CameraCapabilities }> {
  const mockStream = {
    getTracks: () => [
      {
        stop: jest.fn(),
        kind: 'video',
        enabled: true,
        readyState: 'live'
      }
    ]
  } as unknown as MediaStream;

  const capabilities: CameraCapabilities = {
    hasCamera: true,
    hasPermission: true,
    isSecureContext: true,
    supportedConstraints: {}
  };

  return { stream: mockStream, capabilities };
}

export async function getCameraCapabilities(): Promise<CameraCapabilities> {
  return {
    hasCamera: true,
    hasPermission: true,
    isSecureContext: true,
    supportedConstraints: {}
  };
}

export function stopCameraStream(stream: MediaStream): void {
  // Mock implementation
}

export function isMobileDevice(): boolean {
  return false;
}

export function getQRScanConstraints(preferredDeviceId?: string): MediaStreamConstraints {
  return {
    video: {
      width: { ideal: 1280 },
      height: { ideal: 720 },
      facingMode: 'user',
      frameRate: { ideal: 30 }
    }
  };
}
