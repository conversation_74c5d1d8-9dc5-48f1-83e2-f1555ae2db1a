import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import AboutTab from '@/src/components/business/AboutTab';
import { Linking, Alert } from 'react-native';
import { createPublicCardViewStyles } from '@/styles/PublicCardViewStyles';
import { BusinessDiscoveryData } from '@/src/types/discovery';

// Mock external modules
jest.mock('@/styles/PublicCardViewStyles', () => ({
  createPublicCardViewStyles: jest.fn(() => ({
    section: {},
    aboutSection: {},
    aboutText: {},
    categorySection: {},
    categoryTitle: {},
    aboutTableContainer: {},
    aboutTableRow: {},
    aboutTableLabel: {},
    aboutTableLabelText: {},
    aboutTableValue: {},
    statusBadge: {},
    statusText: {},
    callButton: {},
    callButtonText: {},
  })),
}));

// Mock Linking
const mockLinking = {
  openURL: jest.fn(() => Promise.resolve()),
  canOpenURL: jest.fn(() => Promise.resolve(true)),
};

jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Alert: {
      alert: jest.fn(),
    },
    Linking: mockLinking,
    // Mocking specific components to allow testID to be passed
    View: RN.View,
    Text: RN.Text,
    ScrollView: RN.ScrollView,
    TouchableOpacity: RN.TouchableOpacity,
  };
});

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  Package: 'PackageIcon',
  Users: 'UsersIcon',
  Calendar: 'CalendarIcon',
  Clock: 'ClockIcon',
  Phone: 'PhoneIcon',
  Mail: 'MailIcon',
  MapPin: 'MapPinIcon',
  Truck: 'TruckIcon',
  Navigation: 'NavigationIcon',
  MessageCircle: 'MessageCircleIcon',
  Globe: 'GlobeIcon',
}));

describe('AboutTab', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Ensure the mock returns a Promise
    mockLinking.openURL.mockReturnValue(Promise.resolve());
  });

  

  const mockBusinessData: Partial<BusinessDiscoveryData['data']> = {
    about_bio: 'We are a business dedicated to providing the best services.',
    business_name: 'Test Business',
    member_name: 'John Doe',
    business_category: 'Retail',
    established_year: 2020,
    phone: '1234567890',
    whatsapp_number: '919876543210',
    facebook_url: 'https://facebook.com/testbusiness',
    instagram_url: 'https://instagram.com/testbusiness',
    address_line: '123 Main St',
    locality: 'Downtown',
    city: 'Mumbai',
    state: 'Maharashtra',
    pincode: '400001',
    delivery_info: 'Free delivery within 5km',
    business_hours: {
      monday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
      tuesday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
      wednesday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
      thursday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
      friday: { isOpen: true, openTime: '09:00', closeTime: '17:00' },
      saturday: { isOpen: false },
      sunday: { isOpen: true, openTime: '10:00', closeTime: '14:00' },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (createPublicCardViewStyles as jest.Mock).mockReturnValue({
      section: {},
      aboutSection: {},
      aboutText: {},
      categorySection: {},
      categoryTitle: {},
      aboutTableContainer: {},
      aboutTableRow: {},
      aboutTableLabel: {},
      aboutTableLabelText: {},
      aboutTableValue: {},
      statusBadge: {},
      statusText: {},
      callButton: {},
      callButtonText: {},
    });
  });

  it('renders all sections when all data is provided', () => {
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);

    expect(screen.getByText('We are a business dedicated to providing the best services.')).toBeOnTheScreen();
    expect(screen.getByText('Business Information')).toBeOnTheScreen();
    expect(screen.getByText('Test Business')).toBeOnTheScreen();
    expect(screen.getByText('John Doe')).toBeOnTheScreen();
    expect(screen.getByText('Retail')).toBeOnTheScreen();
    expect(screen.getByText('2020')).toBeOnTheScreen();
    expect(screen.getByText('Open')).toBeOnTheScreen();
    expect(screen.getByText('Contact Details')).toBeOnTheScreen();
    expect(screen.getByTestId('call-now-button')).toBeOnTheScreen();
    expect(screen.getByText('123 Main St, Downtown, Mumbai, Maharashtra, 400001')).toBeOnTheScreen();
    expect(screen.getByText('Free delivery within 5km')).toBeOnTheScreen();
    expect(screen.getByText('Business Hours')).toBeOnTheScreen();
    expect(screen.getByText('Monday')).toBeOnTheScreen();
    expect(screen.getByText('9:00 AM - 5:00 PM')).toBeOnTheScreen();
    expect(screen.getByText('Saturday')).toBeOnTheScreen();
    expect(screen.getByText('Closed')).toBeOnTheScreen();
    expect(screen.getByText('Sunday')).toBeOnTheScreen();
    expect(screen.getByText('10:00 AM - 2:00 PM')).toBeOnTheScreen();
  });

  it('does not render About Bio section if about_bio is missing', () => {
    const { about_bio, ...dataWithoutBio } = mockBusinessData;
    render(<AboutTab businessData={dataWithoutBio as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    expect(screen.queryByText('We are a business dedicated to providing the best services.')).toBeNull();
  });

  it('does not render Phone Number section if phone is missing', () => {
    const { phone, ...dataWithoutPhone } = mockBusinessData;
    render(<AboutTab businessData={dataWithoutPhone as BusinessDiscoveryData} isDark={false} />);
    expect(screen.queryByTestId('call-now-button')).toBeNull();
  });

  it('does not render WhatsApp section if whatsapp_number is missing', () => {
    const { whatsapp_number, ...dataWithoutWhatsApp } = mockBusinessData;
    render(<AboutTab businessData={dataWithoutWhatsApp as BusinessDiscoveryData} isDark={false} />);
    expect(screen.queryByTestId('whatsapp-button')).toBeNull();
  });

  it('does not render Facebook section if facebook_url is missing', () => {
    const { facebook_url, ...dataWithoutFacebook } = mockBusinessData;
    render(<AboutTab businessData={dataWithoutFacebook as BusinessDiscoveryData} isDark={false} />);
    expect(screen.queryByTestId('facebook-button')).toBeNull();
  });

  it('does not render Instagram section if instagram_url is missing', () => {
    const { instagram_url, ...dataWithoutInstagram } = mockBusinessData;
    render(<AboutTab businessData={dataWithoutInstagram as BusinessDiscoveryData} isDark={false} />);
    expect(screen.queryByTestId('instagram-button')).toBeNull();
  });

  it('does not render Delivery Info section if delivery_info is missing', () => {
    const { delivery_info, ...dataWithoutDelivery } = mockBusinessData;
    render(<AboutTab businessData={dataWithoutDelivery as BusinessDiscoveryData} isDark={false} />);
    expect(screen.queryByText('Free delivery within 5km')).toBeNull();
  });

  it('handles phone call correctly', () => {
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    fireEvent.press(screen.getByTestId('call-now-button'));
    expect(mockLinking.openURL).toHaveBeenCalledWith('tel:1234567890');
  });

  it('shows alert if phone call fails', async () => {
    mockLinking.openURL.mockImplementationOnce(() => Promise.reject('error'));
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    fireEvent.press(screen.getByTestId('call-now-button'));
    expect(Alert.alert).toHaveBeenCalledWith('Error', 'Unable to make phone call');
  });

  it('handles WhatsApp chat correctly', () => {
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    fireEvent.press(screen.getByTestId('whatsapp-button'));
    expect(mockLinking.openURL).toHaveBeenCalledWith(
      'https://wa.me/919876543210?text=Hi%20Test%20Business%2C%20I%20found%20your%20business%20on%20Dukancard%20and%20would%20like%20to%20know%20more%20about%20your%20services.'
    );
  });

  it('shows alert if WhatsApp chat fails', async () => {
    mockLinking.openURL.mockImplementationOnce(() => Promise.reject('error'));
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    fireEvent.press(screen.getByTestId('whatsapp-button'));
    expect(Alert.alert).toHaveBeenCalledWith('Error', 'Unable to open WhatsApp');
  });

  it('handles Facebook link correctly', () => {
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    fireEvent.press(screen.getByTestId('facebook-button'));
    expect(mockLinking.openURL).toHaveBeenCalledWith('https://facebook.com/testbusiness');
  });

  it('shows alert if Facebook link fails', async () => {
    mockLinking.openURL.mockImplementationOnce(() => Promise.reject('error'));
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    fireEvent.press(screen.getByTestId('facebook-button'));
    expect(Alert.alert).toHaveBeenCalledWith('Error', 'Unable to open Facebook');
  });

  it('handles Instagram link correctly', () => {
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    fireEvent.press(screen.getByTestId('instagram-button'));
    expect(mockLinking.openURL).toHaveBeenCalledWith('https://instagram.com/testbusiness');
  });

  it('shows alert if Instagram link fails', async () => {
    mockLinking.openURL.mockImplementationOnce(() => Promise.reject('error'));
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    fireEvent.press(screen.getByTestId('instagram-button'));
    expect(Alert.alert).toHaveBeenCalledWith('Error', 'Unable to open Instagram');
  });

  it('formats business hours correctly, including closed days', () => {
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    expect(screen.getByText(/Monday/)).toBeOnTheScreen();
    expect(screen.getByText(/9:00 AM - 5:00 PM/)).toBeOnTheScreen();
    expect(screen.getByText(/Saturday/)).toBeOnTheScreen();
    expect(screen.getByText(/Closed/)).toBeOnTheScreen();
    expect(screen.getByText(/Sunday/)).toBeOnTheScreen();
    expect(screen.getByText(/10:00 AM - 2:00 PM/)).toBeOnTheScreen();
  });

  it('does not render Business Hours section if business_hours is missing', () => {
    const { business_hours, ...dataWithoutHours } = mockBusinessData;
    render(<AboutTab businessData={dataWithoutHours as BusinessDiscoveryData} isDark={false} />);
    expect(screen.queryByText('Business Hours')).toBeNull();
  });

  it('renders correctly in dark mode', () => {
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={true} />);
    // Expect styles to be called with isDark = true
    expect(createPublicCardViewStyles).toHaveBeenCalledWith(true);
  });

  it('renders correctly in light mode', () => {
    render(<AboutTab businessData={mockBusinessData as BusinessProfiles & { user_plan?: string | null }} isDark={false} />);
    // Expect styles to be called with isDark = false
    expect(createPublicCardViewStyles).toHaveBeenCalledWith(false);
  });

  it('handles empty business data gracefully', () => {
    const emptyBusinessData = {} as BusinessProfiles & { user_plan?: string | null }; // Cast to BusinessDiscoveryData
    render(<AboutTab businessData={emptyBusinessData} isDark={false} />);
    expect(screen.getByText(/Business Information/)).toBeOnTheScreen(); // Title should still be there
    expect(screen.queryByText('Test Business')).toBeNull();
    expect(screen.getByText(/Contact Details/)).toBeOnTheScreen(); // Title should still be there
    expect(screen.queryByTestId('call-now-button')).toBeNull();
    expect(screen.queryByText(/Business Hours/)).toBeNull();
    expect(screen.queryByText(/Social Media/)).toBeNull();
  });
});