import { createClient } from "@/utils/supabase/server";
import { notFound } from "next/navigation";
import {
  ProductServiceData,
  ProductSortBy,
} from "@/app/(dashboard)/dashboard/business/products/actions";
import PublicCardPageClient from "./PublicCardPageClient";
import { AdData } from "@/types/ad";
import type { Metadata } from "next";

import { getSecureBusinessProfileBySlug } from "@/lib/actions/businessProfiles";
import { getBusinessGalleryImagesForTab } from "@/lib/actions/gallery";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";
import OfflineBusinessMessage from "./components/OfflineBusinessMessage";

const INITIAL_PRODUCTS_PAGE_SIZE = 20;

// Define a type for the profile data we need for these helper functions
type ProfilePlanData = {
  subscription_status: string | null;
  plan_id: string | null;
};

// Helper function to determine user plan
const getUserPlan = (
  profile: ProfilePlanData
): "free" | "basic" | "growth" | "pro" | "enterprise" | undefined => {
  // Simply return the plan_id from the subscription data
  switch (profile.plan_id) {
    case "free":
      return "free";
    case "growth":
      return "growth";
    case "pro":
      return "pro";
    case "enterprise":
      return "enterprise";
    case "basic":
      return "basic";
    default:
      return "free"; // Default to free if no plan_id specified
  }
};

// Helper function to determine if platform ads should be shown
const shouldShowPlatformAds = (): boolean => {
  // Show platform ads for all plans - Pro/Enterprise users can override with their own custom ads
  return true; // Always show platform ads as fallback
};

export default async function PublicCardPage({
  params,
}: {
  params: Promise<{ cardSlug: string }>;
}) {
  const { cardSlug } = await params;
  const supabase = await createClient();

  // Use the secure method to fetch the business profile
  const { data: businessProfile, error: profileError } =
    await getSecureBusinessProfileBySlug(cardSlug);

  if (profileError || !businessProfile) {
    console.error(`Error fetching profile for slug ${cardSlug}:`, profileError);
    notFound();
  }

  // Check if the profile is online
  if (businessProfile.status !== "online") {
    console.log(
      `Business profile ${cardSlug} is not online (status: ${businessProfile.status}).`
    );
    // Show offline message instead of 404
    return <OfflineBusinessMessage />;
  }

  // We no longer check subscription status, only if the business is online
  // The status field is the only thing that matters now

  // Check if required fields are missing but status is still online
  // Define the required fields directly here to avoid type issues
  const requiredFields = [
    "member_name",
    "title",
    "business_name",
    "phone",
    "address_line",
    "pincode",
    "city",
    "state",
    "locality",
    "contact_email",
  ];

  const missingRequiredFields = requiredFields.filter(
    (field) =>
      !businessProfile[field as keyof typeof businessProfile] ||
      String(businessProfile[field as keyof typeof businessProfile]).trim() ===
        ""
  );

  if (missingRequiredFields.length > 0 && businessProfile.status === "online") {
    console.log(
      `Business profile ${cardSlug} is missing required fields but status is online, updating to offline. Missing fields: ${missingRequiredFields.join(
        ", "
      )}`
    );

    // Update the profile using admin client

    await supabase
      .from("business_profiles")
      .update({
        status: "offline",
      })
      .eq("id", businessProfile.id);

    // Show offline message instead of 404
    return <OfflineBusinessMessage />;
  }

  // We no longer check subscription status, only if the business is online
  // The status field is the only thing that matters now

  // Determine user plan
  const userPlan = getUserPlan(businessProfile);
  let topAdData: AdData = null;

  // Fetch platform ads for all businesses (Pro/Enterprise can override with their own custom ads)
  if (shouldShowPlatformAds()) {
    try {
      // First, check if the custom_ad_targets table exists (for backward compatibility)
      const { count, error: tableCheckError } = await supabase
        .from("custom_ad_targets")
        .select("*", { count: "exact", head: true });

      // If the table exists and migration has been applied
      if (count !== null && !tableCheckError) {
        // Use the get_ad_for_pincode function to find the appropriate ad
        const pincode = businessProfile.pincode || "999999"; // Use a dummy pincode if none provided
        const { data: adData, error: adError } = await supabase.rpc(
          "get_ad_for_pincode",
          { target_pincode: pincode }
        );

        if (adData && adData.length > 0) {
          // Found an ad (either pincode-specific or global)
          topAdData = {
            type: "custom",
            imageUrl: adData[0].ad_image_url,
            linkUrl: adData[0].ad_link_url,
          };
        } else {
          // No custom ads found or error occurred
          if (adError)
            console.error(`Error fetching ad for pincode ${pincode}:`, adError);
          topAdData = null; // Show placeholder when no custom ads are available
        }
      } else {
        // Fallback to old approach if migration hasn't been applied yet
        if (businessProfile.pincode) {
          const { data: customAd } = await supabase
            .from("custom_ads")
            .select("ad_image_url, ad_link_url")
            .eq("is_active", true)
            .or(
              `targeting_locations.eq.'"global"',targeting_locations.cs.'["${businessProfile.pincode}"]'`
            )
            .order("created_at", { ascending: false })
            .limit(1)
            .maybeSingle();

          if (customAd) {
            topAdData = {
              type: "custom",
              imageUrl: customAd.ad_image_url,
              linkUrl: customAd.ad_link_url,
            };
          } else {
            // No matching custom ad found
            topAdData = null;
          }
        } else {
          // If business has no pincode, try to find global ads
          const { data: globalAd } = await supabase
            .from("custom_ads")
            .select("ad_image_url, ad_link_url")
            .eq("is_active", true)
            .eq("targeting_locations", '"global"')
            .order("created_at", { ascending: false })
            .limit(1)
            .maybeSingle();

          if (globalAd) {
            topAdData = {
              type: "custom",
              imageUrl: globalAd.ad_image_url,
              linkUrl: globalAd.ad_link_url,
            };
          } else {
            topAdData = null;
          }
        }
      }
    } catch (adFetchError) {
      console.error(`Error fetching custom ad:`, adFetchError);
      topAdData = null; // fallback on error
    }
  }

  const defaultSortPreference: ProductSortBy = "created_desc";

  // Use the admin client to bypass RLS policies

  const productQuery = supabase
    .from("products_services")
    .select("*", { count: "exact" })
    .eq("business_id", businessProfile.id)
    .eq("is_available", true)
    .order("created_at", { ascending: false })
    .limit(INITIAL_PRODUCTS_PAGE_SIZE);

  const {
    data: initialProducts,
    error: productsError,
    count: totalProductCount,
  } = await productQuery;

  if (productsError) {
    console.error(
      `Error fetching initial products for business ${businessProfile.id}:`,
      productsError
    );
    // Consider if this should be a fatal error or just log
  }

  // Fetch gallery images for this business (limited to 4 for gallery tab)
  const {
    images: galleryImages,
    totalCount: galleryTotalCount,
    error: galleryError,
  } = await getBusinessGalleryImagesForTab(cardSlug);

  if (galleryError) {
    console.error(
      `Error fetching gallery images for business ${businessProfile.id}:`,
      galleryError
    );
    // Don't fail the page load for gallery error, just log it
  }

  const {
    data: { user },
  } = await supabase.auth.getUser();
  const isAuthenticated = !!user;
  const currentUserId = user?.id || null;

  // Fetch total reviews count for the business (excluding self-reviews)
  const { count: totalReviews, error: reviewsCountError } = await supabase
    .from("ratings_reviews")
    .select("id", { count: "exact", head: true })
    .eq("business_profile_id", businessProfile.id)
    .neq("user_id", businessProfile.id); // Don't count self-reviews

  if (reviewsCountError) {
    console.error(
      `Error fetching reviews count for business ${businessProfile.id}:`,
      reviewsCountError
    );
  }

  // Create a new object with the total reviews count and ensure all required fields are properly typed
  const businessProfileWithReviews: BusinessCardData = {
    ...businessProfile,
    total_reviews: totalReviews || 0,
    // Ensure all required fields are properly typed and present
    phone: businessProfile.phone || "",
    city: businessProfile.city || "",
    state: businessProfile.state || "",
    pincode: businessProfile.pincode || "",
    locality: businessProfile.locality || "",
    address_line: businessProfile.address_line || "",
    business_name: businessProfile.business_name || "",
    contact_email: businessProfile.contact_email || "",
    member_name: businessProfile.member_name || "",
    status: businessProfile.status as "online" | "offline",
    title: businessProfile.title || "",
    business_category: businessProfile.business_category || "",
    custom_branding: businessProfile.custom_branding,
    custom_ads: businessProfile.custom_ads,
    plan_id: businessProfile.plan_id || null,
    total_visits: businessProfile.total_visits || 0,
    today_visits: businessProfile.today_visits || 0,
    yesterday_visits: businessProfile.yesterday_visits || 0,
    visits_7_days: businessProfile.visits_7_days || 0,
    visits_30_days: businessProfile.visits_30_days || 0,
    city_slug: businessProfile.city_slug || null,
    state_slug: businessProfile.state_slug || null,
    locality_slug: businessProfile.locality_slug || null,
    gallery: businessProfile.gallery,
    latitude: businessProfile.latitude,
    longitude: businessProfile.longitude,
    subscription_status: businessProfile.subscription_status || null,
    whatsapp_number: businessProfile.whatsapp_number || null,
    instagram_url: businessProfile.instagram_url || null,
    facebook_url: businessProfile.facebook_url || null,
    about_bio: businessProfile.about_bio || null,
    business_slug: businessProfile.business_slug || null,
    theme_color: businessProfile.theme_color || null,
    delivery_info: businessProfile.delivery_info || null,
    total_likes: businessProfile.total_likes || 0,
    total_subscriptions: businessProfile.total_subscriptions || 0,
    average_rating: businessProfile.average_rating || null,
    business_hours: businessProfile.business_hours || null,
    trial_end_date: businessProfile.trial_end_date || null,
    created_at: businessProfile.created_at ? new Date(businessProfile.created_at) : undefined,
    updated_at: businessProfile.updated_at ? new Date(businessProfile.updated_at) : undefined,
    established_year: businessProfile.established_year || null,
    google_maps_url: businessProfile.google_maps_url || null,
    primary_phone: businessProfile.phone || "",
  };

  return (
    <div className="min-h-screen flex flex-col">
      <PublicCardPageClient
        businessProfile={businessProfileWithReviews as BusinessCardData}
        initialProducts={(initialProducts as ProductServiceData[]) ?? []}
        totalProductCount={totalProductCount ?? 0}
        defaultSortPreference={defaultSortPreference}
        isAuthenticated={isAuthenticated}
        currentUserId={currentUserId}
        userPlan={userPlan}
        topAdData={topAdData}
        galleryImages={galleryImages ?? []}
        galleryTotalCount={galleryTotalCount ?? 0}
      />
    </div>
  );
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ cardSlug: string }>;
}): Promise<Metadata> {
  const { cardSlug } = await params;
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://dukancard.in";
  const pageUrl = `${siteUrl}/${cardSlug}`;

  // Use the secure method to fetch the business profile
  const { data: businessProfile, error: profileError } =
    await getSecureBusinessProfileBySlug(cardSlug);

  if (profileError || !businessProfile) {
    // Use notFound() to trigger the 404 page for non-existent business slugs
    notFound();
  }

  const businessName = businessProfile.business_name || "Business";

  // For offline businesses, only use the business name in the title
  // For online businesses, include the address
  let baseTitle = businessName;
  let fullAddress = "";

  if (businessProfile.status === "online") {
    // Create a complete address string from all address components
    const addressComponents = [
      businessProfile.address_line,
      businessProfile.city,
      businessProfile.state,
      businessProfile.pincode,
    ].filter(Boolean);

    fullAddress = addressComponents.join(", ");

    // Create the SEO title with business name and address
    if (fullAddress) {
      baseTitle = `${businessName} - ${fullAddress}`;
    }
  }

  // Create different descriptions for online and offline businesses
  let description = "";

  if (businessProfile.status === "online") {
    description =
      `Visit ${businessName}'s digital business card on Dukancard. ${
        businessProfile.about_bio ? businessProfile.about_bio + " " : ""
      }Find products, services, contact info, and location${
        fullAddress ? ` at ${fullAddress}` : ""
      }.`.trim();
  } else {
    description = `${businessName}'s digital business card on Dukancard is currently offline. Check back later or discover other businesses nearby.`;
  }
  const ogImage = businessProfile.logo_url || `${siteUrl}/opengraph-image.png`;

  const keywords = [
    businessName,
    "", // Empty business_category
    businessProfile?.city,
    businessProfile?.state,
    "digital business card",
    "online storefront",
    "shop near me",
    "Dukancard",
  ].filter(Boolean);

  const schema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name: businessName,
    description: businessProfile.about_bio || description,
    url: pageUrl,
    image: businessProfile.logo_url,
    telephone: businessProfile?.phone,
    address: {
      "@type": "PostalAddress",
      streetAddress: businessProfile.address_line,
      addressLocality: businessProfile.city,
      addressRegion: businessProfile.state,
      postalCode: businessProfile.pincode,
      addressCountry: "IN",
    },
  };

  return {
    title: baseTitle,
    description,
    keywords: keywords.filter((k): k is string => k !== null),
    alternates: {
      canonical: `/${cardSlug}`,
    },
    openGraph: {
      title: baseTitle,
      description,
      url: pageUrl,
      siteName: "Dukancard",
      type: "profile",
      locale: "en_IN",
      images: [
        {
          url: ogImage,
          alt: `${businessName} - Digital Business Card`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: baseTitle,
      description,
      images: [ogImage],
    },
    other: {
      "application-ld+json": JSON.stringify(schema),
    },
  };
}
