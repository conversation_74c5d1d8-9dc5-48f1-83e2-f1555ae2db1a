"use server";

import { createClient } from "@/utils/supabase/server";


import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { NearbyProduct, getSortingColumn, getSortingDirection } from "./types";
import { searchDiscoverCombined } from "./combinedActions";

// Define a type for the product result from Supabase
// Unused type kept for reference
/* type ProductResult = {
  id: string;
  business_id: string | null;
  name: string | null;
  description: string | null;
  base_price: number | null;
  discounted_price: number | null;
  product_type: "physical" | "service" | null;
  is_available: boolean | null;
  image_url: string | null;
  created_at: string | null;
  updated_at: string | null;
  business_profiles: {
    business_slug: string | null;
  } | null;
}; */

// Helper function to convert any product result to NearbyProduct
function convertToNearbyProduct(
  product: Record<string, unknown>
): NearbyProduct {
  // Extract business_slug from the joined business_profiles
  let business_slug = null;
  if (product.business_profiles) {
    // Handle both object and array formats
    if (Array.isArray(product.business_profiles)) {
      business_slug = product.business_profiles[0]?.business_slug || null;
    } else if (
      product.business_profiles &&
      typeof product.business_profiles === "object"
    ) {
      business_slug =
        ((product.business_profiles as Record<string, unknown>)
          .business_slug as string) || null;
    }
  }

  return {
    id: product.id as string,
    business_id: product.business_id as string | undefined,
    name: (product.name as string) || "",
    description: (product.description as string) || "",
    base_price: Number(product.base_price) || 0,
    discounted_price: product.discounted_price
      ? Number(product.discounted_price)
      : undefined,
    product_type:
      (product.product_type as "physical" | "service") || "physical",
    is_available: Boolean(product.is_available) || false,
    image_url: product.image_url as string | undefined,
    created_at: product.created_at
      ? new Date(product.created_at as string)
      : undefined,
    updated_at: product.updated_at
      ? new Date(product.updated_at as string)
      : undefined,
    slug: product.slug as string | undefined,
    business_slug: business_slug,
    featured_image_index: 0, // Default value for NearbyProduct
    images: [], // Default empty array for images
  };
}

// Function to fetch more products with combined search for infinite scroll
export async function fetchMoreProductsCombined(params: {
  businessName?: string | null;
  productName?: string | null;
  pincode?: string | null;
  locality?: string | null;
  page: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productSort?: string;
  productType?: "physical" | "service" | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  // Reuse the searchDiscoverCombined function with viewType set to "products"
  const result = await searchDiscoverCombined({
    ...params,
    viewType: "products",
  });

  if (result.error) {
    return { error: result.error };
  }

  if (!result.data?.products) {
    return { error: "No product data found" };
  }

  return {
    data: {
      products: result.data.products,
      totalCount: result.data.totalCount,
      hasMore: result.data.hasMore,
      nextPage: result.data.nextPage,
    },
  };
}

// Function to fetch all products
export async function fetchAllProducts(params: {
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productType?: "physical" | "service" | null;
  pincode?: string | null;
  locality?: string | null;
  productName?: string | null;
  category?: string | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    isAuthenticated: boolean;
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
    pincode = null,
    locality = null,
    productName = null,
    category = null,
  } = params;

  // Check Authentication
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  const isAuthenticated = !!user;

  try {
    const supabase = await createClient();
    const offset = (page - 1) * limit;

    // Get all online business IDs without checking subscription status
    let businessQuery = supabase
      .from("business_profiles")
      .select("id")
      .eq("status", "online");

    // Add pincode filter if provided
    if (pincode) {
      businessQuery = businessQuery.eq("pincode", pincode);
    }

    // Add locality filter if provided
    if (locality) {
      businessQuery = businessQuery.eq("locality", locality);
    }

    // Add category filter if provided
    if (category && category.trim()) {
      businessQuery = businessQuery.eq("business_category", category.trim());
    }

    const { data: validBusinesses, error: businessError } = await businessQuery;

    if (businessError) {
      console.error("Error fetching valid businesses:", businessError);
      return { error: "Failed to fetch valid businesses" };
    }

    if (!validBusinesses || validBusinesses.length === 0) {
      return {
        data: {
          products: [],
          isAuthenticated,
          totalCount: 0,
          hasMore: false,
          nextPage: null,
        },
      };
    }

    const validBusinessIds = validBusinesses.map((b: { id: string }) => b.id);

    // Build the query for counting products - count products from valid businesses
    let countQuery = supabase
      .from("products_services")
      .select("id", { count: "exact" })
      .in("business_id", validBusinessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      countQuery = countQuery.eq("product_type", productType);
    }

    // Add product name filter if provided
    if (productName && productName.trim().length > 0) {
      countQuery = countQuery.ilike("name", `%${productName.trim()}%`);
    }

    // Get total count
    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Error counting products:", countError);
      return { error: "Failed to count products" };
    }

    // Build the query for fetching products from valid businesses
    let productsQuery = supabase
      .from("products_services")
      .select(
        `
        id, business_id, name, description, base_price, discounted_price, product_type,
        is_available, image_url, created_at, updated_at, slug,
        business_profiles!business_id(business_slug)
      `
      )
      .in("business_id", validBusinessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      productsQuery = productsQuery.eq("product_type", productType);
    }

    // Add product name filter if provided
    if (productName && productName.trim().length > 0) {
      productsQuery = productsQuery.ilike("name", `%${productName.trim()}%`);
    }

    // Add sorting
    const sortColumn = getSortingColumn(sortBy, true); // true indicates product view
    const sortAscending = getSortingDirection(sortBy);

    // Special handling for price sorting to use discounted_price when available, otherwise base_price
    if (sortColumn === "price") {
      if (sortAscending) {
        productsQuery = productsQuery
          .order("discounted_price", { ascending: true, nullsFirst: false })
          .order("base_price", { ascending: true, nullsFirst: false });
      } else {
        productsQuery = productsQuery
          .order("discounted_price", { ascending: false, nullsFirst: false })
          .order("base_price", { ascending: false, nullsFirst: false });
      }
    } else {
      productsQuery = productsQuery.order(sortColumn, {
        ascending: sortAscending,
      });
    }

    // Add pagination
    productsQuery = productsQuery.range(offset, offset + limit - 1);

    // Execute the query
    const { data: productsData, error: productsError } = await productsQuery;

    if (productsError) {
      console.error("Error fetching products:", productsError);
      return { error: "Failed to fetch products" };
    }

    // Process the products data to include business_slug
    const products = productsData.map(convertToNearbyProduct);

    // Calculate pagination info
    const totalCount = count || 0;
    const hasMore = totalCount > offset + products.length;
    const nextPage = hasMore ? page + 1 : null;

    return {
      data: {
        products,
        isAuthenticated,
        totalCount,
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Unexpected error in fetchAllProducts:", error);
    return { error: "An unexpected error occurred" };
  }
}

// Function to fetch products by business IDs
export async function fetchProductsByBusinessIds(params: {
  businessIds: string[];
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productType?: "physical" | "service" | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    isAuthenticated: boolean;
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    businessIds,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
  } = params;

  if (!businessIds || businessIds.length === 0) {
    return {
      error: "No business IDs provided",
    };
  }

  // Check Authentication
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  const isAuthenticated = !!user;

  try {
    const offset = (page - 1) * limit;
    

    // Filter the business IDs to only include online ones
    const { data: validBusinesses, error: businessError } = await supabase
      .from("business_profiles")
      .select("id")
      .in("id", businessIds)
      .eq("status", "online");

    if (businessError) {
      console.error("Error filtering valid businesses:", businessError);
      return { error: "Failed to filter valid businesses" };
    }

    // If no valid businesses found, return empty result
    if (!validBusinesses || validBusinesses.length === 0) {
      return {
        data: {
          products: [],
          isAuthenticated,
          totalCount: 0,
          hasMore: false,
          nextPage: null,
        },
      };
    }

    // Get the IDs of valid businesses
    const validBusinessIds = validBusinesses.map((b: { id: string; }) => b.id);

    // Build the query for counting products
    let countQuery = supabase
      .from("products_services")
      .select("id", { count: "exact" })
      .in("business_id", validBusinessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      countQuery = countQuery.eq("product_type", productType);
    }

    // Get total count
    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Error counting products:", countError);
      return { error: "Failed to count products" };
    }

    // Build the query for fetching products
    let productsQuery = supabase
      .from("products_services")
      .select(
        `
        id, business_id, name, description, base_price, discounted_price, product_type,
        is_available, image_url, created_at, updated_at, slug,
        business_profiles!business_id(business_slug)
      `
      )
      .in("business_id", validBusinessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      productsQuery = productsQuery.eq("product_type", productType);
    }

    // Add sorting
    const sortColumn = getSortingColumn(sortBy, true); // true indicates product view
    const sortAscending = getSortingDirection(sortBy);

    // Special handling for price sorting to use discounted_price when available, otherwise base_price
    if (sortColumn === "price") {
      if (sortAscending) {
        productsQuery = productsQuery
          .order("discounted_price", { ascending: true, nullsFirst: false })
          .order("base_price", { ascending: true, nullsFirst: false });
      } else {
        productsQuery = productsQuery
          .order("discounted_price", { ascending: false, nullsFirst: false })
          .order("base_price", { ascending: false, nullsFirst: false });
      }
    } else {
      productsQuery = productsQuery.order(sortColumn, {
        ascending: sortAscending,
      });
    }

    // Add pagination
    productsQuery = productsQuery.range(offset, offset + limit - 1);

    // Execute the query
    const { data: productsData, error: productsError } = await productsQuery;

    if (productsError) {
      console.error("Error fetching products:", productsError);
      return { error: "Failed to fetch products" };
    }

    // Process the products data to include business_slug
    const products = productsData.map(convertToNearbyProduct);

    // Calculate pagination info
    const totalCount = count || 0;
    const hasMore = totalCount > offset + products.length;
    const nextPage = hasMore ? page + 1 : null;

    return {
      data: {
        products,
        isAuthenticated,
        totalCount,
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Unexpected error in fetchProductsByBusinessIds:", error);
    return { error: "An unexpected error occurred" };
  }
}
