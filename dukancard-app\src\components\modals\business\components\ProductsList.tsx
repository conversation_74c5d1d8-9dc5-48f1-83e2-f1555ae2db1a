import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
  Image,
  TextInput,
} from "react-native";
import { Edit, Trash2, Package, Search, X, SortAsc, Plus } from "lucide-react-native";
import { Tables } from "@dukancard-types/supabase";
import { useTheme } from "@/src/hooks/useTheme";
import { useAuth } from "@/src/contexts/AuthContext";
import { ProductsServices, getBusinessProducts, deleteProductService } from "@/backend/supabase/services/business/businessProductsService";
import { createManageProductsModalStyles } from "@/styles/modals/business/manage-products-modal";
import { ProductsModalSkeleton } from "@/src/components/skeletons/modals/ProductsModalSkeleton";
import { ErrorState } from "@/src/components/ui/ErrorState";
import { logError, handleNetworkError } from "@/src/utils/errorHandling";
import { ProductSortOption } from "./ProductsSortBottomSheet";
import { useAlertDialog } from "@/src/components/providers/AlertProvider";

interface ProductCardProps {
  product: Tables<'products_services'>;
  onEdit: (product: Tables<'products_services'>) => void;
  onDelete: (productId: string) => void;
  theme: ReturnType<typeof useTheme>;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onEdit,
  onDelete,
  theme,
}) => {
  const styles = createManageProductsModalStyles(theme);
  const { delete: showDeleteAlert } = useAlertDialog();

  const handleDelete = () => {
    showDeleteAlert(
      "Product",
      () => onDelete(product.id),
      undefined,
      `Are you sure you want to delete "${product.name}"? This action cannot be undone.`
    );
  };

  const formatPrice = (price: number | null) => {
    if (!price) return "Free";
    return `₹${price.toLocaleString("en-IN")}`;
  };

  const getDisplayPrice = () => {
    if (
      product.discounted_price &&
      product.discounted_price < (product.base_price || 0)
    ) {
      return (
        <View style={styles.priceContainer}>
          <Text style={styles.discountedPrice}>
            {formatPrice(product.discounted_price)}
          </Text>
          <Text style={styles.originalPrice}>
            {formatPrice(product.base_price)}
          </Text>
        </View>
      );
    }
    return <Text style={styles.price}>{formatPrice(product.base_price)}</Text>;
  };

  return (
    <View style={styles.productCard}>
      <View style={styles.productImageContainer}>
        {product.image_url ? (
          <Image
            source={{ uri: product.image_url }}
            style={styles.productImage}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.placeholderImage}>
            <Package size={20} color={theme.colors.muted} />
          </View>
        )}
      </View>

      <View style={styles.productInfo}>
        <Text style={styles.productName} numberOfLines={2}>
          {product.name}
        </Text>
        {product.description && (
          <Text style={styles.productDescription} numberOfLines={2}>
            {product.description}
          </Text>
        )}
        {getDisplayPrice()}
        <View style={styles.productMeta}>
          <Text style={styles.productType}>
            {product.product_type === "physical" ? "Physical" : "Service"}
          </Text>
          <Text
            style={[
              styles.productStatus,
              product.is_available
                ? styles.productStatusAvailable
                : styles.productStatusUnavailable,
            ]}
          >
            {product.is_available ? "Available" : "Unavailable"}
          </Text>
        </View>
      </View>

      <View style={styles.productActions}>
        <TouchableOpacity
          onPress={() => onEdit(product)}
          style={[styles.actionButton, styles.editButton]}
          activeOpacity={0.7}
        >
          <Edit size={16} color={theme.colors.foreground} />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleDelete}
          style={[styles.actionButton, styles.deleteButton]}
          activeOpacity={0.7}
        >
          <Trash2 size={16} color="#EF4444" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

interface ProductsListProps {
  searchTerm: string;
  sortBy: ProductSortOption;
  onEdit: (product: ProductsServices) => void;
  onDelete: (productId: string) => void;
  onSearchChange: (term: string) => void;
  onSearch: () => void;
  onClearSearch: () => void;
  onSortPress: () => void;
  onAddPress: () => void;
}

const ProductsList: React.FC<ProductsListProps> = ({
  searchTerm,
  sortBy,
  onEdit,
  onDelete,
  onSearchChange,
  onSearch,
  onClearSearch,
  onSortPress,
  onAddPress,
}) => {
  const { user } = useAuth();
  const theme = useTheme();
  const styles = createManageProductsModalStyles(theme);
  const { error: showErrorAlert } = useAlertDialog();

  const [products, setProducts] = useState<Tables<'products_services'>[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [error, setError] = useState<any>(null);
  const [activeSearchTerm, setActiveSearchTerm] = useState("");

  const fetchProducts = useCallback(
    async (currentPage: number, isRefreshing = false, isSearchOrSort = false) => {
      if (!user) return;

      if (loadingMore || (loading && !isRefreshing && !isSearchOrSort)) return;

      if (isRefreshing && !isSearchOrSort) {
        setRefreshing(true);
      } else if (isSearchOrSort || currentPage === 1) {
        if (isSearchOrSort) {
          setSearchLoading(true);
        } else {
          setLoading(true);
        }
      } else {
        setLoadingMore(true);
      }
      setError(null);

      try {
        const result = await getBusinessProducts(
          currentPage,
          20, // Fetch 20 items per page as requested
          activeSearchTerm,
          sortBy as any
        );

        if (result.success && result.data) {
          setProducts((prev) =>
            currentPage === 1 || isSearchOrSort ? result.data! : [...prev, ...result.data!]
          );
          setHasMore(result.hasMore || false);
          if (currentPage === 1 || isSearchOrSort) {
            setPage(2);
          } else {
            setPage((prev) => prev + 1);
          }
        } else {
          throw new Error(result.error || "Failed to fetch products");
        }
      } catch (err) {
        const appError = handleNetworkError(err);
        setError(appError);
        logError(appError, "ProductsList.fetchProducts");
      } finally {
        setLoading(false);
        setLoadingMore(false);
        setRefreshing(false);
        setSearchLoading(false);
      }
    },
    [user, activeSearchTerm, sortBy, loading, loadingMore]
  );

  // Initial load
  useEffect(() => {
    if (user) {
      setPage(1);
      setProducts([]);
      setLoading(true);
      fetchProducts(1, false, false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  // Search and sort changes
  useEffect(() => {
    if (user) {
      setPage(1);
      setProducts([]);
      setSearchLoading(true);
      fetchProducts(1, false, true); // Mark as search/sort operation
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeSearchTerm, sortBy]);

  const handleRefresh = () => {
    setPage(1);
    fetchProducts(1, true, false);
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore && !refreshing) {
      fetchProducts(page, false, false);
    }
  };

  const handleDelete = async (productId: string) => {
    try {
      const result = await deleteProductService(productId);
      if (result.success) {
        setProducts((prev) =>
          prev.filter((product) => product.id !== productId)
        );
      } else {
        showErrorAlert("Error", result.error || "Failed to delete product");
      }
    } catch (err) {
      showErrorAlert("Error", "Failed to delete product. Please try again.");
      logError(err as Error, "ProductsList.handleDelete");
    }
  };

  const handleSearchSubmit = () => {
    setActiveSearchTerm(searchTerm);
    onSearch();
  };

  const handleClearSearch = () => {
    setActiveSearchTerm("");
    onClearSearch();
  };

  const renderSearchHeader = () => (
    <View style={styles.searchContainer}>
      <View style={styles.searchInputContainer}>
        <Search size={20} color={theme.colors.textSecondary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search products..."
          placeholderTextColor={theme.colors.textSecondary}
          value={searchTerm}
          onChangeText={onSearchChange}
          onSubmitEditing={handleSearchSubmit}
          returnKeyType="search"
        />
        {searchTerm.length > 0 && (
          <TouchableOpacity
            onPress={handleClearSearch}
            style={styles.clearSearchButton}
          >
            <X size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          onPress={handleSearchSubmit}
          style={styles.searchIconButton}
        >
          <Search size={16} color={theme.colors.foreground} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderActionsHeader = () => (
    <View style={styles.actionsContainer}>
      <TouchableOpacity style={styles.addButton} onPress={onAddPress}>
        <Plus size={16} color={theme.colors.background} />
        <Text style={styles.addButtonText}>Add Product</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.sortButton}
        onPress={onSortPress}
      >
        <SortAsc size={20} color={theme.colors.foreground} />
      </TouchableOpacity>
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoadingContainer}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  };

  if ((loading && products.length === 0) || searchLoading || refreshing) {
    return <ProductsModalSkeleton />;
  }

  if (error && products.length === 0) {
    return (
      <ErrorState
        title={error.title}
        message={error.message}
        onRetry={handleRefresh}
      />
    );
  }

  const renderSeparator = () => <View style={styles.separator} />;

  return (
    <FlatList
      data={products}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <ProductCard
          product={item}
          onEdit={onEdit}
          onDelete={handleDelete}
          theme={theme}
        />
      )}
      ItemSeparatorComponent={renderSeparator}
      ListHeaderComponent={
        <View>
          {renderSearchHeader()}
          {renderActionsHeader()}
        </View>
      }
      contentContainerStyle={styles.listContentContainer}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
      ListEmptyComponent={
        !loading && !searchLoading ? (
          <View style={styles.emptyStateContainer}>
            <View style={styles.emptyStateIcon}>
              <Package size={48} color={theme.colors.mutedForeground} />
            </View>
            <Text style={styles.emptyStateTitle}>
              {activeSearchTerm ? "No products found" : "No products yet"}
            </Text>
            <Text style={styles.emptyStateMessage}>
              {activeSearchTerm
                ? `No products match "${activeSearchTerm}". Try adjusting your search.`
                : "Start building your catalog by adding your first product or service."}
            </Text>
          </View>
        ) : null
      }
    />
  );
};

export default ProductsList;
