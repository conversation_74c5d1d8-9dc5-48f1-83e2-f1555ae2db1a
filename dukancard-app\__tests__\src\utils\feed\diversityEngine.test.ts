import { applyDiversityRules, groupPostsByAuthor, roundRobinDistribution } from '@/src/utils/feed/diversityEngine';
import { createMockUnifiedPost } from '../../../utils/mockData';

describe('diversityEngine', () => {
  describe('applyDiversityRules', () => {
    it('should return an empty array for empty input', () => {
      expect(applyDiversityRules([])).toEqual([]);
    });

    it('should return the same array for a single post', () => {
      const posts = [createMockUnifiedPost({ id: '1', author_id: 'A', created_at: '2023-01-01' })];
      expect(applyDiversityRules(posts)).toEqual(posts);
    });

    it('should prevent consecutive posts from the same author by default (maxConsecutiveFromSameAuthor = 1)', () => {
      const posts = [
        createMockUnifiedPost({ id: '1', author_id: 'A', created_at: '2023-01-01' }),
        createMockUnifiedPost({ id: '2', author_id: 'A', created_at: '2023-01-02' }),
        createMockUnifiedPost({ id: '3', author_id: 'B', created_at: '2023-01-03' }),
        createMockUnifiedPost({ id: '4', author_id: 'A', created_at: '2023-01-04' }),
        createMockUnifiedPost({ id: '5', author_id: 'B', created_at: '2023-01-05' }),
      ];
      const expectedOrder = [
        expect.objectContaining({ id: '1', author_id: 'A', created_at: '2023-01-01' }),
        expect.objectContaining({ id: '3', author_id: 'B', created_at: '2023-01-03' }),
        expect.objectContaining({ id: '2', author_id: 'A', created_at: '2023-01-02' }),
        expect.objectContaining({ id: '5', author_id: 'B', created_at: '2023-01-05' }),
        expect.objectContaining({ id: '4', author_id: 'A', created_at: '2023-01-04' }),
      ];
      expect(applyDiversityRules(posts)).toEqual(expectedOrder);
    });

    it('should allow more consecutive posts when maxConsecutiveFromSameAuthor is higher', () => {
      const posts = [
        createMockUnifiedPost({ id: '1', author_id: 'A', created_at: '2023-01-01' }),
        createMockUnifiedPost({ id: '2', author_id: 'A', created_at: '2023-01-02' }),
        createMockUnifiedPost({ id: '3', author_id: 'A', created_at: '2023-01-03' }),
        createMockUnifiedPost({ id: '4', author_id: 'B', created_at: '2023-01-04' }),
      ];
      const expectedOrder = [
        expect.objectContaining({ id: '1', author_id: 'A', created_at: '2023-01-01' }),
        expect.objectContaining({ id: '2', author_id: 'A', created_at: '2023-01-02' }),
        expect.objectContaining({ id: '4', author_id: 'B', created_at: '2023-01-04' }),
        expect.objectContaining({ id: '3', author_id: 'A', created_at: '2023-01-03' }),
      ];
      expect(applyDiversityRules(posts, { maxConsecutiveFromSameAuthor: 2 })).toEqual(expectedOrder);
    });

    it('should handle cases where diversity is impossible', () => {
      const posts = [
        createMockUnifiedPost({ id: '1', author_id: 'A', created_at: '2023-01-01' }),
        createMockUnifiedPost({ id: '2', author_id: 'A', created_at: '2023-01-02' }),
        createMockUnifiedPost({ id: '3', author_id: 'A', created_at: '2023-01-03' }),
      ];
      expect(applyDiversityRules(posts)).toEqual(posts); // Should return as is
    });
  });

  describe('groupPostsByAuthor', () => {
    it('should return an empty map for empty input', () => {
      expect(groupPostsByAuthor([])).toEqual(new Map());
    });

    it('should group posts by author and sort chronologically', () => {
      const posts = [
        createMockUnifiedPost({ id: '1', author_id: 'A', created_at: '2023-01-01T10:00:00Z' }),
        createMockUnifiedPost({ id: '2', author_id: 'B', created_at: '2023-01-01T11:00:00Z' }),
        createMockUnifiedPost({ id: '3', author_id: 'A', created_at: '2023-01-01T12:00:00Z' }),
        createMockUnifiedPost({ id: '4', author_id: 'C', created_at: '2023-01-01T09:00:00Z' }),
      ];
      const grouped = groupPostsByAuthor(posts);
      expect(grouped.get('A')).toEqual([
        expect.objectContaining({ id: '3', author_id: 'A', created_at: '2023-01-01T12:00:00Z' }),
        expect.objectContaining({ id: '1', author_id: 'A', created_at: '2023-01-01T10:00:00Z' }),
      ]);
      expect(grouped.get('B')).toEqual([
        expect.objectContaining({ id: '2', author_id: 'B', created_at: '2023-01-01T11:00:00Z' }),
      ]);
      expect(grouped.get('C')).toEqual([
        expect.objectContaining({ id: '4', author_id: 'C', created_at: '2023-01-01T09:00:00Z' }),
      ]);
    });
  });

  describe('roundRobinDistribution', () => {
    it('should return an empty array for empty map', () => {
      expect(roundRobinDistribution(new Map())).toEqual([]);
    });

    it('should distribute posts in a round-robin fashion', () => {
      const groupedPosts = new Map([
        ['A', [createMockUnifiedPost({ id: 'A1', author_id: 'A', created_at: '' }), createMockUnifiedPost({ id: 'A2', author_id: 'A', created_at: '' })]],
        ['B', [createMockUnifiedPost({ id: 'B1', author_id: 'B', created_at: '' }), createMockUnifiedPost({ id: 'B2', author_id: 'B', created_at: '' }), createMockUnifiedPost({ id: 'B3', author_id: 'B', created_at: '' })]],
        ['C', [createMockUnifiedPost({ id: 'C1', author_id: 'C', created_at: '' })]],
      ]);
      const expectedOrder = [
        expect.objectContaining({ id: 'A1', author_id: 'A', created_at: '' }),
        expect.objectContaining({ id: 'B1', author_id: 'B', created_at: '' }),
        expect.objectContaining({ id: 'C1', author_id: 'C', created_at: '' }),
        expect.objectContaining({ id: 'A2', author_id: 'A', created_at: '' }),
        expect.objectContaining({ id: 'B2', author_id: 'B', created_at: '' }),
        expect.objectContaining({ id: 'B3', author_id: 'B', created_at: '' }),
      ];
      expect(roundRobinDistribution(groupedPosts)).toEqual(expectedOrder);
    });

    it('should handle single author correctly', () => {
      const groupedPosts = new Map([
        ['A', [createMockUnifiedPost({ id: 'A1', author_id: 'A', created_at: '' }), createMockUnifiedPost({ id: 'A2', author_id: 'A', created_at: '' })]],
      ]);
      expect(roundRobinDistribution(groupedPosts)).toEqual([
        expect.objectContaining({ id: 'A1', author_id: 'A', created_at: '' }),
        expect.objectContaining({ id: 'A2', author_id: 'A', created_at: '' }),
      ]);
    });
  });
});