"use server";

import { createClient } from "@/utils/supabase/server";
import { z } from "zod";
import { EmailOTPSchema, VerifyOTPSchema, MobilePasswordLoginSchema } from "@/lib/schemas/authSchemas";
import { handleSupabaseAuthError, isEmailRateLimitError } from "@/lib/utils/supabaseErrorHandler";

// Validate email format
function validateEmail(email: string): { isValid: boolean; message?: string } {
  if (!email) {
    return { isValid: false, message: 'Email is required' };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'Please enter a valid email address' };
  }
  
  return { isValid: true };
}

// Validate OTP format
function validateOTP(otp: string): { isValid: boolean; message?: string } {
  if (!otp) {
    return { isValid: false, message: 'OTP is required' };
  }
  
  if (otp.length !== 6) {
    return { isValid: false, message: 'OTP must be 6 digits' };
  }
  
  if (!/^\d{6}$/.test(otp)) {
    return { isValid: false, message: 'OTP must contain only numbers' };
  }
  
  return { isValid: true };
}

// Send OTP to email
export async function sendOTP(values: z.infer<typeof EmailOTPSchema>) {
  const { email } = values;
  const emailValidation = validateEmail(email);
  if (!emailValidation.isValid) {
    return {
      success: false,
      error: emailValidation.message,
    };
  }

  try {
    const supabase = await createClient();
    const { error } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        // Set shouldCreateUser to true to allow new user registration
        shouldCreateUser: true,
        data: {
          auth_type: "email",
        },
      },
    });

    if (error) {
      if (isEmailRateLimitError(error)) {
        return {
          success: false,
          error: "Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.",
          isConfigurationError: true,
        };
      }

      return {
        success: false,
        error: handleSupabaseAuthError(error),
      };
    }

    return {
      success: true,
      message: "OTP sent to your email address. Please check your inbox.",
    };
  } catch (error) {
    if (isEmailRateLimitError(error as Error)) {
      return {
        success: false,
        error: "Email rate limit exceeded. This indicates a configuration issue with OTP authentication. Please contact support.",
        isConfigurationError: true,
      };
    }

    return {
      success: false,
      error: handleSupabaseAuthError(error as Error),
    };
  }
}

// Verify OTP and sign in
export async function verifyOTP(values: z.infer<typeof VerifyOTPSchema>) {
  const { email, otp } = values;
  const otpValidation = validateOTP(otp);
  if (!otpValidation.isValid) {
    return {
      success: false,
      error: otpValidation.message,
    };
  }
  
  const supabase = await createClient();

  try {
    const { data, error } = await supabase.auth.verifyOtp({
      email: email,
      token: otp,
      type: 'email',
    });

    if (error) {
      return {
        success: false,
        error: handleSupabaseAuthError(error),
      };
    }

    return {
      success: true,
      data: data,
      message: "Successfully signed in!",
    };
  } catch (error) {
    return {
      success: false,
      error: handleSupabaseAuthError(error as Error),
    };
  }
}

// Mobile + password login
export async function loginWithMobilePassword(values: z.infer<typeof MobilePasswordLoginSchema>) {
  const validatedFields = MobilePasswordLoginSchema.safeParse(values);

  if (!validatedFields.success) {
    return {
      success: false,
      error: "Invalid mobile number or password format",
    };
  }

  const { mobile, password } = validatedFields.data;
  const supabase = await createClient();

  try {
    // Format mobile number with +91 prefix
    const phoneNumber = `+91${mobile}`;

    const { data, error } = await supabase.auth.signInWithPassword({
      phone: phoneNumber,
      password: password,
    });

    if (error) {
      return {
        success: false,
        error: handleSupabaseAuthError(error),
      };
    }

    return {
      success: true,
      data: data,
      message: "Successfully signed in!",
    };
  } catch (error) {
    return {
      success: false,
      error: handleSupabaseAuthError(error as Error),
    };
  }
}
