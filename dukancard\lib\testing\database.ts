/**
 * <PERSON><PERSON><PERSON>ASE TEST UTILITIES
 * 
 * Clean utilities for database operations during testing
 */

import { createClient } from '@/utils/supabase/server';
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";
import type { DatabaseState, SubscriptionStatus, PlanType, PlanCycle, ProcessedWebhookEvent } from './types';

export class DatabaseTestUtils {
  private supabase = createClient();

  /**
   * Get current subscription state
   */
  async getSubscriptionState(businessId: string): Promise<DatabaseState> {
    const { data: subscription } = await this.supabase
      .from('payment_subscriptions')
      .select('subscription_status, plan_id, last_webhook_timestamp')
      .eq('business_profile_id', businessId)
      .single();

    const { data: profile } = await this.supabase
      .from('business_profiles')
      .select('has_active_subscription')
      .eq('id', businessId)
      .single();

    return {
      subscriptionStatus: subscription?.subscription_status || 'unknown',
      planId: subscription?.plan_id || 'unknown',
      hasActiveSubscription: profile?.has_active_subscription || false,
      lastWebhookTimestamp: subscription?.last_webhook_timestamp
    };
  }

  /**
   * Setup test subscription with specific state
   */
  async setupTestSubscription(
    businessId: string,
    subscriptionId: string,
    status: SubscriptionStatus,
    planId: PlanType = 'growth',
    planCycle: PlanCycle = 'monthly'
  ): Promise<void> {
    // Update subscription
    await this.supabase
      .from('payment_subscriptions')
      .update({
        razorpay_subscription_id: subscriptionId,
        plan_id: planId,
        plan_cycle: planCycle,
        subscription_status: status,
        updated_at: new Date().toISOString()
      })
      .eq('business_profile_id', businessId);

    // Update business profile based on plan
    const hasActiveSubscription = planId !== 'free' && status === 'active';
    await this.supabase
      .from('business_profiles')
      .update({
        has_active_subscription: hasActiveSubscription,
        updated_at: new Date().toISOString()
      })
      .eq('id', businessId);
  }

  /**
   * Clean up test data
   */
  async cleanupTestData(businessId: string): Promise<void> {
    // Reset subscription to clean trial state
    await this.supabase
      .from('payment_subscriptions')
      .update({
        razorpay_subscription_id: null,
        razorpay_customer_id: null,
        plan_id: 'free',
        plan_cycle: 'monthly',
        subscription_status: 'trial',
        subscription_start_date: null,
        subscription_expiry_time: null,
        last_payment_id: null,
        last_payment_date: null,
        cancellation_requested_at: null,
        cancelled_at: null,
        updated_at: new Date().toISOString()
      })
      .eq('business_profile_id', businessId);

    // Reset business profile
    await this.supabase
      .from('business_profiles')
      .update({
        has_active_subscription: false,
        trial_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', businessId);

    // Clean up test webhook events
    await this.supabase
      .from('processed_webhook_events')
      .delete()
      .like('event_id', 'test_%');
  }

  /**
   * Check if webhook event was processed
   */
  async isWebhookProcessed(eventId: string): Promise<boolean> {
    const { data } = await this.supabase
      .from('processed_webhook_events')
      .select('event_id')
      .eq('event_id', eventId)
      .single();

    return !!data;
  }

  /**
   * Get processed webhook events for a subscription
   */
  async getProcessedWebhookEvents(subscriptionId: string): Promise<ProcessedWebhookEvent[]> {
    const { data } = await this.supabase
      .from('processed_webhook_events')
      .select('*')
      .eq('entity_id', subscriptionId)
      .order('created_at', { ascending: false });

    return data || [];
  }

  /**
   * Verify business profile exists
   */
  async verifyBusinessProfile(businessId: string): Promise<boolean> {
    const { data } = await this.supabase
      .from('business_profiles')
      .select('id')
      .eq('id', businessId)
      .single();

    return !!data;
  }

  /**
   * Create test webhook event record
   */
  async createTestWebhookEvent(
    eventId: string,
    eventType: string,
    entityId: string,
    status: 'pending' | 'processed' | 'failed' = 'processed'
  ): Promise<void> {
    await this.supabase
      .from('processed_webhook_events')
      .insert({
        event_id: eventId,
        event_type: eventType,
        entity_type: 'subscription',
        entity_id: entityId,
        status,
        payload: { test: true },
        created_at: new Date().toISOString()
      });
  }
}
