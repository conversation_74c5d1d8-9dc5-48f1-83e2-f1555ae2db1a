import { Tables } from "@dukancard-types/supabase";

// Feed filter types
export type FeedFilterType =
  | "smart"
  | "subscribed"
  | "locality"
  | "pincode"
  | "city"
  | "state"
  | "all";

// Feed query parameters
export interface FeedQueryParams {
  filter?: FeedFilterType;
  page?: number;
  limit?: number;
  city_slug?: string;
  state_slug?: string;
  locality_slug?: string;
  pincode?: string;
}

// Post with business profile information
export interface PostWithBusinessProfile {
  id: string;
  content: string;
  image_url: string | null;
  created_at: string;
  updated_at: string;
  business_id: string;
  product_ids: string[];
  mentioned_business_ids: string[];
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  pincode: string | null;
  business_profiles: {
    business_name: string;
    business_slug: string;
    logo_url: string | null;
    phone: string | null;
    whatsapp_number: string | null;
    plan: string | null;
  } | null;
}

// Customer post type
export interface CustomerPost extends Tables<'customer_posts'> {
  customer_profiles: {
    name: string;
    avatar_url: string | null;
  } | null;
}

// Post address information
export interface PostAddress {
  locality: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
}

// Post interaction types
export interface PostInteraction {
  id: string;
  post_id: string;
  user_id: string;
  interaction_type: "like" | "comment" | "share";
  created_at: string;
}

// Comment type
export interface PostComment {
  id: string;
  post_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  author_name: string | null;
  author_avatar: string | null;
}

// Post creation state for optimistic updates
export interface PostCreationState {
  isCreating: boolean;
  tempPost?: any;
}

// Enhanced feed response with creation handling
export interface EnhancedFeedResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    items: any[];
    totalCount: number;
    hasMore: boolean;
  };
}
