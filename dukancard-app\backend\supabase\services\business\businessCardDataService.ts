import { supabase } from "@/lib/supabase";
import { applyGalleryPlanLimits } from "@/src/utils/galleryLimits";
import { Tables } from "../../../../src/types/supabase";
import { BusinessCardData, BusinessGalleryImage, EnrichedBusinessReview } from "@/src/types/discovery";

export type ProductsServices = Tables<"products_services">;

export type BusinessReview = EnrichedBusinessReview;
export type BusinessProduct = ProductsServices;





/**
 * Fetch business products for public card view
 */
export async function fetchBusinessProducts(
  businessId: string,
  limit: number = 6
): Promise<{ success: boolean; data?: Tables<"products_services">[]; error?: string }> {
  try {
    const { data, error } = await supabase
      .from("products_services")
      .select("*")
      .eq("business_id", businessId)
      .eq("is_available", true)
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching business products:", error);
      return { success: false, error: "Failed to fetch products" };
    }

    return { success: true, data: data || [] };
  } catch (error) {
    console.error("Exception fetching business products:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Fetch business reviews for public card view
 */
export async function fetchBusinessReviews(
  businessId: string,
  limit: number = 5
): Promise<{
  success: boolean;
  data?: EnrichedBusinessReview[];
  error?: string;
}> {
  try {
    // First, get the reviews without trying to join with customer_profiles
    const { data: reviewsData, error } = await supabase
      .from("ratings_reviews")
      .select(
        `
        id,
        rating,
        review_text,
        created_at,
        updated_at,
        user_id
      `
      )
      .eq("business_profile_id", businessId)
      .not("review_text", "is", null) // Only get reviews with text
      .order("created_at", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching business reviews:", error);
      return { success: false, error: "Failed to fetch reviews" };
    }

    if (!reviewsData || reviewsData.length === 0) {
      return { success: true, data: [] };
    }

    // Get reviewer information for the reviews
    const userIds = reviewsData.map((review) => review.user_id);

    // Fetch both customer and business profiles using regular client with public read access
    const [customerData, businessData] = await Promise.all([
      supabase
        .from("customer_profiles")
        .select("id, name, avatar_url")
        .in("id", userIds),
      supabase
        .from("business_profiles")
        .select("id, business_name, business_slug, logo_url")
        .in("id", userIds),
    ]);

    // Combine review data with reviewer information
    const reviewsWithReviewerInfo: EnrichedBusinessReview[] = reviewsData.map(
      (review) => {
        // Check if reviewer is a customer
        const customer = customerData.data?.find(
          (c) => c.id === review.user_id
        );
        if (customer) {
          return {
            ...review,
            reviewer_type: "customer" as const,
            reviewer_name: customer.name || "Anonymous Customer",
            reviewer_avatar: customer.avatar_url || null,
            customer_name: customer.name || "Anonymous Customer",
            customer_avatar: customer.avatar_url || null,
          };
        }

        // Check if reviewer is a business
        const business = businessData.data?.find(
          (b) => b.id === review.user_id
        );
        if (business) {
          return {
            ...review,
            reviewer_type: "business" as const,
            reviewer_name: business.business_name || "Anonymous Business",
            reviewer_avatar: business.logo_url || null,
            reviewer_slug: business.business_slug || null,
            customer_name: business.business_name || "Anonymous Business",
            customer_avatar: business.logo_url || null,
          };
        }

        // Fallback for unknown reviewer type
        return {
          ...review,
          reviewer_type: "customer" as const,
          reviewer_name: "Anonymous",
          reviewer_avatar: null,
          customer_name: "Anonymous",
          customer_avatar: null,
        };
      }
    );

    return { success: true, data: reviewsWithReviewerInfo };
  } catch (error) {
    console.error("Exception fetching business reviews:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Fetch business gallery images with plan limits applied
 */
export async function fetchBusinessGallery(
  businessSlug: string,
  limit: number = 8
): Promise<{
  success: boolean;
  data?: BusinessGalleryImage[];
  error?: string;
}> {
  try {
    // First get the business profile to extract gallery from JSONB field and get business ID
    const { data: businessData, error: businessError } = await supabase
      .from("business_profiles")
      .select("id, gallery")
      .eq("business_slug", businessSlug)
      .single();

    if (businessError) {
      console.error("Error fetching business for gallery:", businessError);
      return { success: false, error: "Failed to fetch gallery" };
    }

    if (!businessData?.gallery) {
      return { success: true, data: [] };
    }

    // Get the business plan from payment_subscriptions - user can read their own subscription
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("plan_id")
      .eq("business_profile_id", businessData.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
      // Continue with default free plan if there's an error
    }

    // Default to free plan if no subscription found
    const planId = subscriptionData?.plan_id || "free";

    // Parse gallery JSONB and format for display
    const gallery = Array.isArray(businessData.gallery)
      ? businessData.gallery
      : [];

    // Sort by created_at in descending order (newest first) if available
    gallery.sort((a: any, b: any) => {
      const dateA = new Date(a.created_at || 0).getTime();
      const dateB = new Date(b.created_at || 0).getTime();
      return dateB - dateA;
    });

    // Apply plan limits first, then display limit
    const planLimitedGallery = applyGalleryPlanLimits(gallery, planId);
    const finalLimit = Math.min(planLimitedGallery.length, limit);

    const formattedGallery: BusinessGalleryImage[] = planLimitedGallery
      .slice(0, finalLimit)
      .map((item: any, index: number) => ({
        id: item.id || `gallery-${index}`,
        url: item.url || item,
        caption: item.caption,
        order_index: item.order_index || index,
      }));

    return { success: true, data: formattedGallery };
  } catch (error) {
    console.error("Exception fetching business gallery:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Fetch review statistics for a business
 */
export async function fetchReviewStats(businessId: string): Promise<{
  success: boolean;
  data?: BusinessCardData["reviewStats"];
  error?: string;
}> {
  try {
    const { data, error } = await supabase
      .from("ratings_reviews")
      .select("rating")
      .eq("business_profile_id", businessId);

    if (error) {
      console.error("Error fetching review stats:", error);
      return { success: false, error: "Failed to fetch review statistics" };
    }

    const reviews = data || [];
    const totalReviews = reviews.length;

    if (totalReviews === 0) {
      return {
        success: true,
        data: {
          totalReviews: 0,
          averageRating: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        },
      };
    }

    const averageRating =
      reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;

    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    reviews.forEach((review) => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++;
    });

    return {
      success: true,
      data: {
        totalReviews,
        averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
        ratingDistribution,
      },
    };
  } catch (error) {
    console.error("Exception fetching review stats:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Product sort options
 */
export type ProductSortOption =
  | "newest"
  | "price_low"
  | "price_high"
  | "name_asc"
  | "name_desc";

/**
 * Review sort options
 */
export type ReviewSortOption =
  | "newest"
  | "oldest"
  | "highest_rating"
  | "lowest_rating";

/**
 * Fetch business products with pagination, search, and sorting
 */
export async function fetchBusinessProductsPaginated(
  businessId: string,
  page: number = 1,
  limit: number = 6,
  searchTerm?: string,
  sortBy: ProductSortOption = "newest"
): Promise<{
  success: boolean;
  data?: Tables<"products_services">[];
  hasMore?: boolean;
  error?: string;
}> {
  try {
    const offset = (page - 1) * limit;

    // First, get the total count to check if offset is valid
    let countQuery = supabase
      .from("products_services")
      .select("*", { count: "exact", head: true })
      .eq("business_id", businessId)
      .eq("is_available", true);

    // Add search filter if provided
    if (searchTerm && searchTerm.trim()) {
      countQuery = countQuery.ilike("name", `%${searchTerm.trim()}%`);
    }

    const { count: totalCount, error: countError } = await countQuery;

    if (countError) {
      console.error("Error getting product count:", countError);
      return { success: false, error: "Failed to get product count" };
    }

    // Check if offset exceeds available rows
    if (totalCount !== null && offset >= totalCount) {
      return {
        success: true,
        data: [],
        hasMore: false,
      };
    }

    let dataQuery = supabase
      .from("products_services")
      .select(
        `
        *
      `
      )
      .eq("business_id", businessId)
      .eq("is_available", true);

    // Apply sorting
    switch (sortBy) {
      case "newest":
        dataQuery = dataQuery.order("created_at", { ascending: false });
        break;
      case "price_low":
        // Sort by discounted_price first, then base_price
        dataQuery = dataQuery
          .order("discounted_price", { ascending: true })
          .order("base_price", { ascending: true });
        break;
      case "price_high":
        // Sort by discounted_price first, then base_price
        dataQuery = dataQuery
          .order("discounted_price", { ascending: false })
          .order("base_price", { ascending: false });
        break;
      case "name_asc":
        dataQuery = dataQuery.order("name", { ascending: true });
        break;
      case "name_desc":
        dataQuery = dataQuery.order("name", { ascending: false });
        break;
      default:
        dataQuery = dataQuery.order("created_at", { ascending: false });
    }

    // Add search filter if provided
    if (searchTerm && searchTerm.trim()) {
      dataQuery = dataQuery.ilike("name", `%${searchTerm.trim()}%`);
    }

    const { data, error } = await dataQuery.range(offset, offset + limit - 1);

    if (error) {
      console.error("Error fetching business products:", error);
      return { success: false, error: "Failed to fetch products" };
    }

    const hasMore = totalCount ? offset + limit < totalCount : false;

    return {
      success: true,
      data: data as Tables<"products_services">[],
      hasMore,
    };
  } catch (error) {
    console.error("Exception fetching business products:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Fetch business reviews with pagination and sorting
 */
export async function fetchBusinessReviewsPaginated(
  businessId: string,
  page: number = 1,
  limit: number = 5,
  sortBy: ReviewSortOption = "newest"
): Promise<{
  success: boolean;
  data?: EnrichedBusinessReview[];
  hasMore?: boolean;
  error?: string;
}> {
  try {
    const offset = (page - 1) * limit;

    // First, get the total count to check if offset is valid
    const { count: totalCount, error: countError } = await supabase
      .from("ratings_reviews")
      .select("*", { count: "exact", head: true })
      .eq("business_profile_id", businessId)
      .not("review_text", "is", null);

    if (countError) {
      console.error("Error getting review count:", countError);
      return { success: false, error: "Failed to get review count" };
    }

    // Check if offset exceeds available rows
    if (totalCount !== null && offset >= totalCount) {
      return {
        success: true,
        data: [],
        hasMore: false,
      };
    }

    // Get the reviews without trying to join with customer_profiles
    let reviewsQuery = supabase
      .from("ratings_reviews")
      .select(
        `
        id,
        rating,
        review_text,
        created_at,
        updated_at,
        user_id
      `
      )
      .eq("business_profile_id", businessId)
      .not("review_text", "is", null); // Only get reviews with text

    // Apply sorting
    switch (sortBy) {
      case "oldest":
        reviewsQuery = reviewsQuery.order("created_at", { ascending: true });
        break;
      case "highest_rating":
        reviewsQuery = reviewsQuery.order("rating", { ascending: false });
        break;
      case "lowest_rating":
        reviewsQuery = reviewsQuery.order("rating", { ascending: true });
        break;
      case "newest":
      default:
        reviewsQuery = reviewsQuery.order("created_at", { ascending: false });
    }

    const { data: reviewsData, error } = await reviewsQuery.range(
      offset,
      offset + limit - 1
    );

    if (error) {
      console.error("Error fetching business reviews:", error);
      return { success: false, error: "Failed to fetch reviews" };
    }

    if (!reviewsData || reviewsData.length === 0) {
      return { success: true, data: [], hasMore: false };
    }

    // Get reviewer information for the reviews
    const userIds = reviewsData.map((review: any) => review.user_id);

    // Fetch both customer and business profiles using regular client with public read access
    const [customerData, businessData] = await Promise.all([
      supabase
        .from("customer_profiles")
        .select("id, name, avatar_url")
        .in("id", userIds),
      supabase
        .from("business_profiles")
        .select("id, business_name, business_slug, logo_url")
        .in("id", userIds),
    ]);

    // Combine review data with reviewer information
    const reviewsWithReviewerInfo: EnrichedBusinessReview[] = reviewsData.map(
      (review: any) => {
        // Check if reviewer is a customer
        const customer = customerData.data?.find(
          (c) => c.id === review.user_id
        );
        if (customer) {
          return {
            ...review,
            reviewer_type: "customer" as const,
            reviewer_name: customer.name || "Anonymous Customer",
            reviewer_avatar: customer.avatar_url || null,
            customer_name: customer.name || "Anonymous Customer",
            customer_avatar: customer.avatar_url || null,
          };
        }

        // Check if reviewer is a business
        const business = businessData.data?.find(
          (b) => b.id === review.user_id
        );
        if (business) {
          return {
            ...review,
            reviewer_type: "business" as const,
            reviewer_name: business.business_name || "Anonymous Business",
            reviewer_avatar: business.logo_url || null,
            reviewer_slug: business.business_slug || null,
            customer_name: business.business_name || "Anonymous Business",
            customer_avatar: business.logo_url || null,
          };
        }

        // Fallback for unknown reviewer type
        return {
          ...review,
          reviewer_type: "customer" as const,
          reviewer_name: "Anonymous",
          reviewer_avatar: null,
          customer_name: "Anonymous",
          customer_avatar: null,
        };
      }
    );

    const hasMore = totalCount ? offset + limit < totalCount : false;

    return {
      success: true,
      data: reviewsWithReviewerInfo,
      hasMore,
    };
  } catch (error) {
    console.error("Exception fetching business reviews:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Fetch all business card data in one call
 */
export async function fetchBusinessCardData(
  businessId: string,
  businessSlug: string
): Promise<{ success: boolean; data?: BusinessCardData; error?: string }> {
  try {
    const [productsResult, reviewsResult, galleryResult, statsResult] =
      await Promise.all([
        fetchBusinessProducts(businessId, 10),
        fetchBusinessReviews(businessId, 10),
        fetchBusinessGallery(businessSlug, 8),
        fetchReviewStats(businessId),
      ]);

    // Check if any critical operations failed
    if (
      !productsResult.success ||
      !reviewsResult.success ||
      !galleryResult.success ||
      !statsResult.success
    ) {
      const errors = [
        productsResult.error,
        reviewsResult.error,
        galleryResult.error,
        statsResult.error,
      ].filter(Boolean);

      return { success: false, error: errors.join(", ") };
    }

    return {
      success: true,
      data: {
        products: productsResult.data || [],
        reviews: reviewsResult.data || [],
        gallery: galleryResult.data || [],
        reviewStats: statsResult.data || {
          totalReviews: 0,
          averageRating: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        },
      },
    };
  } catch (error) {
    console.error("Exception fetching business card data:", error);
    return { success: false, error: "Failed to load business information" };
  }
}
