import { supabase } from "../../../config/supabase";
import { Tables } from "../../../types/supabase";

export type BusinessProfiles = Tables<"business_profiles">;
import { BusinessSortBy } from "../types";

export type BusinessProfilePublicData = Omit<
  BusinessProfiles,
  "user_id" | "payment_subscriptions"
> & {
  subscription_status: string | null;
  plan_id: string | null;
};

/**
 * Apply sorting to a Supabase query based on the provided sort option
 */
export function applySorting(query: any, sortBy: BusinessSortBy): any {
  switch (sortBy) {
    case "name_asc":
      return query.order("business_name", { ascending: true });
    case "name_desc":
      return query.order("business_name", { ascending: false });
    case "created_asc":
      return query.order("created_at", { ascending: true });
    case "created_desc":
      return query.order("created_at", { ascending: false });
    case "likes_asc":
      return query.order("total_likes", { ascending: true });
    case "likes_desc":
      return query.order("total_likes", { ascending: false });
    case "subscriptions_asc":
      return query.order("total_subscriptions", { ascending: true });
    case "subscriptions_desc":
      return query.order("total_subscriptions", { ascending: false });
    case "rating_asc":
      return query.order("average_rating", { ascending: true });
    case "rating_desc":
      return query.order("average_rating", { ascending: false });
    default:
      return query.order("created_at", { ascending: false });
  }
}

/**
 * Get the current ISO timestamp
 */
export function getCurrentISOTimestamp(): string {
  return new Date().toISOString();
}

/**
 * Create a subscription map from subscription data
 */
export function createSubscriptionMap(
  subscriptionsData:
    | {
        business_profile_id: string;
        subscription_status: string | null;
        plan_id: string | null;
      }[]
    | null
) {
  const subscriptionMap = new Map<
    string,
    {
      subscription_status: string | null;
      plan_id: string | null;
    }
  >();

  if (subscriptionsData) {
    // Group by business_profile_id and take the most recent one
    subscriptionsData.forEach((sub) => {
      if (!subscriptionMap.has(sub.business_profile_id)) {
        subscriptionMap.set(sub.business_profile_id, {
          subscription_status: sub.subscription_status,
          plan_id: sub.plan_id,
        });
      }
    });
  }

  return subscriptionMap;
}

/**
 * Securely fetch all business profiles or search by name and/or location
 */
export async function getSecureBusinessProfiles(
  searchTerm?: string | null,
  pincode?: string | null,
  locality?: string | null,
  city?: string | null,
  page: number = 1,
  limit: number = 20,
  sortBy: BusinessSortBy = "created_desc",
  category?: string | null
): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  try {
    console.log("getSecureBusinessProfiles called with:", {
      searchTerm,
      pincode,
      locality,
      city,
      page,
      limit,
      sortBy,
      category,
    });
    const offset = (page - 1) * limit;

    // Define fields to select
    const businessFields = `
      id, business_name, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
      delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
      business_category, trial_end_date, created_at, updated_at, contact_email, established_year,
      custom_branding, custom_ads, latitude, longitude
    `;

    // Build the base query - only check for online status for businesses
    let countQuery = supabase
      .from("business_profiles")
      .select("id", { count: "exact" })
      .eq("status", "online");

    let businessQuery = supabase
      .from("business_profiles")
      .select(businessFields)
      .eq("status", "online");

    // Add search term filter if provided
    if (searchTerm) {
      const formattedSearchTerm = searchTerm.trim();
      if (formattedSearchTerm) {
        // Use ilike for case-insensitive search
        countQuery = countQuery.ilike(
          "business_name",
          `%${formattedSearchTerm}%`
        );
        businessQuery = businessQuery.ilike(
          "business_name",
          `%${formattedSearchTerm}%`
        );
      }
    }

    // Add city filter if provided
    if (city) {
      countQuery = countQuery.eq("city", city);
      businessQuery = businessQuery.eq("city", city);
    }

    // Add pincode filter if provided
    if (pincode) {
      countQuery = countQuery.eq("pincode", pincode);
      businessQuery = businessQuery.eq("pincode", pincode);
    }

    // Add locality filter if provided
    if (locality) {
      countQuery = countQuery.eq("locality", locality);
      businessQuery = businessQuery.eq("locality", locality);
    }

    // Add category filter if provided
    if (category && category.trim()) {
      countQuery = countQuery.eq("business_category", category.trim());
      businessQuery = businessQuery.eq("business_category", category.trim());
    }

    // Apply sorting
    businessQuery = applySorting(businessQuery, sortBy);

    // Apply pagination
    businessQuery = businessQuery.range(offset, offset + limit - 1);

    // Execute the queries
    const [countResult, dataResult] = await Promise.all([
      countQuery,
      businessQuery,
    ]);

    const { count, error: countError } = countResult;
    const { data, error: dataError } = dataResult;

    console.log("getSecureBusinessProfiles query results:", {
      count,
      data,
      countError,
      dataError,
    });

    if (countError) {
      console.error("Count Error:", countError);
      return { error: "Database error counting profiles." };
    }

    if (dataError) {
      console.error("Data Error:", dataError);
      return { error: "Database error fetching profiles." };
    }

    // If there are no profiles, return empty array
    if (!count || count === 0 || !data || data.length === 0) {
      return { data: [], count: 0 };
    }

    // No need to fetch subscription data for discovery
    const safeData: BusinessProfilePublicData[] = data.map(
      (profile: Record<string, unknown>) => {
        return {
          ...profile,
          // Add missing fields with default values
          has_active_subscription: false, // Default to false
          total_visits: 0,
          today_visits: 0,
          yesterday_visits: 0,
          visits_7_days: 0,
          visits_30_days: 0,
          city_slug: null,
          state_slug: null,
          locality_slug: null,
          gallery: null,
          // Keep original latitude and longitude from database
          latitude: profile.latitude,
          longitude: profile.longitude,
          subscription_status: null,
          plan_id: null,
        } as BusinessProfilePublicData;
      }
    );

    console.log("getSecureBusinessProfiles returning:", { safeData, count });
    return { data: safeData, count };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfiles:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetch business profiles for discover page using the service role key
 */
export async function getSecureBusinessProfilesForDiscover(
  pincodes: string | string[],
  locality?: string | null,
  page: number = 1,
  limit: number = 10,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {
    return { error: "At least one pincode is required." };
  }

  // Convert single pincode to array for consistent handling
  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];

  try {
    console.log("getSecureBusinessProfilesForDiscover called with:", {
      pincodes,
      locality,
      page,
      limit,
      sortBy,
    });
    // Use the admin client with service role key to bypass RLS
    const offset = (page - 1) * limit;

    // Get total count of online businesses
    let countQuery = supabase
      .from("business_profiles")
      .select("id", { count: "exact" })
      .in("pincode", pincodeArray)
      .eq("status", "online");

    // Add locality filter if provided
    if (locality) {
      countQuery = countQuery.eq("locality", locality);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Count Error:", countError);
      return { error: "Database error counting profiles." };
    }

    // If there are no profiles, return empty array
    if (!count || count === 0) {
      return { data: [], count: 0 };
    }

    // Define fields to select
    const businessFields = `
      id, business_name, logo_url, member_name, title,
      address_line, city, state, pincode, locality, phone, instagram_url,
      facebook_url, whatsapp_number, about_bio, status, business_slug, theme_color,
      delivery_info, total_likes, total_subscriptions, average_rating, business_hours,
      business_category, trial_end_date, created_at, updated_at, contact_email, established_year,
      custom_branding, custom_ads, latitude, longitude
    `;

    // Build the query
    let businessQuery = supabase
      .from("business_profiles")
      .select(businessFields)
      .in("pincode", pincodeArray)
      .eq("status", "online");

    // Add locality filter if provided
    if (locality) {
      businessQuery = businessQuery.eq("locality", locality);
    }

    // Apply sorting
    businessQuery = applySorting(businessQuery, sortBy);

    // Apply pagination
    businessQuery = businessQuery.range(offset, offset + limit - 1);

    // Execute the query
    const { data, error } = await businessQuery;

    if (error) {
      console.error("Query Error:", error);
      return { error: "Database error fetching profiles." };
    }
    console.log("getSecureBusinessProfilesForDiscover query result:", {
      data,
      error,
    });

    // Filter out sensitive data before returning (no subscription data needed)
    const safeData: BusinessProfilePublicData[] = data.map((profile: any) => {
      return {
        ...profile,
        // Add missing fields with default values
        total_visits: 0,
        today_visits: 0,
        yesterday_visits: 0,
        visits_7_days: 0,
        visits_30_days: 0,
        city_slug: null,
        state_slug: null,
        locality_slug: null,
        gallery: null,
        // Keep original latitude and longitude from database
        latitude: profile.latitude,
        longitude: profile.longitude,
        subscription_status: null,
        plan_id: null,
      } as BusinessProfilePublicData;
    });

    console.log("getSecureBusinessProfilesForDiscover returning:", {
      count,
      businessCount: safeData.length,
    });
    return { data: safeData, count: count || 0 };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfilesForDiscover:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetch business profile IDs for discover page products using the service role key
 */
export async function getSecureBusinessProfileIdsForDiscover(
  pincodes: string | string[],
  locality?: string | null,
  sortBy: BusinessSortBy = "created_desc"
): Promise<{
  data?: string[];
  error?: string;
}> {
  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {
    return { error: "At least one pincode is required." };
  }

  // Convert single pincode to array for consistent handling
  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];

  try {
    console.log("getSecureBusinessProfileIdsForDiscover called with:", {
      pincodes,
      locality,
      sortBy,
    });
    // Build query for online businesses
    let validBusinessQuery = supabase
      .from("business_profiles")
      .select("id")
      .in("pincode", pincodeArray)
      .eq("status", "online");

    // Add locality filter if provided
    if (locality) {
      validBusinessQuery = validBusinessQuery.eq("locality", locality);
    }

    // Apply sorting
    validBusinessQuery = applySorting(validBusinessQuery, sortBy);

    // Execute the query
    const { data, error } = await validBusinessQuery;

    if (error) {
      console.error("Query Error:", error);
      return { error: "Database error fetching profile IDs." };
    }
    console.log("getSecureBusinessProfileIdsForDiscover query result:", {
      data,
      error,
    });

    // We only need the IDs for this function, so we don't need to fetch subscription data
    // Just return the IDs
    const safeData = data.map((profile) => profile.id);

    return { data: safeData };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfileIdsForDiscover:", e);
    return { error: "An unexpected error occurred." };
  }
}
