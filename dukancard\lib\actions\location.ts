"use server";

import { createClient } from "@/utils/supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";


// --- Pincode Lookup Action ---
export async function getPincodeDetails(pincode: string): Promise<{
  data?: {
    city: string;
    state: string;
    localities: string[];
  };
  city?: string;
  state?: string;
  localities?: string[];
  error?: string;
}> {
  if (!pincode || !/^\d{6}$/.test(pincode)) {
    return { error: "Invalid Pincode format." };
  }

  const supabase = await createClient();
  try {
    // First get city and state from pincodes table
    const { data: pincodeData, error: pincodeError } = await supabase
      .from("pincodes")
      .select("OfficeName, DivisionName, StateName")
      .eq("Pincode", pincode) // Updated column name to match database
      .order("OfficeName");

    if (pincodeError) {
      console.error("Pincode Fetch Error:", pincodeError);
      return { error: "Database error fetching pincode details." };
    }

    if (!pincodeData || pincodeData.length === 0) {
      return { error: "Pincode not found." };
    }

    // State names are already in title case format in the database
    const state = pincodeData[0].StateName;

    // Use DivisionName as the city (already cleaned)
    const city = pincodeData[0].DivisionName;

    // Get unique localities from post office names
    const localities = [
      ...new Set(pincodeData.map((item: { OfficeName: string }) => item.OfficeName)),
    ] as string[];

    return {
      data: { city, state, localities },
      city,
      state,
      localities
    };
  } catch (e) {
    console.error("Pincode Lookup Exception:", e);
    return { error: "An unexpected error occurred during pincode lookup." };
  }
}
// --- End Pincode Lookup ---

// --- City Lookup Action ---
export async function getCityDetails(city: string): Promise<{
  data?: {
    pincodes: string[];
    state: string;
    localities: string[];
  };
  pincodes?: string[];
  state?: string;
  localities?: string[];
  error?: string;
}> {
  if (!city || city.length < 2) {
    return { error: "City name must be at least 2 characters." };
  }

  const supabase = await createClient();
  try {
    // Get pincodes and state for the city - DivisionName is the city column
    const { data: cityData, error: cityError } = await supabase
      .from("pincodes")
      .select("Pincode, OfficeName, StateName, DivisionName")
      .ilike("DivisionName", `%${city}%`)
      .order("Pincode");

    if (cityError) {
      console.error("City Fetch Error:", cityError);
      return { error: "Database error fetching city details." };
    }

    if (!cityData || cityData.length === 0) {
      return { error: "City not found." };
    }

    // State names are already in title case format in the database
    const state = cityData[0].StateName;

    // Get unique pincodes
    const pincodes = [...new Set(cityData.map((item: { Pincode: string }) => item.Pincode))] as string[];

    // Get unique localities from post office names
    const localities = [
      ...new Set(cityData.map((item: { OfficeName: string }) => item.OfficeName)),
    ] as string[];

    return {
      data: { pincodes, state, localities },
      pincodes,
      state,
      localities
    };
  } catch (e) {
    console.error("City Lookup Exception:", e);
    return { error: "An unexpected error occurred during city lookup." };
  }
}
// --- End City Lookup ---

// --- City Autocomplete Action ---
/**
 * Get city suggestions based on a search query
 *
 * This function uses the Supabase PostgreSQL function 'get_distinct_cities' to fetch unique city names.
 * The PostgreSQL function is defined as:
 *
 * ```sql
 * CREATE OR REPLACE FUNCTION get_distinct_cities(search_query TEXT, result_limit INTEGER)
 * RETURNS TABLE(city TEXT) AS $$
 * BEGIN
 *   RETURN QUERY
 *   SELECT DISTINCT "DivisionName" as city
 *   FROM pincodes
 *   WHERE "DivisionName" ILIKE search_query
 *   ORDER BY "DivisionName"
 *   LIMIT result_limit;
 * END;
 * $$ LANGUAGE plpgsql;
 * ```
 *
 * @param query The search query (minimum 2 characters)
 * @returns Array of up to 5 unique city suggestions
 */
export async function getCitySuggestions(query: string): Promise<{
  data?: {
    cities: string[];
  };
  cities?: string[];
  error?: string;
}> {
  if (!query || query.length < 2) {
    return { error: "Query must be at least 2 characters." };
  }

  const supabase = (await createClient()) as SupabaseClient<Database>;
  try {
    // Use the PostgreSQL function to get distinct cities (up to 5)
    const { data: cityData, error: cityError } = await supabase
      .rpc('get_distinct_cities', {
        search_query: `%${query}%`,
        result_limit: 5
      });

    if (cityError) {
      console.error("City Suggestions Error:", cityError);

      // Fallback to regular query if RPC fails
      try {
        // Use a regular query as fallback
        const { data: fallbackData, error: fallbackError } = await supabase
          .from("pincodes")
          .select("DivisionName")
          .ilike("DivisionName", `%${query}%`)
          .order("DivisionName")
          .limit(100);

        if (fallbackError) {
          throw fallbackError;
        }

        if (!fallbackData || fallbackData.length === 0) {
          return { data: { cities: [] }, cities: [] };
        }

        // Get unique cities and format them
        const cities = [...new Set(fallbackData.map((item: { DivisionName: string }) =>
          item.DivisionName.toLowerCase().replace(/\b\w/g, (char: string) => char.toUpperCase())
        ))] as string[];

        const topCities = cities.slice(0, 5);

        return {
          data: { cities: topCities },
          cities: topCities
        };
      } catch (fallbackErr) {
        console.error("Fallback City Query Error:", fallbackErr);
        return { error: "Database error fetching city suggestions." };
      }
    }

    if (!cityData || cityData.length === 0) {
      return { data: { cities: [] }, cities: [] };
    }

    // Format the city names to Title Case
    const cities = cityData.map((item: { city: string }) =>
      item.city.toLowerCase().replace(/\b\w/g, (char: string) => char.toUpperCase())
    );

    return {
      data: { cities },
      cities
    };
  } catch (e) {
    console.error("City Suggestions Exception:", e);
    return { error: "An unexpected error occurred while fetching city suggestions." };
  }
}
// --- End City Autocomplete ---
