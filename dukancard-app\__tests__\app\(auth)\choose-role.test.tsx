import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import ChooseRoleScreen from '@/app/(auth)/choose-role';
import { useAuth } from '@/src/contexts/AuthContext';
import { useAuthErrorHandler } from '@/src/hooks/useAuthErrorHandler';
import { createCustomerProfile } from '@/backend/supabase/services/customer/customerProfileService';
import { router, useLocalSearchParams } from 'expo-router';
import { Alert, View } from 'react-native';

// Mock all external dependencies
jest.mock('@/src/contexts/AuthContext');
jest.mock('@/src/hooks/useAuthErrorHandler');
jest.mock('@/backend/supabase/services/customer/customerProfileService');
jest.mock('expo-router');

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => {
  const MockView = ({ children, testID }: { children?: React.ReactNode; testID?: string }) => <React.Fragment><View testID={testID}>{children}</View></React.Fragment>;
  return {
    User: ({ size, color }: { size: number; color: string }) => <MockView testID="mock-user-icon" />,
    Briefcase: ({ size, color }: { size: number; color: string }) => <MockView testID="mock-briefcase-icon" />,
    ChevronRight: ({ size, color }: { size: number; color: string }) => <MockView testID="mock-chevron-right-icon" />,
  };
});

// Mock useTheme and useSafeAreaInsets as they are UI-related and not core to logic
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: { primary: '#000', textPrimary: '#000', textSecondary: '#000', error: '#000' },
    spacing: { md: 10, lg: 20, xl: 30, xxxl: 50 },
    typography: { fontSize: { base: 14, lg: 16, xl: 20, xxl: 24, xs: 12 }, fontWeight: { bold: 'bold', normal: 'normal', medium: 'medium' }, lineHeight: { normal: 1.5, relaxed: 1.8 } },
    borderRadius: { lg: 10 },
    isDark: false,
    shadows: { sm: { shadowColor: '#000', shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.2, shadowRadius: 1.41, elevation: 2 } },
  }),
}));
jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}));

// Mock react-native Alert and AppState
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native') as typeof import('react-native');
  return {
    ...RN,
    Alert: {
      alert: jest.fn(),
    },
    AppState: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      currentState: 'active',
    },
    ActivityIndicator: ({ animating, ...props }: { animating?: boolean; [key: string]: any }) => animating ? <RN.View testID="mock-activity-indicator" {...props} /> : null,
  };
});

describe('ChooseRoleScreen', () => {
  const mockUser = { id: 'user-123', email: '<EMAIL>', user_metadata: { full_name: 'Test User' } };
  const mockRefreshProfileStatus = jest.fn();
  
  // Declare resolveOperation and rejectOperation in a higher scope
  let resolveOperation: (value?: any) => void;
  let rejectOperation: (reason?: any) => void;

  const mockExecuteWithErrorHandling = jest.fn(async ({ operation, onSuccess, onError }) => {
    const promise = new Promise((resolve, reject) => {
      resolveOperation = resolve;
      rejectOperation = reject;
    });

    // Immediately execute the operation, but control the promise resolution externally
    operation().then(async (result: any) => {
      if (onSuccess) await onSuccess(result);
      resolveOperation(result);
    }).catch(async (error: any) => {
      if (onError) await onError(error);
      rejectOperation(error);
    });

    return promise; // Return the controllable promise
  });

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers(); // Use fake timers for setTimeout

    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
      refreshProfileStatus: mockRefreshProfileStatus,
    });
    (useAuthErrorHandler as jest.Mock).mockReturnValue({
      executeWithErrorHandling: mockExecuteWithErrorHandling,
      clearError: jest.fn(),
      hasError: false,
      error: null,
      isLoading: false,
      isOnline: true,
      canRetryOperation: false,
      retryCount: 0,
    });
    (useLocalSearchParams as jest.Mock).mockReturnValue({});
    (router.replace as jest.Mock).mockClear();
    (createCustomerProfile as jest.Mock).mockClear();
    jest.spyOn(Alert, 'alert').mockClear(); // Clear Alert mock
  });

  afterEach(() => {
    jest.runOnlyPendingTimers(); // Run any pending timers
    jest.useRealTimers(); // Restore real timers
  });

  it('renders correctly with role options', () => {
    const { getByText } = render(<ChooseRoleScreen />);
    expect(getByText('Choose Your Role')).toBeTruthy();
    expect(getByText('Customer')).toBeTruthy();
    expect(getByText('Business')).toBeTruthy();
  });

  it('handles customer role selection and navigates to customer dashboard', async () => {
    (createCustomerProfile as jest.Mock).mockResolvedValue({ error: null });

    const { getByText } = render(<ChooseRoleScreen />);
    fireEvent.press(getByText('Customer'));

    // Manually resolve the operation after the loading state is asserted
    await waitFor(() => expect(mockExecuteWithErrorHandling).toHaveBeenCalled());
    resolveOperation({ error: null }); // Resolve the promise returned by executeWithErrorHandling

    await waitFor(() => {
      expect(createCustomerProfile).toHaveBeenCalledWith(mockUser.id, { name: mockUser.user_metadata.full_name, email: mockUser.email });
      expect(mockRefreshProfileStatus).toHaveBeenCalled();
      expect(router.replace).toHaveBeenCalledWith('/(dashboard)/customer');
    });
  });

  it('handles business role selection and navigates to business onboarding', async () => {
    const { getByText } = render(<ChooseRoleScreen />);
    fireEvent.press(getByText('Business'));

    // Manually resolve the operation after the loading state is asserted
    await waitFor(() => expect(mockExecuteWithErrorHandling).toHaveBeenCalled());
    jest.runAllTimers(); // Advance timers for setTimeout in handleBusinessSelection
    resolveOperation(true); // Resolve the promise returned by executeWithErrorHandling

    await waitFor(() => {
      expect(router.replace).toHaveBeenCalledWith('/(onboarding)/business-details');
    });
  });

  it('shows an alert if user is not found on customer selection', async () => {
    (useAuth as jest.Mock).mockReturnValue({ user: null, refreshProfileStatus: mockRefreshProfileStatus });
    const { getByText } = render(<ChooseRoleScreen />);
    fireEvent.press(getByText('Customer'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'User not found. Please try logging in again.');
      expect(createCustomerProfile).not.toHaveBeenCalled();
      expect(router.replace).not.toHaveBeenCalled();
    });
  });

  it('handles customer profile creation error', async () => {
    (createCustomerProfile as jest.Mock).mockResolvedValue({ error: { message: 'Profile creation failed' } });
    const { getByText } = render(<ChooseRoleScreen />);
    fireEvent.press(getByText('Customer'));

    // Manually reject the operation after the loading state is asserted
    await waitFor(() => expect(mockExecuteWithErrorHandling).toHaveBeenCalled());
    rejectOperation(new Error('Profile creation failed')); // Reject the promise

    await waitFor(() => {
      expect(mockExecuteWithErrorHandling).toHaveBeenCalled();
      expect(router.replace).not.toHaveBeenCalled();
      // Expect error handling UI to be shown, e.g., via showErrorRecovery state
    });
  });

  it('handles business selection with redirect parameter', async () => {
    (useLocalSearchParams as jest.Mock).mockReturnValue({ redirect: 'some-slug' });
    const { getByText } = render(<ChooseRoleScreen />);
    fireEvent.press(getByText('Business'));

    await waitFor(() => expect(mockExecuteWithErrorHandling).toHaveBeenCalled());
    jest.runAllTimers();
    resolveOperation(true);

    await waitFor(() => {
      expect(router.replace).toHaveBeenCalledWith('/(onboarding)/business-details?redirect=some-slug');
    });
  });

  it('handles business selection with message parameter', async () => {
    (useLocalSearchParams as jest.Mock).mockReturnValue({ message: 'Welcome!' });
    const { getByText } = render(<ChooseRoleScreen />);
    fireEvent.press(getByText('Business'));

    await waitFor(() => expect(mockExecuteWithErrorHandling).toHaveBeenCalled());
    jest.runAllTimers();
    resolveOperation(true);

    await waitFor(() => {
      expect(router.replace).toHaveBeenCalledWith('/(onboarding)/business-details?message=Welcome!');
    });
  });

  it('handles business selection with both redirect and message parameters', async () => {
    (useLocalSearchParams as jest.Mock).mockReturnValue({ redirect: 'some-slug', message: 'Welcome!' });
    const { getByText } = render(<ChooseRoleScreen />);
    fireEvent.press(getByText('Business'));

    await waitFor(() => expect(mockExecuteWithErrorHandling).toHaveBeenCalled());
    jest.runAllTimers();
    resolveOperation(true);

    await waitFor(() => {
      expect(router.replace).toHaveBeenCalledWith('/(onboarding)/business-details?redirect=some-slug&message=Welcome!');
    });
  });

  it('displays loading state when creating customer profile', async () => {
    // Mock createCustomerProfile to return a pending promise
    (createCustomerProfile as jest.Mock).mockReturnValue(new Promise(() => {})); 

    const { getByText, queryByText, findByText } = render(<ChooseRoleScreen />);
    fireEvent.press(getByText('Customer'));

    // Assert loading state immediately after action
    expect(await findByText('Setting up your account...')).toBeTruthy();
    expect(queryByText('Customer')).toBeNull(); // Role cards should be hidden
  });

  it('displays loading state when navigating to business onboarding', async () => {
    // Do not run timers immediately to keep it in loading state
    const { getByText, queryByText, findByText } = render(<ChooseRoleScreen />);
    fireEvent.press(getByText('Business'));

    expect(await findByText('Redirecting to onboarding...')).toBeTruthy();
    expect(queryByText('Business')).toBeNull(); // Role cards should be hidden
  });
});