import { addProductService } from '@/app/(dashboard)/dashboard/business/products/actions/addProduct';
import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';
import { handleBaseProductImageUpload } from '@/app/(dashboard)/dashboard/business/products/actions/imageHandlers';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));
jest.mock('@/app/(dashboard)/dashboard/business/products/actions/imageHandlers', () => ({
  handleBaseProductImageUpload: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe('addProductService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return an error if the user is not authenticated', async () => {
    // Arrange
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
      },
    });
    const formData = new FormData();

    // Act
    const result = await addProductService(formData);

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toBe('User not authenticated.');
  });

  it('should return a validation error for invalid data', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    });
    const formData = new FormData();
    formData.append('name', ''); // Invalid name

    // Act
    const result = await addProductService(formData);

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toContain('Invalid data');
  });

  it('should add a product successfully', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProduct = { id: 'prod-123', name: 'Test Product' };

    // Create a proper query builder mock
    const createQueryBuilder = () => ({
      insert: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
    });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => createQueryBuilder()),
    });

    const formData = new FormData();
    formData.append('name', 'Test Product');
    formData.append('base_price', '100');
    formData.append('product_type', 'physical');

    // Act
    const result = await addProductService(formData);

    // Assert
    expect(result.success).toBe(true);
    expect(result.data?.id).toBe('prod-123');
    expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
  });

  it('should add a product with images successfully', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProduct = { id: 'prod-123', name: 'Test Product', image_url: null, images: [], featured_image_index: 0 };
    const mockUploadedUrls = ['url1.jpg', 'url2.jpg'];

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn((tableName) => {
        if (tableName === 'products_services') {
          return {
            insert: jest.fn(() => ({
              select: jest.fn(() => ({
                single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
              })),
            })),
            update: jest.fn(() => ({
              eq: jest.fn(() => ({
                select: jest.fn().mockResolvedValue({ data: { id: mockProduct.id, image_url: mockUploadedUrls[0], images: mockUploadedUrls, featured_image_index: 0 }, error: null }),
              })),
            })),
            select: jest.fn(() => ({
              eq: jest.fn(() => ({
                single: jest.fn().mockResolvedValue({ data: { ...mockProduct, image_url: mockUploadedUrls[0], images: mockUploadedUrls, featured_image_index: 0 }, error: null }),
              })),
            })),
          };
        }
        return {};
      }),
    });

    (handleBaseProductImageUpload as jest.Mock).mockResolvedValue({
      urls: mockUploadedUrls,
      error: null,
    });

    const formData = new FormData();
    formData.append('name', 'Test Product');
    formData.append('base_price', '100');
    formData.append('product_type', 'physical');
    formData.append('productImage_0', new File(['dummy'], 'image1.jpg', { type: 'image/jpeg' }));
    formData.append('productImage_1', new File(['dummy'], 'image2.jpg', { type: 'image/jpeg' }));
    formData.append('featuredImageIndex', '0');

    // Act
    const result = await addProductService(formData);

    // Assert
    expect(result.success).toBe(true);
    expect(result.data?.id).toBe('prod-123');
    expect(handleBaseProductImageUpload).toHaveBeenCalledWith('user-123', 'prod-123', expect.any(Array));
    expect(result.data?.image_url).toBe(mockUploadedUrls[0]);
    expect(result.data?.images).toEqual(mockUploadedUrls);
    expect(result.data?.featured_image_index).toBe(0);
    expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
  });

  it('should return an error if image upload fails', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProduct = { id: 'prod-123', name: 'Test Product' };

    const createQueryBuilder = () => ({
      insert: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
    });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => createQueryBuilder()),
    });

    (handleBaseProductImageUpload as jest.Mock).mockResolvedValue({
      urls: [],
      error: 'Upload failed',
    });

    const formData = new FormData();
    formData.append('name', 'Test Product');
    formData.append('base_price', '100');
    formData.append('product_type', 'physical');
    formData.append('productImage_0', new File(['dummy'], 'image1.jpg', { type: 'image/jpeg' }));

    // Act
    const result = await addProductService(formData);

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toContain('Product created, but image upload failed');
    expect(handleBaseProductImageUpload).toHaveBeenCalled();
    expect(revalidatePath).not.toHaveBeenCalled(); // Revalidate should not be called on failure
  });
});