import React, { useState, useRef } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Switch,
  Image,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Package, Camera, X, Plus, Settings, ChevronDown, ChevronUp, Edit3, Trash2 } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { Tables, Json } from "@dukancard-types/supabase";
import { addProductFormSchema, updateProductFormSchema } from "@/backend/supabase/services/business/schemas";
import { PlanLimitInfo, getPlanDisplayName, getPlanLimitDisplayText } from "@/backend/supabase/services/business/planLimitService";
import { createManageProductsModalStyles } from "@/styles/modals/business/manage-products-modal";
import ImagePickerBottomSheet, {
  ImagePickerBottomSheetRef,
} from "@/src/components/pickers/ImagePickerBottomSheet";
import { compressImageUltraAggressive, toBase64DataUrl } from "@/src/utils/imageCompression";
import { useToast } from "@/src/components/ui/Toast";
import VariantModal from "../VariantModal";
import VariantList from "./VariantList";
import * as ImagePicker from "expo-image-picker";

// Form data type - enhanced with variants
type ProductFormData = {
  product_type: "physical" | "service";
  name: string;
  description?: string;
  base_price: number;
  discounted_price?: number;
  is_available: boolean;
  images: string[];
  featured_image_index: number;
  variants?: any[]; // Will be handled separately
  removedOriginalIndices?: number[]; // Track removed original images for cleanup
};

interface ProductFormProps {
  product?: Tables<'products_services'> | null;
  variants?: Tables<'product_variants'>[];
  onSubmit: (data: ProductFormData) => void;
  onCancel: () => void;
  loading: boolean;
  planLimitInfo?: PlanLimitInfo | null;
}

const ProductForm: React.FC<ProductFormProps> = ({
  product,
  variants,
  onSubmit,
  onCancel,
  loading,
  planLimitInfo,
}) => {
  const theme = useTheme();
  const toast = useToast();
  const styles = createManageProductsModalStyles(theme);
  const imagePickerRef = useRef<ImagePickerBottomSheetRef>(null);

  // State management
  const [images, setImages] = useState<string[]>(product?.images || []);
  const [featuredImageIndex, setFeaturedImageIndex] = useState(product?.featured_image_index || 0);
  const [removedOriginalIndices, setRemovedOriginalIndices] = useState<number[]>([]);
  const [showVariants, setShowVariants] = useState(false);
  const [showVariantForm, setShowVariantForm] = useState(false);
  const [editingVariant, setEditingVariant] = useState<any>(null);
  const [imageProcessing, setImageProcessing] = useState(false);

  // Local variant management (like Next.js)
  const [localVariants, setLocalVariants] = useState<(Tables<'product_variants'> & { tempId?: string })[]>(variants || []);
  // Form setup
  const formMethods = useForm<ProductFormData>({
    defaultValues: {
      product_type: (product?.product_type as "physical" | "service") || "physical",
      name: product?.name || "",
      description: product?.description || "",
      base_price: product?.base_price || 0,
      discounted_price: product?.discounted_price || undefined,
      is_available: product?.is_available ?? true,
      images: product?.images || [],
      featured_image_index: product?.featured_image_index || 0,
    },
    mode: "onChange",
  });

  const { control, handleSubmit, setValue, watch, formState: { errors } } = formMethods;

  // Enhanced image handling with compression
  const handleImageSelect = async (uri: string) => {
    try {
      setImageProcessing(true);

      // Compress image aggressively like Next.js
      const compressed = await compressImageUltraAggressive(uri, {
        targetSizeKB: 100,
        maxWidth: 800,
        maxHeight: 800,
      });

      if (compressed.base64) {
        const dataUrl = toBase64DataUrl(compressed.base64);
        const newImages = [...images, dataUrl];
        setImages(newImages);
        setValue("images", newImages);

        toast.success("Success", `Image compressed to ${Math.round(compressed.size / 1024)}KB`);
      }
    } catch (error) {
      toast.error("Error", "Failed to process image");
    } finally {
      setImageProcessing(false);
    }
  };

  const handleRemoveImage = (index: number) => {
    // Track removed original images for cleanup (like Next.js)
    if (product?.images && index < product.images.length) {
      setRemovedOriginalIndices(prev => [...prev, index]);
    }

    const newImages = images.filter((_, i) => i !== index);
    setImages(newImages);
    setValue("images", newImages);

    // Adjust featured image index if needed
    if (featuredImageIndex >= newImages.length) {
      const newFeaturedIndex = Math.max(0, newImages.length - 1);
      setFeaturedImageIndex(newFeaturedIndex);
      setValue("featured_image_index", newFeaturedIndex);
    }

    toast.success("Success", "Image removed successfully");
  };

  const handleSetFeaturedImage = (index: number) => {
    setFeaturedImageIndex(index);
    setValue("featured_image_index", index);
  };

  const handleCamera = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await handleImageSelect(result.assets[0].uri);
      }
    } catch (error) {
      toast.error("Error", "Failed to capture image");
    }
  };

  const handleGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
        allowsMultipleSelection: true,
        selectionLimit: 5,
      });

      if (!result.canceled) {
        for (const asset of result.assets) {
          await handleImageSelect(asset.uri);
        }
      }
    } catch (error) {
      toast.error("Error", "Failed to select images");
    }
  };

  // Local variant management handlers (like Next.js)
  const handleAddVariant = () => {
    setEditingVariant(null);
    setShowVariantForm(true);
  };

  const handleEditLocalVariant = (variant: any) => {
    setEditingVariant(variant);
    setShowVariantForm(true);
  };

  const handleDeleteLocalVariant = (variantId: string) => {
    setLocalVariants(prev => prev.filter(v => (v.id || v.tempId) !== variantId));
    toast.success("Success", "Variant removed successfully");
  };

  const handleVariantSubmit = async (variantData: any) => {
    try {
      if (editingVariant) {
        // Update existing variant in local state
        setLocalVariants(prev =>
          prev.map(v =>
            (v.id || v.tempId) === (editingVariant.id || editingVariant.tempId)
              ? {
                  ...v,
                  variant_name: variantData.variant_name,
                  variant_values: variantData.variant_values as Json,
                  base_price: variantData.base_price,
                  discounted_price: variantData.discounted_price,
                  is_available: variantData.is_available,
                  images: variantData.images || [],
                  featured_image_index: variantData.featured_image_index || 0,
                }
              : v
          )
        );
        toast.success("Success", "Variant updated successfully");
      } else {
        // Add new variant to local state
        const newVariant = {
          id: `temp-${Date.now()}`,
          tempId: `temp-${Date.now()}`,
          product_id: product?.id || "",
          variant_name: variantData.variant_name,
          variant_values: variantData.variant_values as Json,
          base_price: variantData.base_price,
          discounted_price: variantData.discounted_price,
          is_available: variantData.is_available,
          images: variantData.images || [],
          featured_image_index: variantData.featured_image_index || 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
        setLocalVariants(prev => [...prev, newVariant]);
        toast.success("Success", "Variant added successfully");
      }

      setShowVariantForm(false);
      setEditingVariant(null);
    } catch (error) {
      toast.error("Error", "Failed to save variant");
    }
  };

  const handleVariantCancel = () => {
    setShowVariantForm(false);
    setEditingVariant(null);
  };

  const onFormSubmit = (data: ProductFormData) => {
    const submitData = {
      ...data,
      images,
      featured_image_index: featuredImageIndex,
      variants: localVariants, // Include local variants like Next.js
      removedOriginalIndices, // Include removed image indices for cleanup
    };
    onSubmit(submitData);
  };

  // Note: Variant form is now handled by separate VariantModal

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
      {/* Basic Information Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionIconContainer}>
            <Package size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>Basic Information</Text>
            <Text style={styles.sectionDescription}>
              Essential details about your product or service
            </Text>
          </View>
        </View>

        {/* Product Type Selector */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Product Type *</Text>
          <Controller
            control={control}
            name="product_type"
            render={({ field: { onChange, value } }) => (
              <View style={styles.enhancedSegmentedControl}>
                <TouchableOpacity
                  style={[
                    styles.enhancedSegmentButton,
                    value === "physical" && styles.enhancedSegmentButtonActive,
                  ]}
                  onPress={() => onChange("physical")}
                  activeOpacity={0.7}
                >
                  <View style={styles.segmentIconContainer}>
                    <Package
                      size={20}
                      color={value === "physical" ? (theme.isDark ? "#000" : "#FFF") : theme.colors.mutedForeground}
                    />
                  </View>
                  <View style={styles.segmentTextContainer}>
                    <Text style={[
                      styles.enhancedSegmentButtonText,
                      value === "physical" && styles.enhancedSegmentButtonTextActive,
                    ]}>
                      Physical Product
                    </Text>
                    <Text style={[
                      styles.segmentButtonSubtext,
                      value === "physical" && styles.segmentButtonSubtextActive,
                    ]}>
                      Tangible items you ship
                    </Text>
                  </View>
                  {value === "physical" && (
                    <View style={styles.selectedIndicator}>
                      <View style={styles.selectedDot} />
                    </View>
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.enhancedSegmentButton,
                    value === "service" && styles.enhancedSegmentButtonActive,
                  ]}
                  onPress={() => onChange("service")}
                  activeOpacity={0.7}
                >
                  <View style={styles.segmentIconContainer}>
                    <Settings
                      size={20}
                      color={value === "service" ? (theme.isDark ? "#000" : "#FFF") : theme.colors.mutedForeground}
                    />
                  </View>
                  <View style={styles.segmentTextContainer}>
                    <Text style={[
                      styles.enhancedSegmentButtonText,
                      value === "service" && styles.enhancedSegmentButtonTextActive,
                    ]}>
                      Service
                    </Text>
                    <Text style={[
                      styles.segmentButtonSubtext,
                      value === "service" && styles.segmentButtonSubtextActive,
                    ]}>
                      Digital or consultation services
                    </Text>
                  </View>
                  {value === "service" && (
                    <View style={styles.selectedIndicator}>
                      <View style={styles.selectedDot} />
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            )}
          />
        </View>

        {/* Product Name */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Product Name *</Text>
          <Controller
            control={control}
            name="name"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={[styles.input, errors.name && styles.inputError]}
                placeholder="Enter product name"
                placeholderTextColor={theme.colors.mutedForeground}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
              />
            )}
          />
          {errors.name && (
            <Text style={styles.errorText}>{errors.name.message}</Text>
          )}
        </View>

        {/* Product Description */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Description</Text>
          <Controller
            control={control}
            name="description"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={[styles.input, styles.textArea]}
                placeholder="Describe your product or service"
                placeholderTextColor={theme.colors.mutedForeground}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value || ""}
                multiline
                numberOfLines={4}
              />
            )}
          />
        </View>
      </View>

      {/* Pricing Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionIconContainer}>
            <Text style={styles.currencyIcon}>₹</Text>
          </View>
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>Pricing</Text>
            <Text style={styles.sectionDescription}>
              Set competitive pricing for your product
            </Text>
          </View>
        </View>

        <View style={styles.priceRow}>
          <View style={styles.priceInput}>
            <Text style={styles.inputLabel}>Base Price *</Text>
            <Controller
              control={control}
              name="base_price"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={[styles.input, errors.base_price && styles.inputError]}
                  placeholder="₹0"
                  placeholderTextColor={theme.colors.mutedForeground}
                  onBlur={onBlur}
                  onChangeText={(text) => onChange(text ? parseFloat(text) : 0)}
                  value={value ? String(value) : ""}
                  keyboardType="numeric"
                />
              )}
            />
            {errors.base_price && (
              <Text style={styles.errorText}>{errors.base_price.message}</Text>
            )}
          </View>

          <View style={styles.priceInput}>
            <Text style={styles.inputLabel}>Sale Price</Text>
            <Controller
              control={control}
              name="discounted_price"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={styles.input}
                  placeholder="₹0 (Optional)"
                  placeholderTextColor={theme.colors.mutedForeground}
                  onBlur={onBlur}
                  onChangeText={(text) => onChange(text ? parseFloat(text) : undefined)}
                  value={value ? String(value) : ""}
                  keyboardType="numeric"
                />
              )}
            />
          </View>
        </View>
      </View>

      {/* Images Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionIconContainer}>
            <Camera size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>Product Images</Text>
            <Text style={styles.sectionDescription}>
              Upload high-quality images to showcase your product
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={[
            styles.enhancedAddImageButton,
            imageProcessing && styles.addImageButtonDisabled,
            images.length >= 5 && styles.addImageButtonDisabled
          ]}
          onPress={() => imagePickerRef.current?.present()}
          disabled={imageProcessing || images.length >= 5}
          activeOpacity={0.7}
        >
          <View style={styles.addImageButtonContent}>
            <View style={styles.addImageIconContainer}>
              {imageProcessing ? (
                <ActivityIndicator size="small" color={theme.colors.primary} />
              ) : (
                <Camera size={24} color={images.length >= 5 ? theme.colors.mutedForeground : theme.colors.primary} />
              )}
            </View>
            <View style={styles.addImageTextContainer}>
              <Text style={[
                styles.enhancedAddImageButtonText,
                (imageProcessing || images.length >= 5) && styles.addImageButtonTextDisabled
              ]}>
                {imageProcessing ? "Processing..." :
                 images.length >= 5 ? "Maximum 5 images" : "Add Images"}
              </Text>
              <Text style={styles.addImageButtonSubtext}>
                {images.length}/5 images • Tap to add photos
              </Text>
            </View>
          </View>
        </TouchableOpacity>

        {images.length > 0 && (
          <View style={styles.enhancedImageSection}>
            <View style={styles.imageGridHeader}>
              <Text style={styles.imageGridTitle}>
                Product Images ({images.length}/5)
              </Text>
              <Text style={styles.imageGridSubtitle}>
                Tap &quot;Set Featured&quot; to choose your main product image
              </Text>
            </View>

            <View style={styles.enhancedImageGrid}>
              {images.map((uri, index) => (
                <View key={index} style={styles.enhancedImageContainer}>
                  <Image source={{ uri }} style={styles.enhancedImagePreview} />

                  {/* Remove Button */}
                  <TouchableOpacity
                    style={styles.enhancedRemoveImageButton}
                    onPress={() => handleRemoveImage(index)}
                    activeOpacity={0.7}
                  >
                    <X size={16} color="#FFF" />
                  </TouchableOpacity>

                  {/* Image Index */}
                  <View style={styles.imageIndexBadge}>
                    <Text style={styles.imageIndexText}>{index + 1}</Text>
                  </View>

                  {/* Featured Badge or Set Featured Button */}
                  {index === featuredImageIndex ? (
                    <View style={styles.enhancedFeaturedBadge}>
                      <Text style={styles.enhancedFeaturedBadgeText}>★ Featured</Text>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={styles.enhancedSetFeaturedButton}
                      onPress={() => handleSetFeaturedImage(index)}
                      activeOpacity={0.7}
                    >
                      <Text style={styles.enhancedSetFeaturedButtonText}>Set Featured</Text>
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          </View>
        )}
      </View>

      {/* Product Variants Section - Available for both add and edit */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionIconContainer}>
            <Settings size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>Product Variants</Text>
            <Text style={styles.sectionDescription}>
              {product
                ? "Manage different variations of this product (e.g., size, color, style)"
                : "Add different variations of this product (e.g., size, color, style). Variants will be saved when you create the product."}
            </Text>
          </View>
          <TouchableOpacity
            style={styles.toggleButton}
            onPress={() => setShowVariants(!showVariants)}
          >
            {showVariants ? (
              <ChevronUp size={20} color={theme.colors.primary} />
            ) : (
              <ChevronDown size={20} color={theme.colors.primary} />
            )}
          </TouchableOpacity>
        </View>

        {showVariants && (
          <View style={styles.variantManagementContainer}>
            {/* Enhanced Add Variant Button */}
            <TouchableOpacity
              style={[
                styles.enhancedAddVariantButton,
                showVariantForm && styles.addVariantButtonDisabled
              ]}
              onPress={handleAddVariant}
              disabled={showVariantForm}
              activeOpacity={0.7}
            >
              <View style={styles.addVariantButtonContent}>
                <View style={styles.addVariantIconContainer}>
                  <Plus size={20} color={showVariantForm ? theme.colors.mutedForeground : (theme.isDark ? "#000" : "#FFF")} />
                </View>
                <View style={styles.addVariantTextContainer}>
                  <Text style={[
                    styles.enhancedAddVariantButtonText,
                    showVariantForm && styles.addVariantButtonTextDisabled
                  ]}>
                    Add Product Variant
                  </Text>
                  <Text style={[
                    styles.addVariantButtonSubtext,
                    showVariantForm && styles.addVariantButtonTextDisabled
                  ]}>
                    Create different options (size, color, etc.)
                  </Text>
                </View>
              </View>
            </TouchableOpacity>

            {/* Variant form is now handled by separate VariantModal */}

            {/* Local Variants List */}
            {localVariants.length > 0 ? (
              <View style={styles.localVariantsList}>
                {localVariants.map((variant, index) => (
                  <View key={variant.id || index} style={styles.variantItem}>
                    <View style={styles.variantImageContainer}>
                      {variant.images && variant.images.length > 0 ? (
                        <Image
                          source={{ uri: variant.images[variant.featured_image_index || 0] }}
                          style={styles.variantImage}
                        />
                      ) : (
                        <View style={styles.variantImagePlaceholder}>
                          <Package size={24} color={theme.colors.mutedForeground} />
                        </View>
                      )}
                    </View>

                    <View style={styles.variantInfo}>
                      <Text style={styles.variantName}>{variant.variant_name}</Text>
                      <Text style={styles.variantValues}>
                        {Object.entries(variant.variant_values || {})
                          .map(([key, value]) => `${key}: ${value}`)
                          .join(", ")}
                      </Text>

                      <View style={styles.variantPricing}>
                        {variant.discounted_price && variant.discounted_price < (variant.base_price || 0) ? (
                          <>
                            <Text style={styles.variantDiscountedPrice}>
                              ₹{variant.discounted_price.toLocaleString("en-IN")}
                            </Text>
                            <Text style={styles.variantOriginalPrice}>
                              ₹{variant.base_price?.toLocaleString("en-IN")}
                            </Text>
                          </>
                        ) : (
                          <Text style={styles.variantPrice}>
                            ₹{variant.base_price?.toLocaleString("en-IN") || "Not set"}
                          </Text>
                        )}
                      </View>

                      <View style={styles.variantStatus}>
                        <View
                          style={[
                            styles.statusIndicator,
                            {
                              backgroundColor: variant.is_available
                                ? theme.colors.success
                                : theme.colors.destructive,
                            },
                          ]}
                        />
                        <Text style={styles.statusText}>
                          {variant.is_available ? "Available" : "Unavailable"}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.variantActions}>
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleEditLocalVariant(variant)}
                        disabled={showVariantForm}
                      >
                        <Edit3 size={16} color={theme.colors.primary} />
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleDeleteLocalVariant(variant.id || index.toString())}
                        disabled={showVariantForm}
                      >
                        <Trash2 size={16} color={theme.colors.destructive} />
                      </TouchableOpacity>
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              !showVariantForm && (
                <View style={styles.emptyState}>
                  <Package size={48} color={theme.colors.mutedForeground} />
                  <Text style={styles.emptyStateTitle}>No Variants</Text>
                  <Text style={styles.emptyStateDescription}>
                    Add variants to offer different options for this product
                  </Text>
                </View>
              )
            )}
          </View>
        )}
      </View>

      {/* Availability Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <View style={styles.sectionIconContainer}>
            <Settings size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.sectionTitleContainer}>
            <Text style={styles.sectionTitle}>Availability</Text>
            <Text style={styles.sectionDescription}>
              Control whether this product is available for purchase
            </Text>
          </View>
        </View>

        {/* Enhanced Plan Limit Info */}
        {planLimitInfo && (
          <View style={styles.enhancedPlanLimitContainer}>
            <View style={styles.planLimitHeader}>
              <View style={styles.planLimitIconContainer}>
                <Settings size={16} color={theme.colors.primary} />
              </View>
              <View style={styles.planLimitTextContainer}>
                <Text style={styles.enhancedPlanLimitTitle}>
                  {getPlanDisplayName(planLimitInfo.planId)} Plan
                </Text>
                <Text style={styles.enhancedPlanLimitText}>
                  Available Products: {planLimitInfo.currentAvailableCount} of {getPlanLimitDisplayText(planLimitInfo.planLimit)}
                </Text>
              </View>
            </View>
            {planLimitInfo.isAtLimit && !product?.is_available && (
              <View style={styles.planLimitWarningContainer}>
                <Text style={styles.enhancedPlanLimitWarning}>
                  ⚠️ You&apos;ve reached your plan limit. Upgrade your plan or make another product unavailable first.
                </Text>
              </View>
            )}
          </View>
        )}

        <Controller
          control={control}
          name="is_available"
          render={({ field: { onChange, value } }) => {
            // Check if enabling this would exceed the plan limit
            const isAtPlanLimit = planLimitInfo &&
              planLimitInfo.planLimit !== null &&
              planLimitInfo.currentAvailableCount >= planLimitInfo.planLimit &&
              !value &&
              !product?.is_available;

            const handleToggle = (newValue: boolean) => {
              if (newValue && isAtPlanLimit) {
                toast.error("Plan Limit Reached",
                  `You've reached your plan limit of ${planLimitInfo?.planLimit} available products.`
                );
                return;
              }
              onChange(newValue);
            };

            return (
              <View style={styles.enhancedAvailabilityContainer}>
                <View style={styles.enhancedSwitchRow}>
                  <View style={styles.availabilityIconContainer}>
                    <View style={[
                      styles.availabilityStatusDot,
                      { backgroundColor: value ? "#22C55E" : theme.colors.destructive }
                    ]} />
                  </View>
                  <View style={styles.enhancedSwitchLabelContainer}>
                    <Text style={styles.enhancedSwitchLabel}>Available for purchase</Text>
                    <Text style={styles.availabilityDescription}>
                      {value
                        ? "Customers can see and purchase this product"
                        : "Product will be hidden from customers"}
                    </Text>
                    {isAtPlanLimit && (
                      <Text style={styles.enhancedSwitchSubLabel}>
                        ⚠️ Plan limit reached
                      </Text>
                    )}
                  </View>
                  <View style={styles.switchContainer}>
                    <Switch
                      value={value}
                      onValueChange={handleToggle}
                      disabled={isAtPlanLimit || false}
                      trackColor={{
                        false: theme.colors.muted,
                        true: isAtPlanLimit ? theme.colors.muted : "#22C55E",
                      }}
                      thumbColor={value ? "#FFFFFF" : theme.colors.mutedForeground}
                    />
                  </View>
                </View>
              </View>
            );
          }}
        />
      </View>
      </ScrollView>

      {/* Sticky Action Buttons */}
      <View style={styles.stickyButtonContainer}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={onCancel}
          disabled={loading || imageProcessing}
        >
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.submitButton,
            (loading || imageProcessing) && styles.submitButtonDisabled
          ]}
          onPress={handleSubmit(onFormSubmit)}
          disabled={loading || imageProcessing}
        >
          {loading ? (
            <ActivityIndicator size="small" color={theme.colors.primaryForeground} />
          ) : (
            <Text style={styles.submitButtonText}>
              {product ? "Update" : "Add"} {watch("product_type") === "physical" ? "Product" : "Service"}
            </Text>
          )}
        </TouchableOpacity>
      </View>

      <ImagePickerBottomSheet
        ref={imagePickerRef}
        onCameraPress={handleCamera}
        onGalleryPress={handleGallery}
      />

      {/* Separate Variant Modal */}
      <VariantModal
        visible={showVariantForm}
        variant={editingVariant}
        onSubmit={handleVariantSubmit}
        onCancel={handleVariantCancel}
        loading={loading}
      />
    </View>
  );
};

export default ProductForm;
