import { getProductWithVariants, getAvailableProductVariants, getProductVariants, getBusinessVariantStats, checkVariantCombinationUnique } from '@/app/(dashboard)/dashboard/business/products/actions/getProductWithVariants';
import { createClient } from '@/utils/supabase/server';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe('getProductWithVariants', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return an error if the user is not authenticated', async () => {
    // Arrange
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
      },
    });

    // Act
    const result = await getProductWithVariants('prod-123');

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toBe('User not authenticated.');
  });

  it('should fetch a product with variants successfully', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockRpc = jest.fn().mockResolvedValue({ data: [{ product_id: 'prod-123', product_name: 'Test' }], error: null });
    const mockSingle = jest.fn().mockResolvedValue({ data: { business_id: 'user-123' }, error: null });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      rpc: mockRpc,
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: mockSingle,
    });

    // Act
    const result = await getProductWithVariants('prod-123');

    // Assert
    expect(result.success).toBe(true);
    expect(result.data?.id).toBe('prod-123');
    expect(mockRpc).toHaveBeenCalledWith('get_product_with_variants', { product_uuid: 'prod-123' });
  });

  it('should return an error if rpc call fails', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockError = new Error('RPC error');

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      rpc: jest.fn().mockResolvedValue({ data: null, error: mockError }),
    });

    // Act
    const result = await getProductWithVariants('prod-123');

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toBe('Failed to fetch product data.');
  });

  it('should return an error if product is not found via rpc', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      rpc: jest.fn().mockResolvedValue({ data: [], error: null }),
    });

    // Act
    const result = await getProductWithVariants('prod-123');

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toBe('Product not found.');
  });

  it('should return an error if ownership check fails', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockRpc = jest.fn().mockResolvedValue({ data: [{ product_id: 'prod-123' }], error: null });
    const mockSingle = jest.fn().mockResolvedValue({ data: null, error: new Error('Ownership error') });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      rpc: mockRpc,
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: mockSingle,
    });

    // Act
    const result = await getProductWithVariants('prod-123');

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toBe('Product not found or access denied.');
  });

  describe('getAvailableProductVariants', () => {
    it('should fetch available variants successfully', async () => {
      // Arrange
      const mockVariants = [{ id: 'var-1', is_available: true }];
      mockSupabase.mockReturnValue({
        rpc: jest.fn().mockResolvedValue({ data: mockVariants, error: null }),
      });

      // Act
      const result = await getAvailableProductVariants('prod-123');

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockVariants);
      expect(mockSupabase().rpc).toHaveBeenCalledWith('get_available_product_variants', { product_uuid: 'prod-123' });
    });

    it('should return an error if rpc call fails', async () => {
      // Arrange
      const mockError = new Error('RPC error');
      mockSupabase.mockReturnValue({
        rpc: jest.fn().mockResolvedValue({ data: null, error: mockError }),
      });

      // Act
      const result = await getAvailableProductVariants('prod-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to fetch variant data.');
    });
  });

  describe('getProductVariants', () => {
    it('should return an error if the user is not authenticated', async () => {
      // Arrange
      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
        },
      });

      // Act
      const result = await getProductVariants('prod-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('User not authenticated.');
    });

    it('should fetch product variants successfully', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockVariants = [{ id: 'var-1', variant_values: '{"size":"M"}' }];
      const mockProduct = { business_id: 'user-123' };

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn((table: string) => {
          if (table === 'products_services') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
            };
          }
          if (table === 'product_variants') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              order: jest.fn().mockReturnThis(),
              limit: jest.fn().mockReturnThis(),
              range: jest.fn().mockResolvedValue({ data: mockVariants, error: null, count: mockVariants.length }),
            };
          }
          return {};
        }),
      });

      // Act
      const result = await getProductVariants('prod-123');

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual([{ id: 'var-1', variant_values: { size: 'M' } }]);
      expect(result.count).toBe(1);
    });

    it('should apply includeUnavailable filter', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { business_id: 'user-123' };
      const mockEq = jest.fn().mockReturnThis();

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn((table: string) => {
          if (table === 'products_services') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
            };
          }
          if (table === 'product_variants') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: mockEq,
              order: jest.fn().mockReturnThis(),
              limit: jest.fn().mockReturnThis(),
              range: jest.fn().mockResolvedValue({ data: [], error: null, count: 0 }),
            };
          }
          return {};
        }),
      });

      // Act
      await getProductVariants('prod-123', { includeUnavailable: false });

      // Assert
      expect(mockEq).toHaveBeenCalledWith('is_available', true);
    });

    it('should apply sorting options', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { business_id: 'user-123' };
      const mockOrder = jest.fn().mockReturnThis();

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn((table: string) => {
          if (table === 'products_services') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
            };
          }
          if (table === 'product_variants') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              order: mockOrder,
              limit: jest.fn().mockReturnThis(),
              range: jest.fn().mockResolvedValue({ data: [], error: null, count: 0 }),
            };
          }
          return {};
        }),
      });

      // Act & Assert
      await getProductVariants('prod-123', { sortBy: 'created_asc' });
      expect(mockOrder).toHaveBeenCalledWith('created_at', { ascending: true });

      await getProductVariants('prod-123', { sortBy: 'name_desc' });
      expect(mockOrder).toHaveBeenCalledWith('variant_name', { ascending: false });

      await getProductVariants('prod-123', { sortBy: 'price_asc' });
      expect(mockOrder).toHaveBeenCalledWith('base_price', { ascending: true, nullsFirst: false });
    });

    it('should apply limit and offset options', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { business_id: 'user-123' };
      const mockLimit = jest.fn().mockReturnThis();
      const mockRange = jest.fn().mockResolvedValue({ data: [], error: null, count: 0 });

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn((table: string) => {
          if (table === 'products_services') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
            };
          }
          if (table === 'product_variants') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              order: jest.fn().mockReturnThis(),
              limit: mockLimit,
              range: mockRange,
            };
          }
          return {};
        }),
      });

      // Act
      await getProductVariants('prod-123', { limit: 5, offset: 10 });

      // Assert
      expect(mockLimit).toHaveBeenCalledWith(5);
      expect(mockRange).toHaveBeenCalledWith(10, 14);
    });

    it('should handle query error', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { business_id: 'user-123' };
      const mockError = new Error('Query error');

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn((table: string) => {
          if (table === 'products_services') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
            };
          }
          if (table === 'product_variants') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              order: jest.fn().mockReturnThis(),
              limit: jest.fn().mockReturnThis(),
              range: jest.fn().mockResolvedValue({ data: null, error: mockError, count: null }),
            };
          }
          return {};
        }),
      });

      // Act
      const result = await getProductVariants('prod-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to fetch variants.');
    });
  });

  describe('getBusinessVariantStats', () => {
    it('should return an error if the user is not authenticated', async () => {
      // Arrange
      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
        },
      });

      // Act
      const result = await getBusinessVariantStats();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('User not authenticated.');
    });

    it('should fetch business variant stats successfully', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockStats = [{
        total_products: 10,
        products_with_variants: 5,
        total_variants: 20,
        available_variants: 15,
      }];

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        rpc: jest.fn().mockResolvedValue({ data: mockStats, error: null }),
      });

      // Act
      const result = await getBusinessVariantStats();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        total_products: 10,
        products_with_variants: 5,
        total_variants: 20,
        available_variants: 15,
      });
      expect(mockSupabase().rpc).toHaveBeenCalledWith('get_business_variant_stats', { business_uuid: 'user-123' });
    });

    it('should return default stats if no data is returned', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        rpc: jest.fn().mockResolvedValue({ data: [], error: null }),
      });

      // Act
      const result = await getBusinessVariantStats();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        total_products: 0,
        products_with_variants: 0,
        total_variants: 0,
        available_variants: 0,
      });
    });

    it('should return an error if rpc call fails', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockError = new Error('RPC error');

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        rpc: jest.fn().mockResolvedValue({ data: null, error: mockError }),
      });

      // Act
      const result = await getBusinessVariantStats();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to fetch variant statistics.');
    });
  });

  describe('checkVariantCombinationUnique', () => {
    it('should return an error if the user is not authenticated', async () => {
      // Arrange
      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
        },
      });

      // Act
      const result = await checkVariantCombinationUnique('prod-123', { size: 'M' });

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('User not authenticated.');
    });

    it('should return an error if product ownership check fails', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = null;
      const mockError = new Error('Ownership error');

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockProduct, error: mockError }),
      });

      // Act
      const result = await checkVariantCombinationUnique('prod-123', { size: 'M' });

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Product not found or access denied.');
    });

    it('should return isUnique as true if combination is unique', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { business_id: 'user-123' };

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
        rpc: jest.fn().mockResolvedValue({ data: true, error: null }),
      });

      // Act
      const result = await checkVariantCombinationUnique('prod-123', { size: 'M' });

      // Assert
      expect(result.success).toBe(true);
      expect(result.isUnique).toBe(true);
      expect(mockSupabase().rpc).toHaveBeenCalledWith('is_variant_combination_unique', {
        product_uuid: 'prod-123',
        variant_vals: { size: 'M' },
        exclude_variant_id: null,
      });
    });

    it('should return isUnique as false if combination is not unique', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { business_id: 'user-123' };

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
        rpc: jest.fn().mockResolvedValue({ data: false, error: null }),
      });

      // Act
      const result = await checkVariantCombinationUnique('prod-123', { size: 'M' });

      // Assert
      expect(result.success).toBe(true);
      expect(result.isUnique).toBe(false);
    });

    it('should pass excludeVariantId to rpc call', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { business_id: 'user-123' };

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
        rpc: jest.fn().mockResolvedValue({ data: true, error: null }),
      });

      // Act
      await checkVariantCombinationUnique('prod-123', { size: 'M' }, 'var-456');

      // Assert
      expect(mockSupabase().rpc).toHaveBeenCalledWith('is_variant_combination_unique', {
        product_uuid: 'prod-123',
        variant_vals: { size: 'M' },
        exclude_variant_id: 'var-456',
      });
    });

    it('should return an error if rpc call fails', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { business_id: 'user-123' };
      const mockError = new Error('RPC error');

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
        rpc: jest.fn().mockResolvedValue({ data: null, error: mockError }),
      });

      // Act
      const result = await checkVariantCombinationUnique('prod-123', { size: 'M' });

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to validate variant uniqueness.');
    });
  });
});