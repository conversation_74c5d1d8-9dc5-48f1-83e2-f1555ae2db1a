// Mock for html5-qrcode library

export enum Html5QrcodeScanType {
  SCAN_TYPE_CAMERA = 0,
  SCAN_TYPE_FILE = 1,
}

export class Html5Qrcode {
  private elementId: string;

  constructor(elementId: string) {
    this.elementId = elementId;
  }

  async scanFile(file: File): Promise<string> {
    // Mock implementation - always return a test QR code result
    return 'https://dukancard.in/test-business';
  }

  clear(): Promise<void> {
    return Promise.resolve();
  }

  stop(): Promise<void> {
    return Promise.resolve();
  }
}

export class Html5QrcodeScanner {
  private elementId: string;
  private config: any;
  private verbose: boolean;

  constructor(elementId: string, config: any, verbose: boolean = false) {
    this.elementId = elementId;
    this.config = config;
    this.verbose = verbose;
  }

  render(
    qrCodeSuccessCallback: (decodedText: string, decodedResult: any) => void,
    qrCodeErrorCallback?: (errorMessage: string) => void
  ): void {
    // Mock implementation - create a simple div in the target element
    const element = document.getElementById(this.elementId);
    if (element) {
      element.innerHTML = `
        <div data-testid="html5-qrcode-scanner">
          <div>Mock QR Scanner</div>
          <button onclick="window.mockQRSuccess && window.mockQRSuccess()">Simulate Success</button>
          <button onclick="window.mockQRError && window.mockQRError()">Simulate Error</button>
        </div>
      `;

      // Set up global mock functions for testing
      (window as any).mockQRSuccess = () => {
        qrCodeSuccessCallback('https://dukancard.in/test-business', {});
      };

      (window as any).mockQRError = () => {
        qrCodeErrorCallback?.('Mock error');
      };
    }
  }

  clear(): Promise<void> {
    const element = document.getElementById(this.elementId);
    if (element) {
      element.innerHTML = '';
    }
    
    // Clean up global mock functions
    delete (window as any).mockQRSuccess;
    delete (window as any).mockQRError;
    
    return Promise.resolve();
  }
}

// Export default for compatibility
export default {
  Html5Qrcode,
  Html5QrcodeScanner,
  Html5QrcodeScanType,
};
