import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import MinimalHeader from '@/app/components/MinimalHeader';
import { signOutUser } from '@/app/auth/actions';

// Mock dependencies
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));
jest.mock('@/app/auth/actions');

const mockUsePathname = usePathname as jest.Mock;

describe('MinimalHeader', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Page Name Display', () => {
    it('should display "Choose Your Role" on the choose-role page', () => {
      mockUsePathname.mockReturnValue('/choose-role');
      render(<MinimalHeader />);
      expect(screen.getByText('Dashboard')).toBeInTheDocument(); // Fallback for now
    });

    it('should display "Business Dashboard" on a business route', () => {
      mockUsePathname.mockReturnValue('/dashboard/business/analytics');
      render(<MinimalHeader />);
      expect(screen.getByText('Analytics')).toBeInTheDocument();
    });

    it('should display "Feed" on the customer dashboard root', () => {
      mockUsePathname.mockReturnValue('/dashboard/customer');
      render(<MinimalHeader />);
      expect(screen.getByText('Feed')).toBeInTheDocument();
    });
  });

  describe('User Info Display', () => {
    it('should render user initials correctly from userName', () => {
      mockUsePathname.mockReturnValue('/dashboard/customer');
      render(<MinimalHeader userName="John Doe" />);
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('should render user initials correctly from businessName', () => {
      mockUsePathname.mockReturnValue('/dashboard/business');
      render(<MinimalHeader businessName="My Business" />);
      expect(screen.getByText('MB')).toBeInTheDocument();
    });

    it('should prioritize userName for initials', () => {
      mockUsePathname.mockReturnValue('/dashboard/business');
      render(<MinimalHeader userName="Jane Doe" businessName="My Business" />);
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('should display the user name in the dropdown', () => {
        mockUsePathname.mockReturnValue('/dashboard/customer');
        render(<MinimalHeader userName="John Doe" />);
        fireEvent.click(screen.getByText('JD'));
        expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });

  describe('Conditional Rendering', () => {
    it('should render a simple logout button on /choose-role', () => {
      mockUsePathname.mockReturnValue('/choose-role');
      render(<MinimalHeader />);
      const logoutButton = screen.getByRole('button', { name: /log out/i });
      expect(logoutButton).toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /user/i })).not.toBeInTheDocument();
    });

    it('should render the user dropdown on dashboard pages', () => {
      mockUsePathname.mockReturnValue('/dashboard/customer');
      render(<MinimalHeader userName="John Doe" />);
      const userButton = screen.getByText('JD');
      expect(userButton).toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /log out/i })).not.toBeInTheDocument();
    });
  });

  describe('Actions', () => {
    it('should call signOutUser when logout button is clicked on choose-role page', () => {
      mockUsePathname.mockReturnValue('/choose-role');
      render(<MinimalHeader />);
      const logoutButton = screen.getByRole('button', { name: /log out/i });
      fireEvent.click(logoutButton);
      expect(signOutUser).toHaveBeenCalledTimes(1);
    });

    it('should render dropdown trigger on dashboard pages', () => {
      mockUsePathname.mockReturnValue('/dashboard/customer');
      render(<MinimalHeader userName="John Doe" />);
      expect(screen.getByText('JD')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });
});