import { deleteProductService } from '@/app/(dashboard)/dashboard/business/products/actions/deleteProduct';
import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe('deleteProductService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return an error if the user is not authenticated', async () => {
    // Arrange
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
      },
    });

    // Act
    const result = await deleteProductService('prod-123');

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toBe('User not authenticated.');
  });

  it('should return an error if itemId is not provided', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    });

    // Act
    const result = await deleteProductService('');

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toBe('Item ID is required.');
  });

  it('should delete a product successfully', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProduct = { id: 'prod-123', name: 'Test Product', images: [] };
    const mockDelete = jest.fn().mockReturnThis();
    const mockEq = jest.fn().mockReturnThis();
    const mockSingle = jest.fn().mockResolvedValue({ data: mockProduct, error: null });
    const mockRemove = jest.fn().mockResolvedValue({ data: null, error: null });
    const mockList = jest.fn().mockResolvedValue({ data: [], error: null });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: mockEq,
        single: mockSingle,
        delete: mockDelete,
      })),
      storage: {
        from: jest.fn(() => ({
          remove: mockRemove,
          list: mockList,
        })),
      }
    });

    // Act
    const result = await deleteProductService('prod-123');

    // Assert
    expect(result.success).toBe(true);
    expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
  });

  it('should delete a product and its associated images successfully', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProductWithImages = { 
      id: 'prod-123', 
      name: 'Test Product', 
      images: [
        'https://example.com/storage/v1/object/public/business/users/user-123/products/prod-123/image1.jpg',
        'https://example.com/storage/v1/object/public/business/users/user-123/products/prod-123/image2.png',
      ],
    };
    const mockDelete = jest.fn().mockReturnThis();
    const mockEq = jest.fn().mockReturnThis();
    const mockSingle = jest.fn().mockResolvedValue({ data: mockProductWithImages, error: null });
    const mockRemove = jest.fn().mockResolvedValue({ data: null, error: null });
    const mockList = jest.fn().mockResolvedValue({ data: [], error: null });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: mockEq,
        single: mockSingle,
        delete: mockDelete,
      })),
      storage: {
        from: jest.fn(() => ({
          remove: mockRemove,
          list: mockList,
        })),
      }
    });

    // Act
    const result = await deleteProductService('prod-123');

    // Assert
    expect(result.success).toBe(true);
    expect(mockRemove).toHaveBeenCalledWith(['users/user-123/products/prod-123/image1.jpg']);
    expect(mockRemove).toHaveBeenCalledWith(['users/user-123/products/prod-123/image2.png']);
    expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
  });

  it('should delete a product even if image deletion fails', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProductWithImages = { 
      id: 'prod-123', 
      name: 'Test Product', 
      images: [
        'https://example.com/storage/v1/object/public/business/users/user-123/products/prod-123/image1.jpg',
      ],
    };
    const mockDelete = jest.fn().mockReturnThis();
    const mockEq = jest.fn().mockReturnThis();
    const mockSingle = jest.fn().mockResolvedValue({ data: mockProductWithImages, error: null });
    const mockRemove = jest.fn().mockResolvedValue({ data: null, error: new Error('Failed to delete image') });
    const mockList = jest.fn().mockResolvedValue({ data: [], error: null });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: mockEq,
        single: mockSingle,
        delete: mockDelete,
      })),
      storage: {
        from: jest.fn(() => ({
          remove: mockRemove,
          list: mockList,
        })),
      }
    });

    // Act
    const result = await deleteProductService('prod-123');

    // Assert
    expect(result.success).toBe(true);
    expect(mockRemove).toHaveBeenCalled();
    expect(mockDelete).toHaveBeenCalled(); // Product should still be deleted from DB
    expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
  });
});