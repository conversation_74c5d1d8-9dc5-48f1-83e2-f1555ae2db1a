
import { render } from '@testing-library/react';
import ChooseRolePage from '@/app/(auth)/choose-role/page';
import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import ChooseRoleClient from '@/app/(auth)/choose-role/ChooseRoleClient';

// Mock dependencies
jest.mock('@/utils/supabase/server');
jest.mock('next/navigation', () => ({
  redirect: jest.fn((url: string) => {
    const error = new Error(`NEXT_REDIRECT: ${url}`);
    (error as any).digest = 'NEXT_REDIRECT';
    throw error;
  }),
}));
jest.mock('@/app/(auth)/choose-role/ChooseRoleClient', () => {
  return {
    __esModule: true,
    default: jest.fn(() => null),
  };
});

describe('ChooseRolePage', () => {
  const mockSupabase = createClient as jest.Mock;
  const mockRedirect = redirect as unknown as jest.Mock;
  const mockChooseRoleClient = ChooseRoleClient as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should redirect unauthenticated users to /login', async () => {
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: null } }),
      },
    });

    try {
      await ChooseRolePage({ searchParams: Promise.resolve({}) });
    } catch (e: any) {
      expect(e.message).toMatch(/^NEXT_REDIRECT: \/login$/);
    }
    expect(mockRedirect).toHaveBeenCalledWith('/login');
  });

  it('should redirect users with an existing customer profile to /dashboard/customer', async () => {
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'user-123' } } }),
      },
      from: jest.fn((table: string) => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({
          data: table === 'customer_profiles' ? { id: 'user-123' } : null,
          error: null,
        }),
      })),
    });

    try {
      await ChooseRolePage({ searchParams: Promise.resolve({}) });
    } catch (e: any) {
      expect(e.message).toMatch(/^NEXT_REDIRECT: \/dashboard\/customer$/);
    }
    expect(mockRedirect).toHaveBeenCalledWith('/dashboard/customer');
  });

  it('should redirect users with an existing business profile to /dashboard/business', async () => {
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'user-123' } } }),
      },
      from: jest.fn((table: string) => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({
          data: table === 'business_profiles' ? { id: 'user-123' } : null,
          error: null,
        }),
      })),
    });

    try {
      await ChooseRolePage({ searchParams: Promise.resolve({}) });
    } catch (e: any) {
      expect(e.message).toMatch(/^NEXT_REDIRECT: \/dashboard\/business$/);
    }
    expect(mockRedirect).toHaveBeenCalledWith('/dashboard/business');
  });

  it('should render the ChooseRoleClient component for authenticated users without a profile', async () => {
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'user-123' } } }),
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
      })),
    });

    const searchParams = { redirect: 'some-slug', message: 'hello' };
    const Component = await ChooseRolePage({ searchParams: Promise.resolve(searchParams) });
    render(Component);

    expect(mockChooseRoleClient).toHaveBeenCalledWith(
      {
        userId: 'user-123',
        redirectSlug: 'some-slug',
        message: 'hello',
      },
      undefined
    );
  });

  it('should handle errors when checking for profiles and redirect to login', async () => {
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'user-123' } } }),
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({ data: null, error: new Error('DB Error') }),
      })),
    });

    try {
      await ChooseRolePage({ searchParams: Promise.resolve({}) });
    } catch (e: any) {
      expect(e.message).toMatch(/^NEXT_REDIRECT: \/login\?message=Error checking profile status$/);
    }
    expect(mockRedirect).toHaveBeenCalledWith('/login?message=Error checking profile status');
  });
});
