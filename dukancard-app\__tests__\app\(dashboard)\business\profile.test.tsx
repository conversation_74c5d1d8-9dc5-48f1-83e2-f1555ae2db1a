import React from 'react';
import { render, waitFor, fireEvent } from '@testing-library/react-native';
import BusinessProfileScreen from '@/app/(dashboard)/business/profile';
import { useAuth } from '@/src/contexts/AuthContext';
import { useRouter } from 'expo-router';
import { getBusinessProfile } from '@/backend/supabase/services/common/profileService';

// Mock external modules
jest.mock('@/src/contexts/AuthContext');
jest.mock('expo-router');
jest.mock('@/backend/supabase/services/common/profileService');
jest.mock('@/styles/dashboard/business/business-profile-styles', () => ({
  createBusinessProfileStyles: jest.fn(() => ({
    container: {},
    profileContent: {},
    separator: {},
    profileMenuItem: {},
    menuItemText: {},
    themeSection: {},
    themeSectionHeader: {},
    themeSectionTitle: {},
    logoutButton: {},
    logoutButtonText: {},
    errorContainer: {},
  })),
}));
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: jest.fn(() => 'light'),
}));
jest.mock('@/src/components/shared/layout/DashboardLayout', () => ({
  DashboardLayout: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));
jest.mock('@/src/components/ui/SkeletonLoader', () => ({
  ProfileSkeleton: () => <></>,
}));
jest.mock('@/src/components/ui/ErrorState', () => ({
  ErrorState: () => <></>,
}));
jest.mock('@/src/components/ui/ThemeToggleButton', () => ({
  ThemeToggleButton: () => <></>,
}));
jest.mock('@/src/components/ui/ComingSoonModal', () => ({
  __esModule: true,
  default: () => <></>,
}));
jest.mock('@/src/components/modals/business/ManageCardModal', () => ({
  __esModule: true,
  default: () => <></>,
}));
jest.mock('@/src/components/modals/business/ManageProductsModal', () => ({
  __esModule: true,
  default: () => <></>,
}));
jest.mock('@/src/components/shared/ProfileHeader', () => ({
  ProfileHeader: () => <></>,
}));
jest.mock('@/src/components/modals/business/ShareBusinessCardModal', () => ({
  __esModule: true,
  default: () => <></>,
}));
jest.mock('@react-navigation/native', () => ({
  useFocusEffect: jest.fn(),
}));


describe('BusinessProfileScreen', () => {
  const mockUser = { id: '123', user_metadata: { name: 'Test Business' }, email: '<EMAIL>' };
  const mockProfileStatus = { roleStatus: { hasBusinessProfile: true, role: 'business' } };
  const mockBusinessProfile = {
    business_name: 'Test Business',
    logo_url: 'http://example.com/logo.png',
    total_likes: 100,
    total_subscriptions: 50,
    average_rating: 4.5,
    business_slug: 'test-business-slug',
  };

  beforeEach(() => {
    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
      profileStatus: mockProfileStatus,
      signOut: jest.fn(),
    });
    (useRouter as jest.Mock).mockReturnValue({
      push: jest.fn(),
      replace: jest.fn(),
    });
    (getBusinessProfile as jest.Mock).mockResolvedValue({ success: true, data: mockBusinessProfile });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly when data is loaded', async () => {
    const { getByText } = render(<BusinessProfileScreen />);

    await waitFor(() => {
      expect(getByText('Show My Digital Card')).toBeDefined();
      expect(getByText('Manage Card')).toBeDefined();
      expect(getByText('Manage Products/Services')).toBeDefined();
      expect(getByText('Gallery')).toBeDefined();
      expect(getByText('Analytics')).toBeDefined();
      expect(getByText('Manage Plan')).toBeDefined();
      expect(getByText('App Theme')).toBeDefined();
      expect(getByText('Logout')).toBeDefined();
    });
  });

  it('shows loading skeleton initially', () => {
    (getBusinessProfile as jest.Mock).mockReturnValue(new Promise(() => {})); // Never resolve

    const { queryByText } = render(<BusinessProfileScreen />);
    expect(queryByText('Manage Card')).toBeNull(); // Should not be visible yet
  });

  it('displays error state if profile loading fails', async () => {
    (getBusinessProfile as jest.Mock).mockResolvedValue({ success: false, error: 'Profile error' });
    const { findByText } = render(<BusinessProfileScreen />);
    await findByText('Unable to load data');
  });

  it('redirects to customer dashboard if user is customer', async () => {
    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
      profileStatus: { roleStatus: { hasCustomerProfile: true, role: 'customer' } },
      signOut: jest.fn(),
    });
    (getBusinessProfile as jest.Mock).mockResolvedValue({ success: false, error: 'No business profile' });

    const { push, replace } = useRouter();
    render(<BusinessProfileScreen />);

    await waitFor(() => {
      expect(replace).toHaveBeenCalledWith('/(dashboard)/customer');
    });
  });

  it('opens Manage Card modal when "Manage Card" is pressed', async () => {
    const { getByText } = render(<BusinessProfileScreen />);
    await waitFor(() => expect(getByText('Manage Card')).toBeDefined());
    fireEvent.press(getByText('Manage Card'));
    // Assert modal visibility if possible, or just that the action was triggered
  });

  it('opens Manage Products/Services modal when pressed', async () => {
    const { getByText } = render(<BusinessProfileScreen />);
    await waitFor(() => expect(getByText('Manage Products/Services')).toBeDefined());
    fireEvent.press(getByText('Manage Products/Services'));
  });

  it('opens Share Business Card modal when pressed', async () => {
    const { getByText } = render(<BusinessProfileScreen />);
    await waitFor(() => expect(getByText('Show My Digital Card')).toBeDefined());
    fireEvent.press(getByText('Show My Digital Card'));
  });

  it('shows Coming Soon modal for Gallery', async () => {
    const { getByText } = render(<BusinessProfileScreen />);
    await waitFor(() => expect(getByText('Gallery')).toBeDefined());
    fireEvent.press(getByText('Gallery'));
    // In a real test, you'd assert that the ComingSoonModal is visible with correct props
  });

  it('calls signOut when Logout is pressed', async () => {
    const signOutMock = jest.fn().mockResolvedValue({ error: null });
    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
      profileStatus: mockProfileStatus,
      signOut: signOutMock,
    });

    const { getByText } = render(<BusinessProfileScreen />);
    await waitFor(() => expect(getByText('Logout')).toBeDefined());
    fireEvent.press(getByText('Logout'));
    await waitFor(() => expect(signOutMock).toHaveBeenCalledTimes(1));
  });

  // Add more tests for:
  // - Refresh control functionality
  // - Theme toggle interaction
  // - Other Coming Soon modal interactions (Analytics, Manage Plan)
});
