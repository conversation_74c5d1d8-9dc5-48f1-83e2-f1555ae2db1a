import { createClient } from "@/utils/supabase/server";
import { TABLES, COLUMNS } from "@dukancard/lib/supabase/constants";
import { Tables } from "@dukancard/types/supabase";

export async function getUserProfile(userId: string) {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .select("*")
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error("Error fetching user profile:", error.message);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error("Unexpected error fetching user profile:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

export async function createUserProfile(profile: Tables<'customer_profiles', 'Insert'>) {
  const supabase = createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .insert([profile])
      .select();

    if (error) {
      console.error("Error creating user profile:", error.message);
      return { data: null, error: error.message };
    }
    return { data: data[0], error: null };
  } catch (err) {
    console.error("Unexpected error creating user profile:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}
