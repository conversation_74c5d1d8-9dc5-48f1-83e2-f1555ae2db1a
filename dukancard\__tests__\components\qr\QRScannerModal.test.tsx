/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import QRScannerModal from '@/components/qr/QRScannerModal';

// Mock html5-qrcode
jest.mock('html5-qrcode');

// Mock the QRScanner component since it uses external libraries
jest.mock('@/components/qr/QRScanner', () => {
  return function MockQRScanner({ onScanSuccess, onScanError }: any) {
    return (
      <div data-testid="qr-scanner">
        <button
          onClick={() => onScanSuccess('test-business')}
          data-testid="mock-scan-success"
        >
          Simulate Scan Success
        </button>
        <button
          onClick={() => onScanError('Camera not available')}
          data-testid="mock-scan-error"
        >
          Simulate Scan Error
        </button>
      </div>
    );
  };
});

// Mock camera utils
jest.mock('@/lib/utils/cameraUtils');

// Mock QR code utils
jest.mock('@/lib/utils/qrCodeUtils', () => ({
  validateQRCodeForUser: jest.fn(() => ({
    isValid: true,
    businessSlug: 'test-business'
  }))
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

describe('QRScannerModal', () => {
  const mockOnClose = jest.fn();
  const mockOnScanSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render modal when open', () => {
    const { container } = render(
      <QRScannerModal
        isOpen={true}
        onClose={mockOnClose}
        onScanSuccess={mockOnScanSuccess}
      />
    );

    expect(container).toBeInTheDocument();
    expect(screen.getByText('Scan QR Code')).toBeInTheDocument();
    expect(screen.getByText('Camera')).toBeInTheDocument();
    expect(screen.getByText('Upload')).toBeInTheDocument();
  });

  it('should not render modal when closed', () => {
    render(
      <QRScannerModal
        isOpen={false}
        onClose={mockOnClose}
        onScanSuccess={mockOnScanSuccess}
      />
    );

    expect(screen.queryByText('Scan QR Code')).not.toBeInTheDocument();
  });
});
