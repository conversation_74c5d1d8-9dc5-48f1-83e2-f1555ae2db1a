import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { getSubscription } from "@/lib/razorpay/services/subscription";
import { TABLES, COLUMNS } from "@/lib/supabase/constants";
import { Database } from "@/types/supabase";

/**
 * GET /api/subscriptions/my
 *
 * Fetches the current user's subscription from Razorpay
 *
 * Example response:
 * ```json
 * {
 *   "success": true,
 *   "data": {
 *     "id": "sub_00000000000001",
 *     "entity": "subscription",
 *     "plan_id": "plan_00000000000001",
 *     "customer_id": "cust_D00000000000001",
 *     "status": "active",
 *     "current_start": 1577355871,
 *     "current_end": 1582655400,
 *     "ended_at": null,
 *     "quantity": 1,
 *     "notes": {
 *       "notes_key_1": "Tea, <PERSON>, Hot",
 *       "notes_key_2": "Tea, <PERSON>… decaf."
 *     },
 *     "charge_at": 1577385991,
 *     "offer_id": "offer_JHD834hjbxzhd38d",
 *     "start_at": 1577385991,
 *     "end_at": 1603737000,
 *     "auth_attempts": 0,
 *     "total_count": 6,
 *     "paid_count": 1,
 *     "customer_notify": true,
 *     "created_at": 1577356081,
 *     "expire_by": 1577485991,
 *     "short_url": "https://rzp.io/i/z3b1R61A9",
 *     "has_scheduled_changes": false,
 *     "change_scheduled_at": null,
 *     "remaining_count": 5
 *   }
 * }
 * ```
 */
export async function GET() {
  try {
    // Verify authentication using Supabase
    const supabase = await createClient<Database>();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get the user's subscription from the payment_subscriptions table
    const { data: subscription, error: subscriptionError } = await supabase
      .from(TABLES.PAYMENT_SUBSCRIPTIONS)
      .select("*")
      .eq(COLUMNS.BUSINESS_PROFILE_ID, user.id)
      .order(COLUMNS.CREATED_AT, { ascending: false })
      .limit(1)
      .single();

    if (subscriptionError) {
      // If no subscription is found, return a 404
      if (subscriptionError.code === "PGRST116") {
        return NextResponse.json(
          { success: false, error: "No subscription found for this user" },
          { status: 404 }
        );
      }

      console.error("[RAZORPAY_ERROR] Error fetching subscription:", subscriptionError);
      return NextResponse.json(
        { success: false, error: "Error fetching subscription" },
        { status: 500 }
      );
    }

    // Check if the user has a Razorpay subscription ID
    if (!subscription.razorpay_subscription_id) {
      return NextResponse.json(
        { success: false, error: "No Razorpay subscription found for this user" },
        { status: 404 }
      );
    }

    // Fetch the subscription from Razorpay
    const result = await getSubscription(subscription.razorpay_subscription_id);

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    // Return the subscription with additional database information
    return NextResponse.json(
      {
        success: true,
        data: {
          ...result.data,
          db_subscription: {
            id: subscription.id,
            plan_id: subscription.plan_id,
            plan_cycle: subscription.plan_cycle,
            subscription_status: subscription.subscription_status,
            subscription_start_date: subscription.subscription_start_date,
            subscription_expiry_time: subscription.subscription_expiry_time,
            last_payment_date: subscription.last_payment_date,
            last_payment_method: subscription.last_payment_method
          }
        }
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error fetching subscription:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 }
    );
  }
}
