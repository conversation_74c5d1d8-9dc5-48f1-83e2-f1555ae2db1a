'use client';

import React, { useEffect, useState, useTran<PERSON>tion, forwardRef, useImperativeHandle } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useSearchParams } from 'next/navigation';
import { updateCustomerAddress, type AddressFormState } from '../actions';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { MapPin, Save, Loader2, Globe, Building2, Info } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Form,
} from "@/components/ui/form";
import { usePincodeDetails } from './hooks/usePincodeDetails';

// Address form schema with proper validation
const AddressFormSchema = z.object({
  address: z
    .string()
    .max(100, { message: "Address cannot exceed 100 characters." })
    .optional()
    .or(z.literal("")),
  pincode: z
    .string()
    .min(1, { message: "Pincode is required" })
    .regex(/^\d{6}$/, { message: "Must be a valid 6-digit pincode" }),
  city: z
    .string()
    .min(1, { message: "City is required" })
    .refine((val) => val.trim().length > 0, { message: "City cannot be empty" }),
  state: z
    .string()
    .min(1, { message: "State is required" })
    .refine((val) => val.trim().length > 0, { message: "State cannot be empty" }),
  locality: z
    .string()
    .min(1, { message: "Locality is required" })
    .refine((val) => val.trim().length > 0, { message: "Locality cannot be empty" }),
});

type AddressFormData = z.infer<typeof AddressFormSchema>;

interface AddressFormProps {
  initialData?: {
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
  };
  hideSubmitButton?: boolean;
}

export interface AddressFormRef {
  getFormData: () => AddressFormData | null;
  validateForm: () => boolean;
  getFormErrors: () => Record<string, unknown>;
}

const AddressForm = forwardRef<AddressFormRef, AddressFormProps>(
  ({ initialData, hideSubmitButton = false }, ref) => {
  const [isPending, startTransition] = useTransition();
  const [formState, setFormState] = useState<AddressFormState>({
    message: null,
    success: false,
    errors: {}
  });
  const searchParams = useSearchParams();
  const redirectMessage = searchParams.get('message');

  const form = useForm<AddressFormData>({
    resolver: zodResolver(AddressFormSchema),
    defaultValues: {
      address: initialData?.address || '',
      pincode: initialData?.pincode || '',
      city: initialData?.city || '',
      state: initialData?.state || '',
      locality: initialData?.locality || '',
    },
  });

  // Expose form methods via ref
  useImperativeHandle(ref, () => ({
    getFormData: () => {
      // Always return current values - let the parent handle validation
      const values = form.getValues();
      return values;
    },
    validateForm: () => {
      // Trigger validation and return the result
      form.trigger();
      return Object.keys(form.formState.errors).length === 0;
    },
    getFormErrors: () => {
      return form.formState.errors;
    }
  }));

  // Use pincode details hook
  const { isPincodeLoading, availableLocalities, handlePincodeChange } =
    usePincodeDetails({
      form,
      initialPincode: initialData?.pincode,
      initialLocality: initialData?.locality,
    });

  // Handle redirect message toast
  useEffect(() => {
    if (redirectMessage) {
      toast.info(redirectMessage);
    }
  }, [redirectMessage]);

  // Handle form state changes
  useEffect(() => {
    if (formState.message) {
      if (formState.success) {
        toast.success(formState.message);
        // Reset form state after success
        setFormState({ message: null, success: false, errors: {} });
      } else {
        toast.error(formState.message);
      }
    }
  }, [formState]);

  const onSubmit = (data: AddressFormData) => {
    const formData = new FormData();
    formData.append('address', data.address || '');
    formData.append('pincode', data.pincode);
    formData.append('city', data.city);
    formData.append('state', data.state);
    formData.append('locality', data.locality);

    startTransition(async () => {
      try {
        const initialState: AddressFormState = {
          message: null,
          errors: {},
          success: false
        };

        const result = await updateCustomerAddress(initialState, formData);
        setFormState(result);
      } catch (error) {
        console.error('Error submitting address form:', error);
        setFormState({
          message: 'An unexpected error occurred. Please try again.',
          success: false,
          errors: {}
        });
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* Show redirect message if present */}
      {redirectMessage && (
        <Alert className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/50">
          <Info className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <AlertDescription className="text-amber-800 dark:text-amber-200">
            {redirectMessage}
          </AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {/* Address Field (Optional) */}
          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">
                  Address (Optional)
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="e.g., House/Flat No., Street Name"
                    {...field}
                    value={field.value ?? ""}
                    className="w-full"
                  />
                </FormControl>
                <FormDescription className="text-xs text-muted-foreground">
                  Your street address or building details
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Pincode Field */}
          <FormField
            control={form.control}
            name="pincode"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium flex items-center gap-1.5">
                  <Globe className="h-4 w-4 text-primary" />
                  Pincode *
                </FormLabel>
                <div className="flex items-center gap-2">
                  <FormControl className="flex-1">
                    <Input
                      placeholder="e.g., 751001"
                      {...field}
                      value={field.value ?? ""}
                      maxLength={6}
                      type="number"
                      onChange={(e) => {
                        field.onChange(e);
                        if (e.target.value.length === 6) {
                          handlePincodeChange(e.target.value);
                        }
                      }}
                      onInput={(e) => {
                        const target = e.target as HTMLInputElement;
                        target.value = target.value.replace(/[^0-9]/g, "");
                      }}
                    />
                  </FormControl>
                  {isPincodeLoading && (
                    <Loader2 className="h-4 w-4 animate-spin text-primary" />
                  )}
                </div>
                <FormDescription className="text-xs text-muted-foreground">
                  6-digit pincode to auto-fill city and state
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* City and State fields */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium flex items-center gap-1.5">
                    <MapPin className="h-4 w-4 text-primary/50" />
                    City *
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Auto-filled from Pincode"
                      {...field}
                      value={field.value ?? ""}
                      className="bg-muted cursor-not-allowed"
                      readOnly
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium flex items-center gap-1.5">
                    <MapPin className="h-4 w-4 text-primary/50" />
                    State *
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Auto-filled from Pincode"
                      {...field}
                      value={field.value ?? ""}
                      className="bg-muted cursor-not-allowed"
                      readOnly
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Locality Field */}
          <FormField
            control={form.control}
            name="locality"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium flex items-center gap-1.5">
                  <Building2 className="h-4 w-4 text-primary" />
                  Locality / Area *
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value ?? ""}
                  disabled={availableLocalities.length === 0}
                >
                  <FormControl>
                    <SelectTrigger
                      disabled={availableLocalities.length === 0}
                      className="w-full"
                    >
                      <SelectValue
                        placeholder={
                          availableLocalities.length === 0
                            ? "Enter Pincode first"
                            : "Select your locality"
                        }
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="w-full">
                    {availableLocalities.map((loc) => (
                      <SelectItem key={loc} value={loc}>
                        {loc}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription className="text-xs text-muted-foreground">
                  Select the specific area within the pincode
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {!hideSubmitButton && (
            <div className="mt-6 flex justify-end">
              <Button
                type="submit"
                disabled={isPending}
                className="bg-primary hover:bg-primary/90 text-primary-foreground"
              >
                {isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update Address
                  </>
                )}
              </Button>
            </div>
          )}
        </form>
      </Form>
    </div>
  );
});

export default AddressForm;
