import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import ActivitiesPageClient from '@/app/(dashboard)/dashboard/business/activities/components/ActivitiesPageClient';
import { getBusinessActivities, getUnreadActivitiesCount } from '@/lib/actions/activities';
import { realtimeService } from '@/lib/services/realtimeService';
import ActivityItem from '@/app/(dashboard)/dashboard/business/activities/components/ActivityItem';

jest.mock('@/app/(dashboard)/dashboard/business/activities/components/ActivityItem', () => {
  return {
    __esModule: true,
    default: jest.fn(({ activity, onView }) => (
      <div data-testid="activity-item" onClick={() => onView && onView(activity.id)}>
        {activity.activity_type === 'like' && `Liked by ${activity.user_profile?.name}`}
        {activity.activity_type === 'subscribe' && `Subscribed by ${activity.user_profile?.name}`}
        {activity.activity_type === 'rating' && `Rated by ${activity.user_profile?.name} with ${activity.rating_value}`}
      </div>
    )),
  };
});
import { BusinessActivity } from '@/lib/actions/activities';
import { toast } from 'sonner';

// Mock activities actions
jest.mock('@/lib/actions/activities');

// Mock Supabase client
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      on: jest.fn(() => ({
        subscribe: jest.fn(),
        unsubscribe: jest.fn(),
      })),
    })),
    channel: jest.fn(() => ({
      on: jest.fn(() => ({
        subscribe: jest.fn(),
        unsubscribe: jest.fn(),
      })),
    })),
  })),
}));

// Mock realtimeService
jest.mock('@/lib/services/realtimeService');

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

const mockBusinessActivities: BusinessActivity[] = [
  {
    id: 'activity-1',
    business_profile_id: 'test-business-id',
    user_id: 'user-1',
    activity_type: 'like',
    created_at: new Date().toISOString(),
    is_read: false,
    user_profile: {
      id: 'user-1',
      name: 'John Doe',
      avatar_url: null,
      is_business: false,
    },
    rating_value: null,
    post_id: null,
    post_type: null,
  },
  {
    id: 'activity-2',
    business_profile_id: 'test-business-id',
    user_id: 'user-2',
    activity_type: 'subscribe',
    created_at: new Date().toISOString(),
    is_read: true,
    user_profile: {
      id: 'user-2',
      name: 'Jane Smith',
      avatar_url: null,
      is_business: false,
    },
    rating_value: null,
    post_id: null,
    post_type: null,
  },
];

describe('ActivitiesPageClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (getBusinessActivities as jest.Mock).mockResolvedValue({
      activities: mockBusinessActivities,
      count: mockBusinessActivities.length,
    });
    (getUnreadActivitiesCount as jest.Mock).mockResolvedValue({
      count: 1,
    });
    (realtimeService.subscribeToBusinessActivities as jest.Mock).mockReturnValue({
      unsubscribe: jest.fn(),
    });
    (realtimeService.subscribeToTable as jest.Mock).mockReturnValue({
      unsubscribe: jest.fn(),
    });
  });

  const mockProps = {
    initialActivities: [],
    totalCount: 0,
    unreadCount: 0,
    businessProfileId: 'test-business-id',
  };

  it('renders without crashing', async () => {
    render(<ActivitiesPageClient {...mockProps} />);
    await screen.findByText('Business Activities');
  });

  it('displays initial activities if provided', async () => {
    const propsWithInitialData = {
      ...mockProps,
      initialActivities: mockBusinessActivities,
      totalCount: mockBusinessActivities.length,
      unreadCount: 1,
    };
    render(<ActivitiesPageClient {...propsWithInitialData} />);
    await waitFor(() => {
      expect(screen.getByText('John Doe liked your business')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith subscribed to your business')).toBeInTheDocument();
    });
  });

  it('displays loading skeletons when activities are loading', async () => {
    (getBusinessActivities as jest.Mock).mockResolvedValueOnce({
      activities: [],
      count: 0,
    });
    (getBusinessActivities as jest.Mock).mockImplementationOnce(() => new Promise(() => {})); // Never resolve to keep loading

    render(<ActivitiesPageClient {...mockProps} />);
    await waitFor(() => {
      expect(screen.getAllByTestId('activity-skeleton')).toHaveLength(4);
    });
  });

  it('displays empty state when no activities are found', async () => {
    (getBusinessActivities as jest.Mock).mockResolvedValue({
      activities: [],
      count: 0,
    });
    render(<ActivitiesPageClient {...mockProps} />);
    await waitFor(() => {
      expect(screen.getByText('No activities yet')).toBeInTheDocument();
    });
  });

  it('handles tab changes and reloads activities', async () => {
    render(<ActivitiesPageClient {...mockProps} />);
    await screen.findByText('Business Activities');

    (getBusinessActivities as jest.Mock).mockClear();

    fireEvent.click(screen.getByRole('tab', { name: /likes/i }));

    await waitFor(() => {
      expect(getBusinessActivities).toHaveBeenCalledWith(expect.objectContaining({
        filterBy: 'like',
        page: 1,
      }));
    });
  });

  it('handles sort changes and reloads activities', async () => {
    render(<ActivitiesPageClient {...mockProps} />);
    await screen.findByText('Business Activities');

    (getBusinessActivities as jest.Mock).mockClear();

    fireEvent.mouseDown(screen.getByRole('combobox', { name: /sort by/i }));
    await waitFor(() => screen.getByText('Oldest First'));
    fireEvent.click(screen.getByText('Oldest First'));

    await waitFor(() => {
      expect(getBusinessActivities).toHaveBeenCalledWith(expect.objectContaining({
        sortBy: 'oldest',
        page: 1,
      }));
    });
  });

  it('handles refresh button click and reloads activities', async () => {
    render(<ActivitiesPageClient {...mockProps} />);
    await screen.findByText('Business Activities');

    (getBusinessActivities as jest.Mock).mockClear();

    fireEvent.click(screen.getByRole('button', { name: /refresh/i }));

    await waitFor(() => {
      expect(getBusinessActivities).toHaveBeenCalledWith(expect.objectContaining({
        page: 1,
      }));
    });
  });

  it('loads more activities on scroll', async () => {
    (getBusinessActivities as jest.Mock)
      .mockResolvedValueOnce({
        activities: mockBusinessActivities,
        count: 20, // Simulate more activities available
      })
      .mockResolvedValueOnce({
        activities: [
          { ...mockBusinessActivities[0], id: 'activity-3' },
          { ...mockBusinessActivities[1], id: 'activity-4' },
        ],
        count: 20,
      });

    render(<ActivitiesPageClient {...mockProps} />);
    await waitFor(() => {
      expect(screen.getByText('John Doe liked your business')).toBeInTheDocument();
    });

    // Simulate scrolling by triggering the IntersectionObserver
    act(() => {
      const observerCallback = (realtimeService.subscribeToBusinessActivities as jest.Mock).mock.calls[0][1];
      observerCallback([{ isIntersecting: true }]);
    });

    await waitFor(() => {
      expect(getBusinessActivities).toHaveBeenCalledTimes(2);
      expect(screen.getByText('activity-3')).toBeInTheDocument(); // Assuming activity-3 is rendered
    });
  });

  it('handles real-time new activity inserts', async () => {
    render(<ActivitiesPageClient {...mockProps} />);
    await screen.findByText('Business Activities');

    const newActivity: BusinessActivity = {
      id: 'activity-new',
      business_profile_id: 'test-business-id',
      user_id: 'user-new',
      activity_type: 'like',
      created_at: new Date().toISOString(),
      is_read: false,
      user_profile: {
        id: 'user-new',
        name: 'New User',
        avatar_url: null,
        is_business: false,
      },
      rating_value: null,
      post_id: null,
      post_type: null,
    };

    (getBusinessActivities as jest.Mock).mockResolvedValueOnce({
      activities: [newActivity, ...mockBusinessActivities],
      count: mockBusinessActivities.length + 1,
    });

    // Simulate real-time insert
    await act(async () => {
      const callback = (realtimeService.subscribeToBusinessActivities as jest.Mock).mock.calls[0][1];
      await callback({
        eventType: 'INSERT',
        new: newActivity,
        old: null,
        errors: null,
        schema: 'public',
        table: 'business_activities',
      });
    });

    await waitFor(() => {
      expect(screen.getByText('New User liked your business')).toBeInTheDocument();
      expect(toast.info).toHaveBeenCalledWith('New Activity', expect.objectContaining({
        description: 'New User liked your business',
      }));
      expect(getUnreadActivitiesCount).toHaveBeenCalled();
    });
  });

  it('updates unread count and marks activities as read when viewed', async () => {
    const unreadActivity: BusinessActivity = {
      ...mockBusinessActivities[0],
      is_read: false,
    };
    const propsWithUnread = {
      ...mockProps,
      initialActivities: [unreadActivity],
      unreadCount: 1,
    };

    render(<ActivitiesPageClient {...propsWithUnread} />);
    await screen.findByText('Business Activities');

    // Simulate activity coming into view
    act(() => {
      const activityItem = screen.getByText('Liked by John Doe').closest('[data-testid="activity-item"]');
      if (activityItem) {
        fireEvent.click(activityItem);
      }
    });

    await waitFor(() => {
      expect(getUnreadActivitiesCount).toHaveBeenCalled();
      // Expect the unread badge to disappear or show 0
      expect(screen.queryByText('1 unread')).not.toBeInTheDocument();
    });
  });
});
