import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';

/**
 * Complete mock UnifiedPost factory for testing
 * Ensures all required fields are present to avoid TypeScript errors
 */
export const createMockUnifiedPost = (overrides: Partial<UnifiedPost> = {}): UnifiedPost => ({
  id: 'mock-id',
  post_source: 'customer',
  author_id: 'mock-author-id',
  content: 'Mock post content',
  image_url: null,
  created_at: '2023-01-01T12:00:00Z',
  updated_at: '2023-01-01T12:00:00Z',
  city_slug: null,
  state_slug: null,
  locality_slug: null,
  pincode: null,
  product_ids: [],
  mentioned_business_ids: [],
  author_name: 'Mock Author',
  author_avatar: null,
  business_slug: null,
  phone: null,
  whatsapp_number: null,
  business_plan: null,
  ...overrides
});

/**
 * Create multiple mock posts with sequential IDs
 */
export const createMockUnifiedPosts = (count: number, baseOverrides: Partial<UnifiedPost> = {}): UnifiedPost[] => {
  return Array.from({ length: count }, (_, index) => 
    createMockUnifiedPost({
      id: `mock-id-${index + 1}`,
      ...baseOverrides
    })
  );
};

/**
 * Create mock customer posts
 */
export const createMockCustomerPosts = (count: number = 3): UnifiedPost[] => {
  return createMockUnifiedPosts(count, {
    post_source: 'customer',
    business_plan: null,
    business_slug: null,
    phone: null,
    whatsapp_number: null
  });
};

/**
 * Create mock business posts with different plans
 */
export const createMockBusinessPosts = (count: number = 4): UnifiedPost[] => {
  const plans = ['enterprise', 'pro', 'growth', 'basic', 'free'];
  return Array.from({ length: count }, (_, index) => 
    createMockUnifiedPost({
      id: `business-${index + 1}`,
      post_source: 'business',
      business_plan: plans[index % plans.length],
      business_slug: `business-slug-${index + 1}`,
      phone: `+1234567890${index}`,
      whatsapp_number: `+1234567890${index}`
    })
  );
};

/**
 * Create a mixed set of customer and business posts
 */
export const createMockMixedPosts = (): UnifiedPost[] => {
  const customerPosts = createMockCustomerPosts(3);
  const businessPosts = createMockBusinessPosts(4);
  return [...customerPosts, ...businessPosts];
};
