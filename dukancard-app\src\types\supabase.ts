export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      blogs: {
        Row: {
          author_email: string | null
          author_name: string
          categories: string[] | null
          content: string
          created_at: string
          excerpt: string | null
          featured_image_url: string | null
          id: string
          meta_description: string | null
          meta_title: string | null
          published_at: string | null
          reading_time_minutes: number | null
          slug: string | null
          status: string
          tags: string[] | null
          title: string
          updated_at: string
        }
        Insert: {
          author_email?: string | null
          author_name?: string
          categories?: string[] | null
          content: string
          created_at?: string
          excerpt?: string | null
          featured_image_url?: string | null
          id?: string
          meta_description?: string | null
          meta_title?: string | null
          published_at?: string | null
          reading_time_minutes?: number | null
          slug?: string | null
          status?: string
          tags?: string[] | null
          title: string
          updated_at?: string
        }
        Update: {
          author_email?: string | null
          author_name?: string
          categories?: string[] | null
          content?: string
          created_at?: string
          excerpt?: string | null
          featured_image_url?: string | null
          id?: string
          meta_description?: string | null
          meta_title?: string | null
          published_at?: string | null
          reading_time_minutes?: number | null
          slug?: string | null
          status?: string
          tags?: string[] | null
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      business_activities: {
        Row: {
          activity_type: string
          business_profile_id: string
          created_at: string
          id: string
          is_read: boolean
          post_id: string | null
          post_type: string | null
          rating_value: number | null
          user_id: string
        }
        Insert: {
          activity_type: string
          business_profile_id: string
          created_at?: string
          id?: string
          is_read?: boolean
          post_id?: string | null
          post_type?: string | null
          rating_value?: number | null
          user_id: string
        }
        Update: {
          activity_type?: string
          business_profile_id?: string
          created_at?: string
          id?: string
          is_read?: boolean
          post_id?: string | null
          post_type?: string | null
          rating_value?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "business_activities_business_profile_id_fkey"
            columns: ["business_profile_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      business_posts: {
        Row: {
          business_id: string
          city_slug: string | null
          content: string
          created_at: string
          id: string
          image_url: string | null
          locality_slug: string | null
          mentioned_business_ids: string[] | null
          pincode: string | null
          product_ids: string[] | null
          state_slug: string | null
          updated_at: string
        }
        Insert: {
          business_id: string
          city_slug?: string | null
          content: string
          created_at?: string
          id?: string
          image_url?: string | null
          locality_slug?: string | null
          mentioned_business_ids?: string[] | null
          pincode?: string | null
          product_ids?: string[] | null
          state_slug?: string | null
          updated_at?: string
        }
        Update: {
          business_id?: string
          city_slug?: string | null
          content?: string
          created_at?: string
          id?: string
          image_url?: string | null
          locality_slug?: string | null
          mentioned_business_ids?: string[] | null
          pincode?: string | null
          product_ids?: string[] | null
          state_slug?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "business_posts_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      business_profiles: {
        Row: {
          about_bio: string | null
          address_line: string | null
          average_rating: number | null
          business_category: string | null
          business_hours: Json | null
          business_name: string
          business_slug: string | null
          city: string | null
          city_slug: string | null
          contact_email: string | null
          created_at: string
          custom_ads: Json | null
          custom_branding: Json | null
          delivery_info: string | null
          established_year: number | null
          facebook_url: string | null
          gallery: Json | null
          google_maps_url: string | null
          has_active_subscription: boolean
          id: string
          instagram_url: string | null
          latitude: number | null
          locality: string | null
          locality_slug: string | null
          logo_url: string | null
          longitude: number | null
          member_name: string | null
          phone: string | null
          pincode: string | null
          state: string | null
          state_slug: string | null
          status: string
          theme_color: string | null
          title: string | null
          today_visits: number
          total_likes: number
          total_subscriptions: number
          total_visits: number
          trial_end_date: string | null
          updated_at: string
          visits_30_days: number
          visits_7_days: number
          whatsapp_number: string | null
          yesterday_visits: number
        }
        Insert: {
          about_bio?: string | null
          address_line?: string | null
          average_rating?: number | null
          business_category?: string | null
          business_hours?: Json | null
          business_name: string
          business_slug?: string | null
          city?: string | null
          city_slug?: string | null
          contact_email?: string | null
          created_at?: string
          custom_ads?: Json | null
          custom_branding?: Json | null
          delivery_info?: string | null
          established_year?: number | null
          facebook_url?: string | null
          gallery?: Json | null
          google_maps_url?: string | null
          has_active_subscription?: boolean
          id: string
          instagram_url?: string | null
          latitude?: number | null
          locality?: string | null
          locality_slug?: string | null
          logo_url?: string | null
          longitude?: number | null
          member_name?: string | null
          phone?: string | null
          pincode?: string | null
          state?: string | null
          state_slug?: string | null
          status?: string
          theme_color?: string | null
          title?: string | null
          today_visits?: number
          total_likes?: number
          total_subscriptions?: number
          total_visits?: number
          trial_end_date?: string | null
          updated_at?: string
          visits_30_days?: number
          visits_7_days?: number
          whatsapp_number?: string | null
          yesterday_visits?: number
        }
        Update: {
          about_bio?: string | null
          address_line?: string | null
          average_rating?: number | null
          business_category?: string | null
          business_hours?: Json | null
          business_name?: string
          business_slug?: string | null
          city?: string | null
          city_slug?: string | null
          contact_email?: string | null
          created_at?: string
          custom_ads?: Json | null
          custom_branding?: Json | null
          delivery_info?: string | null
          established_year?: number | null
          facebook_url?: string | null
          gallery?: Json | null
          google_maps_url?: string | null
          has_active_subscription?: boolean
          id?: string
          instagram_url?: string | null
          latitude?: number | null
          locality?: string | null
          locality_slug?: string | null
          logo_url?: string | null
          longitude?: number | null
          member_name?: string | null
          phone?: string | null
          pincode?: string | null
          state?: string | null
          state_slug?: string | null
          status?: string
          theme_color?: string | null
          title?: string | null
          today_visits?: number
          total_likes?: number
          total_subscriptions?: number
          total_visits?: number
          trial_end_date?: string | null
          updated_at?: string
          visits_30_days?: number
          visits_7_days?: number
          whatsapp_number?: string | null
          yesterday_visits?: number
        }
        Relationships: []
      }
      card_visits: {
        Row: {
          business_profile_id: string
          id: string
          visit_date: string
          visited_at: string
          visitor_identifier: string
        }
        Insert: {
          business_profile_id: string
          id?: string
          visit_date?: string
          visited_at?: string
          visitor_identifier: string
        }
        Update: {
          business_profile_id?: string
          id?: string
          visit_date?: string
          visited_at?: string
          visitor_identifier?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_business_profile"
            columns: ["business_profile_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      custom_ad_targets: {
        Row: {
          ad_id: string
          created_at: string
          id: string
          is_global: boolean
          pincode: string
        }
        Insert: {
          ad_id: string
          created_at?: string
          id?: string
          is_global?: boolean
          pincode: string
        }
        Update: {
          ad_id?: string
          created_at?: string
          id?: string
          is_global?: boolean
          pincode?: string
        }
        Relationships: [
          {
            foreignKeyName: "custom_ad_targets_ad_id_fkey"
            columns: ["ad_id"]
            isOneToOne: false
            referencedRelation: "ad_targets_view"
            referencedColumns: ["ad_id"]
          },
          {
            foreignKeyName: "custom_ad_targets_ad_id_fkey"
            columns: ["ad_id"]
            isOneToOne: false
            referencedRelation: "custom_ads"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "custom_ad_targets_ad_id_fkey"
            columns: ["ad_id"]
            isOneToOne: false
            referencedRelation: "expired_ads_view"
            referencedColumns: ["ad_id"]
          },
        ]
      }
      custom_ads: {
        Row: {
          ad_image_url: string
          ad_link_url: string | null
          created_at: string
          expiry_date: string | null
          id: string
          is_active: boolean
        }
        Insert: {
          ad_image_url: string
          ad_link_url?: string | null
          created_at?: string
          expiry_date?: string | null
          id?: string
          is_active?: boolean
        }
        Update: {
          ad_image_url?: string
          ad_link_url?: string | null
          created_at?: string
          expiry_date?: string | null
          id?: string
          is_active?: boolean
        }
        Relationships: []
      }
      customer_posts: {
        Row: {
          city_slug: string | null
          content: string
          created_at: string
          customer_id: string
          id: string
          image_url: string | null
          locality_slug: string | null
          mentioned_business_ids: string[] | null
          pincode: string | null
          state_slug: string | null
          updated_at: string
        }
        Insert: {
          city_slug?: string | null
          content: string
          created_at?: string
          customer_id: string
          id?: string
          image_url?: string | null
          locality_slug?: string | null
          mentioned_business_ids?: string[] | null
          pincode?: string | null
          state_slug?: string | null
          updated_at?: string
        }
        Update: {
          city_slug?: string | null
          content?: string
          created_at?: string
          customer_id?: string
          id?: string
          image_url?: string | null
          locality_slug?: string | null
          mentioned_business_ids?: string[] | null
          pincode?: string | null
          state_slug?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "customer_posts_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_posts_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_profiles_public"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_profiles: {
        Row: {
          address: string | null
          avatar_url: string | null
          city: string | null
          city_slug: string | null
          created_at: string
          email: string | null
          id: string
          latitude: number | null
          locality: string | null
          locality_slug: string | null
          longitude: number | null
          name: string | null
          phone: string | null
          pincode: string | null
          state: string | null
          state_slug: string | null
          updated_at: string
        }
        Insert: {
          address?: string | null
          avatar_url?: string | null
          city?: string | null
          city_slug?: string | null
          created_at?: string
          email?: string | null
          id: string
          latitude?: number | null
          locality?: string | null
          locality_slug?: string | null
          longitude?: number | null
          name?: string | null
          phone?: string | null
          pincode?: string | null
          state?: string | null
          state_slug?: string | null
          updated_at?: string
        }
        Update: {
          address?: string | null
          avatar_url?: string | null
          city?: string | null
          city_slug?: string | null
          created_at?: string
          email?: string | null
          id?: string
          latitude?: number | null
          locality?: string | null
          locality_slug?: string | null
          longitude?: number | null
          name?: string | null
          phone?: string | null
          pincode?: string | null
          state?: string | null
          state_slug?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      likes: {
        Row: {
          business_profile_id: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          business_profile_id: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          business_profile_id?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "likes_business_profile_id_fkey"
            columns: ["business_profile_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      monthly_visit_metrics: {
        Row: {
          business_profile_id: string
          created_at: string
          id: string
          month: number
          unique_visits: number
          updated_at: string
          year: number
        }
        Insert: {
          business_profile_id: string
          created_at?: string
          id?: string
          month: number
          unique_visits?: number
          updated_at?: string
          year: number
        }
        Update: {
          business_profile_id?: string
          created_at?: string
          id?: string
          month?: number
          unique_visits?: number
          updated_at?: string
          year?: number
        }
        Relationships: [
          {
            foreignKeyName: "monthly_visit_metrics_business_profile_id_fkey"
            columns: ["business_profile_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_subscriptions: {
        Row: {
          business_profile_id: string
          cancellation_reason: string | null
          cancellation_requested_at: string | null
          cancelled_at: string | null
          created_at: string
          id: string
          last_payment_date: string | null
          last_payment_id: string | null
          last_payment_method: string | null
          last_webhook_timestamp: string | null
          original_plan_cycle: string | null
          original_plan_id: string | null
          plan_cycle: string
          plan_id: string
          razorpay_customer_id: string | null
          razorpay_subscription_id: string | null
          subscription_charge_time: string | null
          subscription_expiry_time: string | null
          subscription_paused_at: string | null
          subscription_start_date: string | null
          subscription_status: string
          updated_at: string
        }
        Insert: {
          business_profile_id: string
          cancellation_reason?: string | null
          cancellation_requested_at?: string | null
          cancelled_at?: string | null
          created_at?: string
          id?: string
          last_payment_date?: string | null
          last_payment_id?: string | null
          last_payment_method?: string | null
          last_webhook_timestamp?: string | null
          original_plan_cycle?: string | null
          original_plan_id?: string | null
          plan_cycle: string
          plan_id: string
          razorpay_customer_id?: string | null
          razorpay_subscription_id?: string | null
          subscription_charge_time?: string | null
          subscription_expiry_time?: string | null
          subscription_paused_at?: string | null
          subscription_start_date?: string | null
          subscription_status?: string
          updated_at?: string
        }
        Update: {
          business_profile_id?: string
          cancellation_reason?: string | null
          cancellation_requested_at?: string | null
          cancelled_at?: string | null
          created_at?: string
          id?: string
          last_payment_date?: string | null
          last_payment_id?: string | null
          last_payment_method?: string | null
          last_webhook_timestamp?: string | null
          original_plan_cycle?: string | null
          original_plan_id?: string | null
          plan_cycle?: string
          plan_id?: string
          razorpay_customer_id?: string | null
          razorpay_subscription_id?: string | null
          subscription_charge_time?: string | null
          subscription_expiry_time?: string | null
          subscription_paused_at?: string | null
          subscription_start_date?: string | null
          subscription_status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payment_subscriptions_business_profile_id_fkey"
            columns: ["business_profile_id"]
            isOneToOne: true
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pincodes: {
        Row: {
          CircleName: string
          city_slug: string | null
          created_at: string
          Delivery: string
          District: string
          DivisionName: string
          id: number
          Latitude: string
          locality_slug: string | null
          Longitude: string
          OfficeName: string
          OfficeType: string
          Pincode: string
          RegionName: string
          state_slug: string | null
          StateName: string
        }
        Insert: {
          CircleName: string
          city_slug?: string | null
          created_at?: string
          Delivery: string
          District: string
          DivisionName: string
          id?: number
          Latitude: string
          locality_slug?: string | null
          Longitude: string
          OfficeName: string
          OfficeType: string
          Pincode: string
          RegionName: string
          state_slug?: string | null
          StateName: string
        }
        Update: {
          CircleName?: string
          city_slug?: string | null
          created_at?: string
          Delivery?: string
          District?: string
          DivisionName?: string
          id?: number
          Latitude?: string
          locality_slug?: string | null
          Longitude?: string
          OfficeName?: string
          OfficeType?: string
          Pincode?: string
          RegionName?: string
          state_slug?: string | null
          StateName?: string
        }
        Relationships: []
      }
      processed_webhook_events: {
        Row: {
          created_at: string | null
          entity_id: string | null
          entity_type: string | null
          error_message: string | null
          event_id: string
          event_type: string | null
          notes: string | null
          payload: Json | null
          processed_at: string
          retry_count: number | null
          status: string | null
        }
        Insert: {
          created_at?: string | null
          entity_id?: string | null
          entity_type?: string | null
          error_message?: string | null
          event_id: string
          event_type?: string | null
          notes?: string | null
          payload?: Json | null
          processed_at?: string
          retry_count?: number | null
          status?: string | null
        }
        Update: {
          created_at?: string | null
          entity_id?: string | null
          entity_type?: string | null
          error_message?: string | null
          event_id?: string
          event_type?: string | null
          notes?: string | null
          payload?: Json | null
          processed_at?: string
          retry_count?: number | null
          status?: string | null
        }
        Relationships: []
      }
      product_variants: {
        Row: {
          base_price: number | null
          created_at: string
          discounted_price: number | null
          featured_image_index: number | null
          id: string
          images: string[] | null
          is_available: boolean
          product_id: string
          updated_at: string
          variant_name: string
          variant_values: Json
        }
        Insert: {
          base_price?: number | null
          created_at?: string
          discounted_price?: number | null
          featured_image_index?: number | null
          id?: string
          images?: string[] | null
          is_available?: boolean
          product_id: string
          updated_at?: string
          variant_name: string
          variant_values?: Json
        }
        Update: {
          base_price?: number | null
          created_at?: string
          discounted_price?: number | null
          featured_image_index?: number | null
          id?: string
          images?: string[] | null
          is_available?: boolean
          product_id?: string
          updated_at?: string
          variant_name?: string
          variant_values?: Json
        }
        Relationships: [
          {
            foreignKeyName: "product_variants_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products_services"
            referencedColumns: ["id"]
          },
        ]
      }
      products_services: {
        Row: {
          base_price: number | null
          business_id: string
          created_at: string
          description: string | null
          discounted_price: number | null
          featured_image_index: number | null
          id: string
          image_url: string | null
          images: string[] | null
          is_available: boolean
          name: string
          product_type: string | null
          slug: string | null
          updated_at: string
        }
        Insert: {
          base_price?: number | null
          business_id: string
          created_at?: string
          description?: string | null
          discounted_price?: number | null
          featured_image_index?: number | null
          id?: string
          image_url?: string | null
          images?: string[] | null
          is_available?: boolean
          name: string
          product_type?: string | null
          slug?: string | null
          updated_at?: string
        }
        Update: {
          base_price?: number | null
          business_id?: string
          created_at?: string
          description?: string | null
          discounted_price?: number | null
          featured_image_index?: number | null
          id?: string
          image_url?: string | null
          images?: string[] | null
          is_available?: boolean
          name?: string
          product_type?: string | null
          slug?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "products_services_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      ratings_reviews: {
        Row: {
          business_profile_id: string
          created_at: string
          id: string
          rating: number
          review_text: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          business_profile_id: string
          created_at?: string
          id?: string
          rating: number
          review_text?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          business_profile_id?: string
          created_at?: string
          id?: string
          rating?: number
          review_text?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ratings_reviews_business_profile_id_fkey"
            columns: ["business_profile_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      storage_cleanup_config: {
        Row: {
          batch_size: number | null
          created_at: string | null
          id: number
          is_running: boolean | null
          last_processed_offset: number | null
          last_run_at: string | null
          max_batches_per_run: number | null
          total_files_deleted: number | null
          total_users_processed: number | null
          updated_at: string | null
        }
        Insert: {
          batch_size?: number | null
          created_at?: string | null
          id?: number
          is_running?: boolean | null
          last_processed_offset?: number | null
          last_run_at?: string | null
          max_batches_per_run?: number | null
          total_files_deleted?: number | null
          total_users_processed?: number | null
          updated_at?: string | null
        }
        Update: {
          batch_size?: number | null
          created_at?: string | null
          id?: number
          is_running?: boolean | null
          last_processed_offset?: number | null
          last_run_at?: string | null
          max_batches_per_run?: number | null
          total_files_deleted?: number | null
          total_users_processed?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      storage_cleanup_progress: {
        Row: {
          bucket_name: string
          error_message: string | null
          files_deleted: number | null
          id: number
          processed_at: string | null
          run_id: string | null
          status: string
          user_id: string
        }
        Insert: {
          bucket_name: string
          error_message?: string | null
          files_deleted?: number | null
          id?: number
          processed_at?: string | null
          run_id?: string | null
          status: string
          user_id: string
        }
        Update: {
          bucket_name?: string
          error_message?: string | null
          files_deleted?: number | null
          id?: number
          processed_at?: string | null
          run_id?: string | null
          status?: string
          user_id?: string
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          business_profile_id: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          business_profile_id: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          business_profile_id?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_business_profile_id_fkey"
            columns: ["business_profile_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      system_alerts: {
        Row: {
          alert_type: string
          created_at: string | null
          entity_id: string | null
          id: string
          message: string
          metadata: Json | null
          resolved: boolean | null
          resolved_at: string | null
          severity: string
          subscription_id: string | null
          updated_at: string | null
        }
        Insert: {
          alert_type: string
          created_at?: string | null
          entity_id?: string | null
          id?: string
          message: string
          metadata?: Json | null
          resolved?: boolean | null
          resolved_at?: string | null
          severity: string
          subscription_id?: string | null
          updated_at?: string | null
        }
        Update: {
          alert_type?: string
          created_at?: string | null
          entity_id?: string | null
          id?: string
          message?: string
          metadata?: Json | null
          resolved?: boolean | null
          resolved_at?: string | null
          severity?: string
          subscription_id?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      webhook_error_logs: {
        Row: {
          created_at: string
          error_message: string
          event_id: string | null
          event_type: string
          id: string
          payload: Json
          retry_count: number
          status: string
          subscription_id: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          error_message: string
          event_id?: string | null
          event_type: string
          id?: string
          payload: Json
          retry_count?: number
          status: string
          subscription_id?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          error_message?: string
          event_id?: string | null
          event_type?: string
          id?: string
          payload?: Json
          retry_count?: number
          status?: string
          subscription_id?: string | null
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      ad_targets_view: {
        Row: {
          ad_created_at: string | null
          ad_id: string | null
          ad_image_url: string | null
          ad_link_url: string | null
          expiry_date: string | null
          expiry_status: string | null
          is_active: boolean | null
          is_global: boolean | null
          pincode: string | null
          target_created_at: string | null
          target_id: string | null
        }
        Relationships: []
      }
      customer_profiles_public: {
        Row: {
          avatar_url: string | null
          city: string | null
          city_slug: string | null
          created_at: string | null
          id: string | null
          locality: string | null
          locality_slug: string | null
          name: string | null
          state: string | null
          state_slug: string | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          city?: string | null
          city_slug?: string | null
          created_at?: string | null
          id?: string | null
          locality?: string | null
          locality_slug?: string | null
          name?: string | null
          state?: string | null
          state_slug?: string | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          city?: string | null
          city_slug?: string | null
          created_at?: string | null
          id?: string | null
          locality?: string | null
          locality_slug?: string | null
          name?: string | null
          state?: string | null
          state_slug?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      expired_ads_view: {
        Row: {
          ad_id: string | null
          ad_image_url: string | null
          ad_link_url: string | null
          created_at: string | null
          expired_for: unknown | null
          expiry_date: string | null
          is_active: boolean | null
          status: string | null
        }
        Insert: {
          ad_id?: string | null
          ad_image_url?: string | null
          ad_link_url?: string | null
          created_at?: string | null
          expired_for?: never
          expiry_date?: string | null
          is_active?: boolean | null
          status?: never
        }
        Update: {
          ad_id?: string | null
          ad_image_url?: string | null
          ad_link_url?: string | null
          created_at?: string | null
          expired_for?: never
          expiry_date?: string | null
          is_active?: boolean | null
          status?: never
        }
        Relationships: []
      }
      public_subscription_status: {
        Row: {
          business_profile_id: string | null
          plan_cycle: string | null
          plan_id: string | null
          subscription_status: string | null
        }
        Insert: {
          business_profile_id?: string | null
          plan_cycle?: string | null
          plan_id?: string | null
          subscription_status?: string | null
        }
        Update: {
          business_profile_id?: string | null
          plan_cycle?: string | null
          plan_id?: string | null
          subscription_status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payment_subscriptions_business_profile_id_fkey"
            columns: ["business_profile_id"]
            isOneToOne: true
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      unified_posts: {
        Row: {
          author_avatar: string | null
          author_id: string | null
          author_name: string | null
          business_plan: string | null
          business_slug: string | null
          city_slug: string | null
          content: string | null
          created_at: string | null
          id: string | null
          image_url: string | null
          locality_slug: string | null
          mentioned_business_ids: string[] | null
          phone: string | null
          pincode: string | null
          post_source: string | null
          product_ids: string[] | null
          state_slug: string | null
          updated_at: string | null
          whatsapp_number: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      add_custom_ad: {
        Args: {
          p_ad_image_url: string
          p_ad_link_url: string
          p_is_active: boolean
          p_is_global: boolean
          p_expiry_date?: string
          p_pincodes?: string[]
        }
        Returns: string
      }
      check_and_process_expired_trials: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      check_user_trial_status: {
        Args: { user_id: string }
        Returns: {
          business_profile_id: string
          was_expired: boolean
          action_taken: string
          new_status: string
        }[]
      }
      clean_old_card_visits: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      cleanup_expired_ads: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_orphaned_storage: {
        Args: Record<PropertyKey, never>
        Returns: {
          bucket_name: string
          user_id: string
          folder_path: string
          files_deleted: number
          status: string
        }[]
      }
      cleanup_orphaned_storage_batch: {
        Args: { batch_size?: number; max_batches?: number }
        Returns: {
          run_id: string
          batch_number: number
          users_processed: number
          files_deleted: number
          status: string
          message: string
        }[]
      }
      cleanup_orphaned_storage_scalable: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      cleanup_orphaned_storage_summary: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      cleanup_webhook_events: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_business_profile_atomic: {
        Args: { p_business_data: Json; p_subscription_data: Json }
        Returns: Json
      }
      delete_custom_ad: {
        Args: { p_ad_id: string }
        Returns: boolean
      }
      find_closest_locality: {
        Args: { user_lat: number; user_lng: number; max_distance_km?: number }
        Returns: {
          pincode: string
          office_name: string
          division_name: string
          state_name: string
          distance_km: number
        }[]
      }
      find_subscription_inconsistencies: {
        Args: Record<PropertyKey, never>
        Returns: {
          business_profile_id: string
          razorpay_subscription_id: string
          subscription_status: string
          has_active_subscription: boolean
          plan_id: string
          inconsistency_type: string
        }[]
      }
      fix_subscription_inconsistency: {
        Args: { target_business_profile_id: string }
        Returns: string
      }
      generate_slug: {
        Args: { input_text: string }
        Returns: string
      }
      generate_variant_combinations: {
        Args: { variant_types_values: Json }
        Returns: {
          combination: Json
          combination_name: string
        }[]
      }
      get_ad_for_pincode: {
        Args: { target_pincode: string }
        Returns: {
          id: string
          ad_image_url: string
          ad_link_url: string
          is_global: boolean
          expiry_date: string
        }[]
      }
      get_all_ads_for_pincode: {
        Args: { target_pincode: string }
        Returns: {
          id: string
          ad_image_url: string
          ad_link_url: string
          is_global: boolean
          expiry_date: string
          is_active: boolean
          created_at: string
          status: string
        }[]
      }
      get_available_product_variants: {
        Args: { product_uuid: string }
        Returns: {
          id: string
          variant_name: string
          variant_values: Json
          base_price: number
          discounted_price: number
          images: string[]
          featured_image_index: number
        }[]
      }
      get_available_years_for_monthly_metrics: {
        Args: { business_id: string }
        Returns: {
          year: number
        }[]
      }
      get_business_variant_stats: {
        Args: { business_uuid: string }
        Returns: {
          total_products: number
          products_with_variants: number
          total_variants: number
          available_variants: number
        }[]
      }
      get_columns: {
        Args: { table_name: string }
        Returns: {
          column_name: string
          data_type: string
        }[]
      }
      get_custom_ads_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          total_ads: number
          active_ads: number
          expired_ads: number
          global_ads: number
          pincode_targeted_ads: number
          unique_pincodes_targeted: number
          expiring_soon_ads: number
        }[]
      }
      get_daily_unique_visit_trend: {
        Args: { business_id: string; start_date: string; end_date: string }
        Returns: {
          date: string
          visits: number
        }[]
      }
      get_distinct_cities: {
        Args: { search_query: string; result_limit: number }
        Returns: {
          city: string
        }[]
      }
      get_hourly_unique_visit_trend: {
        Args: { business_id: string; target_date: string }
        Returns: {
          hour: number
          visits: number
        }[]
      }
      get_monthly_metrics: {
        Args: {
          target_user_id: string
          target_year?: number
          target_month?: number
        }
        Returns: {
          profile_id: string
          metric_year: number
          metric_month: number
          unique_visits: number
          calculated_at: string
        }[]
      }
      get_monthly_unique_visit_trend: {
        Args: {
          business_id: string
          start_year: number
          start_month: number
          end_year: number
          end_month: number
        }
        Returns: {
          year: number
          month: number
          visits: number
        }[]
      }
      get_monthly_unique_visits: {
        Args: { business_id: string; target_year: number; target_month: number }
        Returns: number
      }
      get_most_targeted_pincodes: {
        Args: { limit_count?: number }
        Returns: {
          pincode: string
          ad_count: number
        }[]
      }
      get_product_with_variants: {
        Args: { product_uuid: string }
        Returns: {
          product_id: string
          product_name: string
          product_description: string
          product_base_price: number
          product_discounted_price: number
          product_is_available: boolean
          product_images: string[]
          product_featured_image_index: number
          variant_count: number
          variants: Json
        }[]
      }
      get_scalable_system_health: {
        Args: Record<PropertyKey, never>
        Returns: {
          function_name: string
          is_healthy: boolean
          last_run_hours_ago: number
          batch_size: number
          is_running: boolean
          health_status: string
        }[]
      }
      get_subscription_health_metrics: {
        Args: Record<PropertyKey, never>
        Returns: {
          total_subscriptions: number
          active_subscriptions: number
          trial_subscriptions: number
          cancelled_subscriptions: number
          expired_subscriptions: number
          inconsistent_subscriptions: number
          health_score: number
        }[]
      }
      get_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
        }[]
      }
      get_total_unique_visits: {
        Args: { business_id: string }
        Returns: number
      }
      get_webhook_error_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          total_events: number
          successful_events: number
          failed_events: number
          retrying_events: number
          success_rate: number
          last_24h_events: number
          last_24h_failures: number
        }[]
      }
      handle_payment_method_updated: {
        Args: {
          user_id: string
          subscription_id: string
          payment_id: string
          event_id: string
        }
        Returns: undefined
      }
      handle_refund_processed: {
        Args: { user_id: string; payment_id: string; event_id: string }
        Returns: undefined
      }
      handle_subscription_cancelled: {
        Args: { user_id: string; subscription_id: string; event_id: string }
        Returns: undefined
      }
      handle_subscription_charged: {
        Args: {
          user_id: string
          subscription_id: string
          customer_id: string
          plan_id: string
          end_date: string
          start_date: string
          event_id: string
        }
        Returns: undefined
      }
      handle_webhook_event: {
        Args: {
          p_event_id: string
          p_event_type: string
          p_razorpay_subscription_id: string
          p_business_id: string
          p_update_payload: Json
        }
        Returns: boolean
      }
      increment: {
        Args: { row_id: string; table_name: string; column_name: string }
        Returns: number
      }
      is_variant_combination_unique: {
        Args: {
          product_uuid: string
          variant_vals: Json
          exclude_variant_id?: string
        }
        Returns: boolean
      }
      jsonb_object_keys_count: {
        Args: { obj: Json }
        Returns: number
      }
      limit_products_for_free_plan: {
        Args: { p_business_id: string }
        Returns: number
      }
      limit_products_for_plan: {
        Args: { p_business_id: string; p_plan_id: string }
        Returns: number
      }
      manual_subscription_override: {
        Args: {
          p_business_profile_id: string
          p_subscription_id: string
          p_new_status: string
          p_new_plan_id: string
          p_override_reason: string
          p_admin_user_id: string
        }
        Returns: undefined
      }
      manual_update_visit_counts: {
        Args: { profile_id: string }
        Returns: undefined
      }
      populate_historical_monthly_data: {
        Args: { business_id: string; start_year: number; start_month: number }
        Returns: number
      }
      reset_all_scalable_offsets: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      reset_all_visit_counts: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      reset_daily_visit_counts: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      test_existing_triggers: {
        Args: Record<PropertyKey, never>
        Returns: {
          trigger_name: string
          table_name: string
          function_name: string
          status: string
        }[]
      }
      test_webhook_event: {
        Args: { p_business_id: string; p_update_payload: Json }
        Returns: boolean
      }
      track_notification_action: {
        Args: {
          p_user_id: string
          p_notification_type: string
          p_sent_at: string
          p_action_type: string
        }
        Returns: boolean
      }
      update_all_monthly_visit_metrics: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      update_custom_ad: {
        Args: {
          p_ad_id: string
          p_ad_image_url?: string
          p_ad_link_url?: string
          p_is_active?: boolean
          p_expiry_date?: string
          p_is_global?: boolean
          p_pincodes?: string[]
        }
        Returns: boolean
      }
      update_monthly_metrics_active_users: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      update_notification_opened: {
        Args: {
          p_user_id: string
          p_notification_type: string
          p_sent_at: string
        }
        Returns: boolean
      }
      update_period_visit_counts: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_subscription_atomic: {
        Args: {
          p_subscription_id: string
          p_new_status: string
          p_business_profile_id: string
          p_has_active_subscription: boolean
          p_additional_data?: Json
          p_webhook_timestamp?: string
        }
        Returns: Json
      }
      validate_and_fix_subscription_state: {
        Args: { p_business_profile_id: string }
        Returns: Json
      }
      verify_expired_trials_batch: {
        Args: { batch_size?: number }
        Returns: string
      }
      verify_realtime_metrics: {
        Args: { business_id: string }
        Returns: {
          metric_type: string
          current_value: number
          last_updated: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const

export type BusinessProfilesTable = Database['public']['Tables']['business_profiles']
export type BusinessProfile = BusinessProfilesTable['Row']
export type BusinessProfileInsert = BusinessProfilesTable['Insert']
export type BusinessProfileUpdate = BusinessProfilesTable['Update']

export type BusinessDiscoveryData = BusinessProfile;
export type BusinessCardData = BusinessProfile;
