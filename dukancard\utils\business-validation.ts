/**
 * Utility functions for validating business profile completeness
 */
import { type BusinessProfilesRow } from "@/types/supabase";

type BusinessProfiles = BusinessProfilesRow;

// Required fields for accessing business dashboard
export const REQUIRED_BUSINESS_FIELDS = [
  "member_name",
  "title",
  "business_name",
  "business_category",
  "contact_email",
  "phone",
  "address_line",
  "pincode",
  "city",
  "state",
  "locality",
] as const;

export type RequiredBusinessField = (typeof REQUIRED_BUSINESS_FIELDS)[number];

/**
 * Check if all required business fields are complete
 * @param profile - Business profile object
 * @returns Object with validation result and missing fields
 */
export function validateRequiredBusinessFields(
  profile: Partial<BusinessProfiles> | null
) {
  if (!profile) {
    return {
      isComplete: false,
      missingFields: [...REQUIRED_BUSINESS_FIELDS],
      missingFieldLabels: [
        "Your name",
        "Your title",
        "Business name",
        "Business category",
        "Contact email",
        "Primary phone",
        "Address line",
        "Pincode",
        "City",
        "State",
        "Locality/area",
      ],
    };
  }

  const missingFields: RequiredBusinessField[] = [];
  const missingFieldLabels: string[] = [];

  const fieldLabelMap: Record<RequiredBusinessField, string> = {
    member_name: "Your name",
    title: "Your title",
    business_name: "Business name",
    business_category: "Business category",
    contact_email: "Contact email",
    phone: "Primary phone",
    address_line: "Address line",
    pincode: "Pincode",
    city: "City",
    state: "State",
    locality: "Locality/area",
  };

  REQUIRED_BUSINESS_FIELDS.forEach((field) => {
    const value = profile[field as keyof BusinessProfiles];
    if (!value || String(value).trim() === "") {
      missingFields.push(field);
      missingFieldLabels.push(fieldLabelMap[field]);
    }
  });

  return {
    isComplete: missingFields.length === 0,
    missingFields,
    missingFieldLabels,
  };
}

/**
 * Generate a user-friendly message for missing fields
 * @param missingFieldLabels - Array of missing field labels
 * @returns Formatted message string
 */
export function generateMissingFieldsMessage(
  missingFieldLabels: string[]
): string {
  if (missingFieldLabels.length === 0) return "";

  if (missingFieldLabels.length === 1) {
    return `Please complete your ${missingFieldLabels[0].toLowerCase()} to access the dashboard.`;
  }

  if (missingFieldLabels.length === 2) {
    return `Please complete your ${missingFieldLabels[0].toLowerCase()} and ${missingFieldLabels[1].toLowerCase()} to access the dashboard.`;
  }

  const lastField = missingFieldLabels[missingFieldLabels.length - 1];
  const otherFields = missingFieldLabels.slice(0, -1);

  return `Please complete your ${otherFields
    .map((f) => f.toLowerCase())
    .join(", ")}, and ${lastField.toLowerCase()} to access the dashboard.`;
}
