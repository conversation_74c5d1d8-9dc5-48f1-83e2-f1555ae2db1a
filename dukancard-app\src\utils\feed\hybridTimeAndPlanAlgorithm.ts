import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';
// import { PLAN_PRIORITY } from './planPrioritizer';
import { applyDiversityRules } from './diversityEngine';

/**
 * Hybrid Time + Plan Algorithm
 * 
 * Strategy per page:
 * 1. Take 1 latest post from each plan tier (enterprise, pro, growth, basic, free) = 5 posts
 * 2. Fill remaining 5 slots with latest posts regardless of plan
 * 3. Apply diversity rules to prevent consecutive same-author posts
 * 4. Customer posts maintain chronological order with diversity
 * 
 * Benefits:
 * - Each plan gets guaranteed representation
 * - Latest content gets priority
 * - No posts are lost
 * - Database pagination compatible
 */

export interface HybridAlgorithmOptions {
  targetCount?: number;
  guaranteedSlotsPerPlan?: number;
  enableDiversity?: boolean;
  maintainCustomerChronology?: boolean;
}

/**
 * Main hybrid algorithm implementation
 */
export function processHybridTimeAndPlan(
  posts: UnifiedPost[],
  page: number = 1,
  options: HybridAlgorithmOptions = {}
): UnifiedPost[] {
  const {
    targetCount = 10,
    guaranteedSlotsPerPlan = 1,
    enableDiversity = true,
    maintainCustomerChronology = true
  } = options;

  if (posts.length === 0) return [];

  // Separate customer and business posts
  const customerPosts = posts.filter(post => post.post_source === 'customer');
  const businessPosts = posts.filter(post => post.post_source === 'business');

  // Process customer posts (maintain chronological order with optional diversity)
  const processedCustomerPosts = processCustomerPostsForHybrid(customerPosts, enableDiversity);

  // Process business posts using hybrid algorithm
  const processedBusinessPosts = processBusinessPostsHybrid(
    businessPosts, 
    page, 
    targetCount, 
    guaranteedSlotsPerPlan
  );

  // Merge and apply final processing
  const mergedPosts = mergeHybridPosts(
    processedCustomerPosts,
    processedBusinessPosts,
    targetCount,
    maintainCustomerChronology
  );

  // Apply diversity rules if enabled
  return enableDiversity ? applyDiversityRules(mergedPosts) : mergedPosts;
}

/**
 * Process customer posts - maintain chronological order
 */
export function processCustomerPostsForHybrid(
  customerPosts: UnifiedPost[],
  enableDiversity: boolean
): UnifiedPost[] {
  if (customerPosts.length === 0) return [];

  // Sort chronologically (latest first)
  const sortedPosts = customerPosts.sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  if (!enableDiversity) return sortedPosts;

  // Apply diversity while maintaining chronological preference
  return applyDiversityRules(sortedPosts, { prioritizeRecency: true });
}

/**
 * Process business posts using hybrid time + plan algorithm
 */
export function processBusinessPostsHybrid(
  businessPosts: UnifiedPost[],
  page: number,
  targetCount: number,
  guaranteedSlotsPerPlan: number
): UnifiedPost[] {
  if (businessPosts.length === 0) return [];

  // Sort all business posts chronologically (latest first)
  const sortedBusinessPosts = businessPosts.sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  // Group posts by plan
  const postsByPlan = groupPostsByPlan(sortedBusinessPosts);

  // Calculate pagination offset for this page
  const pageOffset = (page - 1) * targetCount;

  // Step 1: Get guaranteed slots (1 latest post per plan)
  const guaranteedPosts = getGuaranteedPostsPerPlan(
    postsByPlan, 
    guaranteedSlotsPerPlan,
    pageOffset
  );

  // Step 2: Get remaining posts (latest posts regardless of plan)
  const remainingSlots = Math.max(0, targetCount - guaranteedPosts.length);
  const remainingPosts = getRemainingLatestPosts(
    sortedBusinessPosts,
    guaranteedPosts,
    remainingSlots,
    pageOffset
  );

  // Combine and sort by timestamp
  const combinedPosts = [...guaranteedPosts, ...remainingPosts]
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  return combinedPosts;
}

/**
 * Group business posts by plan tier
 */
export function groupPostsByPlan(businessPosts: UnifiedPost[]): Map<string, UnifiedPost[]> {
  const grouped = new Map<string, UnifiedPost[]>();
  
  businessPosts.forEach(post => {
    const plan = post.business_plan || 'free';
    if (!grouped.has(plan)) {
      grouped.set(plan, []);
    }
    grouped.get(plan)!.push(post);
  });

  return grouped;
}

/**
 * Get guaranteed posts per plan (1 latest from each plan for current page)
 */
export function getGuaranteedPostsPerPlan(
  postsByPlan: Map<string, UnifiedPost[]>,
  slotsPerPlan: number,
  pageOffset: number
): UnifiedPost[] {
  const guaranteedPosts: UnifiedPost[] = [];
  const planOrder = ['enterprise', 'pro', 'growth', 'basic', 'free'];

  planOrder.forEach(plan => {
    const planPosts = postsByPlan.get(plan) || [];

    // For each plan, take the next available post based on page
    // Page 1: take post 0, Page 2: take post 1, etc.
    const postIndex = Math.floor(pageOffset / 10); // Assuming 10 posts per page
    if (planPosts[postIndex]) {
      guaranteedPosts.push(planPosts[postIndex]);
    }
  });

  return guaranteedPosts;
}

/**
 * Get remaining latest posts regardless of plan
 * These fill the remaining slots after guaranteed plan slots
 */
export function getRemainingLatestPosts(
  allBusinessPosts: UnifiedPost[],
  excludePosts: UnifiedPost[],
  remainingSlots: number,
  _pageOffset: number
): UnifiedPost[] {
  if (remainingSlots <= 0) return [];

  // Create set of excluded post IDs for fast lookup
  const excludeIds = new Set(excludePosts.map(post => post.id));

  // Filter out already selected posts
  const availablePosts = allBusinessPosts.filter(post => !excludeIds.has(post.id));

  // For remaining slots, just take the next latest posts
  // No complex pagination offset needed since we're working with filtered data
  return availablePosts.slice(0, remainingSlots);
}

/**
 * Merge customer and business posts with EQUAL TREATMENT
 * No priority given to either customer or business posts
 * Only business posts get plan prioritization among themselves
 */
export function mergeHybridPosts(
  customerPosts: UnifiedPost[],
  businessPosts: UnifiedPost[],
  targetCount: number,
  _maintainCustomerChronology: boolean
): UnifiedPost[] {
  if (customerPosts.length === 0) return businessPosts.slice(0, targetCount);
  if (businessPosts.length === 0) return customerPosts.slice(0, targetCount);

  // EQUAL TREATMENT: Merge all posts by timestamp only
  // Customer posts compete equally with business posts based on time
  // Plan prioritization only applies within business posts, not between customer vs business
  const merged = [...customerPosts, ...businessPosts]
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  return merged.slice(0, targetCount);
}

/**
 * Get algorithm statistics for debugging
 */
export function getHybridAlgorithmStats(
  posts: UnifiedPost[],
  processedPosts: UnifiedPost[]
): {
  originalCount: number;
  processedCount: number;
  planDistribution: Record<string, number>;
  guaranteedSlots: number;
  remainingSlots: number;
  customerPosts: number;
  businessPosts: number;
} {
  const planDistribution: Record<string, number> = {};
  let guaranteedSlots = 0;
  let remainingSlots = 0;
  
  const businessPosts = processedPosts.filter(p => p.post_source === 'business');
  const customerPosts = processedPosts.filter(p => p.post_source === 'customer');
  
  // Count plan distribution
  businessPosts.forEach(post => {
    const plan = post.business_plan || 'free';
    planDistribution[plan] = (planDistribution[plan] || 0) + 1;
  });

  // Estimate guaranteed vs remaining slots
  const planCount = Object.keys(planDistribution).length;
  guaranteedSlots = Math.min(planCount, 5); // Max 1 per plan
  remainingSlots = businessPosts.length - guaranteedSlots;

  return {
    originalCount: posts.length,
    processedCount: processedPosts.length,
    planDistribution,
    guaranteedSlots,
    remainingSlots,
    customerPosts: customerPosts.length,
    businessPosts: businessPosts.length
  };
}

/**
 * Validate that no posts are lost in processing
 */
export function validateNoPostLoss(
  originalPosts: UnifiedPost[],
  processedPosts: UnifiedPost[],
  expectedCount: number
): {
  isValid: boolean;
  issues: string[];
  lostPosts: string[];
} {
  const issues: string[] = [];
  const lostPosts: string[] = [];
  
  const originalIds = new Set(originalPosts.map(p => p.id));
  const processedIds = new Set(processedPosts.map(p => p.id));
  
  // Check for lost posts
  originalIds.forEach(id => {
    if (!processedIds.has(id)) {
      lostPosts.push(id);
    }
  });
  
  if (lostPosts.length > 0) {
    issues.push(`Lost ${lostPosts.length} posts during processing`);
  }
  
  if (processedPosts.length > expectedCount) {
    issues.push(`Returned ${processedPosts.length} posts, expected ${expectedCount}`);
  }
  
  return {
    isValid: issues.length === 0 && lostPosts.length === 0,
    issues,
    lostPosts
  };
}
