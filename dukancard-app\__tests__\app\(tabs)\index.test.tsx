import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import HomeScreen from '@/app/(tabs)/index';
import { useAuth } from '@/src/contexts/AuthContext';
import { getUserDisplayName } from '@/lib/supabase';
import { useTheme } from '@/src/hooks/useTheme';
import { Alert } from 'react-native';

// Mock external modules and components
jest.mock('expo-image', () => ({
  Image: 'Image',
}));
jest.mock('@/src/components/HelloWave', () => ({
  HelloWave: 'HelloWave',
}));
jest.mock('@/src/components/ParallaxScrollView', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));
jest.mock('@/src/components/ThemedText', () => {
  const ReactNative = jest.requireActual('react-native');
  return {
    ThemedText: ({ children, type, style }: any) => <ReactNative.Text style={style}>{children}</ReactNative.Text>,
  };
});
jest.mock('@/src/components/ThemedView', () => {
  const ReactNative = jest.requireActual('react-native');
  return {
    ThemedView: ({ children, style }: any) => <ReactNative.View style={style}>{children}</ReactNative.View>,
  };
});
jest.mock('@/src/contexts/AuthContext');
jest.mock('@/lib/supabase', () => ({
  getUserDisplayName: jest.fn((user) => user?.email || 'Guest'),
}));
jest.mock('@/src/hooks/useTheme');
jest.mock('@/lib/theme/colors', () => ({
  responsiveFontSize: jest.fn((size) => size),
}));
jest.mock('react-native', () => {
  const ReactNative = jest.requireActual('react-native');
  return {
    ...ReactNative,
    Alert: {
      alert: jest.fn(),
    },
  };
});

describe('HomeScreen', () => {
  const mockUser = { email: '<EMAIL>' };
  const mockSignOut = jest.fn();
  const mockTheme = {
    colors: {
      primary: '#D4AF37',
      secondary: '#A1CEDC',
      error: '#FF0000',
      errorForeground: '#FFFFFF',
    },
    spacing: { xs: 4, md: 16, sm: 8, xl: 24 },
    borderRadius: { md: 8 },
  };

  beforeEach(() => {
    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
      signOut: mockSignOut,
    });
    (useTheme as jest.Mock).mockReturnValue(mockTheme);
    (Alert.alert as jest.Mock).mockClear();
    mockSignOut.mockResolvedValue({ error: null });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders welcome message with user email', () => {
    const { getByText } = render(<HomeScreen />);
    expect(getByText('Welcome, <EMAIL>!')).toBeDefined();
  });

  it('renders sign out button', () => {
    const { getByText } = render(<HomeScreen />);
    expect(getByText('Sign Out')).toBeDefined();
  });

  it('calls Alert.alert on sign out button press', () => {
    const { getByText } = render(<HomeScreen />);
    fireEvent.press(getByText('Sign Out'));
    expect(Alert.alert).toHaveBeenCalledWith(
      'Sign Out',
      'Are you sure you want to sign out?',
      expect.any(Array)
    );
  });

  it('calls signOut when confirm sign out is pressed', async () => {
    const { getByText } = render(<HomeScreen />);
    fireEvent.press(getByText('Sign Out'));

    // Simulate pressing the 'Sign Out' button in the alert
    const signOutOption = (Alert.alert as jest.Mock).mock.calls[0][2].find(
      (option: any) => option.text === 'Sign Out'
    );
    await act(async () => {
      signOutOption.onPress();
    });

    expect(mockSignOut).toHaveBeenCalledTimes(1);
  });

  it('shows error alert if signOut fails', async () => {
    mockSignOut.mockResolvedValue({ error: new Error('Sign out failed') });

    const { getByText } = render(<HomeScreen />);
    fireEvent.press(getByText('Sign Out'));

    const signOutOption = (Alert.alert as jest.Mock).mock.calls[0][2].find(
      (option: any) => option.text === 'Sign Out'
    );
    await act(async () => {
      signOutOption.onPress();
    });

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to sign out. Please try again.');
    });
  });
});
