import { addProductVariant, addMultipleVariants, generateVariantCombinations } from '@/app/(dashboard)/dashboard/business/products/actions/addVariant';
import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';
import { handleVariantImageUpload } from '@/app/(dashboard)/dashboard/business/products/actions/imageHandlers';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));
jest.mock('@/app/(dashboard)/dashboard/business/products/actions/imageHandlers', () => ({
  handleVariantImageUpload: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe('Product Variant Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('addProductVariant', () => {
    it('should return an error if the user is not authenticated', async () => {
      // Arrange
      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
        },
      });
      const formData = new FormData();

      // Act
      const result = await addProductVariant(formData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('User not authenticated.');
    });

    it('should add a variant successfully', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { id: '123e4567-e89b-12d3-a456-************', business_id: 'user-123' };
      const mockVariant = { id: 'var-123', product_id: '123e4567-e89b-12d3-a456-************' };
      const mockInsert = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockSingle = jest.fn().mockResolvedValue({ data: mockVariant, error: null });

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn((table: string) => {
          if (table === 'products_services') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
            };
          }
          return {
            insert: mockInsert,
            select: mockSelect,
            single: mockSingle,
            eq: jest.fn().mockReturnThis(),
          };
        }),
      });

      const formData = new FormData();
      formData.append('product_id', '123e4567-e89b-12d3-a456-************'); // Valid UUID
      formData.append('variant_name', 'Color');
      formData.append('variant_values', JSON.stringify({ color: 'Red' }));
      formData.append('base_price', '100');

      // Act
      const result = await addProductVariant(formData);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.id).toBe('var-123');
      expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
    });

    it('should add a variant with images successfully', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { id: '123e4567-e89b-12d3-a456-************', business_id: 'user-123' };
      const mockVariant = { id: 'var-123', product_id: '123e4567-e89b-12d3-a456-************', images: [], featured_image_index: 0 };
      const mockUploadedUrls = ['url1.jpg', 'url2.jpg'];

      const mockInsert = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockSingle = jest.fn().mockResolvedValue({ data: mockVariant, error: null });
      const mockUpdate = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn((table: string) => {
          if (table === 'products_services') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
            };
          }
          return {
            insert: mockInsert,
            select: mockSelect,
            single: mockSingle,
            update: mockUpdate,
            eq: mockEq,
          };
        }),
      });

      (handleVariantImageUpload as jest.Mock).mockResolvedValue({
        urls: mockUploadedUrls,
        error: null,
      });

      const formData = new FormData();
      formData.append('product_id', '123e4567-e89b-12d3-a456-************');
      formData.append('variant_name', 'Color');
      formData.append('variant_values', JSON.stringify({ color: 'Red' }));
      formData.append('base_price', '100');
      formData.append('images[0]', new File(['dummy'], 'image1.jpg', { type: 'image/jpeg' }));
      formData.append('featured_image_index', '0');

      // Act
      const result = await addProductVariant(formData);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.id).toBe('var-123');
      expect(handleVariantImageUpload).toHaveBeenCalledWith('user-123', '123e4567-e89b-12d3-a456-************', 'var-123', expect.any(Array));
      expect(mockUpdate).toHaveBeenCalledWith({
        images: mockUploadedUrls,
        featured_image_index: 0,
      });
      expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
      expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products/123e4567-e89b-12d3-a456-************');
    });

    it('should return an error if image upload fails and delete the variant', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockProduct = { id: '123e4567-e89b-12d3-a456-************', business_id: 'user-123' };
      const mockVariant = { id: 'var-123', product_id: '123e4567-e89b-12d3-a456-************' };

      const mockInsert = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockSingle = jest.fn().mockResolvedValue({ data: mockVariant, error: null });
      const mockDelete = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn((table: string) => {
          if (table === 'products_services') {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
            };
          }
          return {
            insert: mockInsert,
            select: mockSelect,
            single: mockSingle,
            delete: mockDelete,
            eq: mockEq,
          };
        }),
      });

      (handleVariantImageUpload as jest.Mock).mockResolvedValue({
        urls: [],
        error: 'Upload failed',
      });

      const formData = new FormData();
      formData.append('product_id', '123e4567-e89b-12d3-a456-************');
      formData.append('variant_name', 'Color');
      formData.append('variant_values', JSON.stringify({ color: 'Red' }));
      formData.append('base_price', '100');
      formData.append('images[0]', new File(['dummy'], 'image1.jpg', { type: 'image/jpeg' }));

      // Act
      const result = await addProductVariant(formData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Upload failed');
      expect(handleVariantImageUpload).toHaveBeenCalled();
      expect(mockDelete).toHaveBeenCalledWith();
      expect(mockEq).toHaveBeenCalledWith('id', 'var-123');
      expect(revalidatePath).not.toHaveBeenCalled();
    });
  });

  describe('addMultipleVariants', () => {
    it('should add multiple variants successfully', async () => {
        // Arrange
        const mockUser = { id: 'user-123' };
        const mockProduct = { id: 'prod-123', business_id: 'user-123' };
        const mockVariants = [{ id: 'var-1' }, { id: 'var-2' }];
        const mockInsert = jest.fn().mockReturnThis();
        const mockSelect = jest.fn().mockResolvedValue({ data: mockVariants, error: null });
  
        mockSupabase.mockReturnValue({
          auth: {
            getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
          },
          from: jest.fn((table: string) => {
            if (table === 'products_services') {
              return {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
              };
            }
            return {
              insert: mockInsert,
              select: mockSelect,
            };
          }),
        });
  
        const variantsToAdd = [
          { variant_name: 'Red', variant_values: { color: 'Red' } },
          { variant_name: 'Blue', variant_values: { color: 'Blue' } },
        ];
  
        // Act
        const result = await addMultipleVariants('prod-123', variantsToAdd);
  
        // Assert
        expect(result.success).toBe(true);
        expect(result.data).toHaveLength(2);
        expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
      });
  });

  describe('generateVariantCombinations', () => {
    it('should generate combinations correctly', async () => {
        // Arrange
        const variantTypes = {
          Color: ['Red', 'Blue'],
          Size: ['S', 'M'],
        };
  
        // Act
        const result = await generateVariantCombinations('prod-123', variantTypes);
  
        // Assert
        expect(result.success).toBe(true);
        expect(result.combinations).toHaveLength(4);
        expect(result.combinations).toContainEqual({
          variant_name: 'Red S',
          variant_values: { Color: 'Red', Size: 'S' },
        });
      });
  });
});