import { Database, Tables } from "./supabase";
import { BusinessProfileSelect } from "./discovery";

export type BusinessProfile = Tables<"business_profiles">;
export type BusinessProfileInsert = Database['public']['Tables']['business_profiles']['Insert'];
export type BusinessProfileUpdate = Database['public']['Tables']['business_profiles']['Update'];

export type BusinessCardData = BusinessProfile & {
  // Add any additional properties that are not in the database
};

export type BusinessDiscoveryData = {
  success: boolean;
  error?: string;
  data?: BusinessProfileSelect;
};


export type BusinessGalleryImage = {
  id?: string;
  url: string;
  alt?: string;
};