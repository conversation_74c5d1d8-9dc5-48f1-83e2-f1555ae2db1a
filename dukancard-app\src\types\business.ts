import { Database } from "./supabase";

export type BusinessProfile = Database['public']['Tables']['business_profiles']['Row'];
export type BusinessProfileInsert = Database['public']['Tables']['business_profiles']['Insert'];
export type BusinessProfileUpdate = Database['public']['Tables']['business_profiles']['Update'];

export type BusinessDiscoveryData = {
  success: boolean;
  error?: string;
  data?: BusinessProfile;
};
export type BusinessCardData = BusinessProfile;

export type BusinessGalleryImage = {
  url: string;
  alt?: string;
};