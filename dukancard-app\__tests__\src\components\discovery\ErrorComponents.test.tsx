import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import {
  ErrorSection,
  EmptyState,
  NetworkError,
  LocationError,
} from '@/src/components/discovery/ErrorComponents';
import { useTheme } from '@/src/hooks/useTheme';

// Mock the useTheme hook
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      error: '#ff0000',
      primaryForeground: '#ffffff',
      textSecondary: '#888888',
      textPrimary: '#000000',
      primary: '#C29D5B',
      warning: '#ffaa00',
    },
  }),
}));

describe('ErrorSection', () => {
  it('renders nothing when error is null', () => {
    const { queryByText } = render(<ErrorSection error={null} />);
    expect(queryByText('Error Occurred')).toBeNull();
  });

  it('renders the error message and title when an error is provided', () => {
    const { getByText } = render(<ErrorSection error="Something went wrong" />);
    expect(getByText('Error Occurred')).toBeTruthy();
    expect(getByText('Something went wrong')).toBeTruthy();
  });

  it('calls onRetry when the retry button is pressed', () => {
    const onRetry = jest.fn();
    const { getByText } = render(
      <ErrorSection error="Network error" onRetry={onRetry} />
    );
    fireEvent.press(getByText('Try Again'));
    expect(onRetry).toHaveBeenCalled();
  });
});

describe('EmptyState', () => {
  it('renders the title and description', () => {
    const { getByText } = render(
      <EmptyState title="No Results" description="Try a different search" />
    );
    expect(getByText('No Results')).toBeTruthy();
    expect(getByText('Try a different search')).toBeTruthy();
  });

  it('calls onAction when the action button is pressed', () => {
    const onAction = jest.fn();
    const { getByText } = render(
      <EmptyState
        title="No Results"
        description="..."
        actionText="Clear Search"
        onAction={onAction}
      />
    );
    fireEvent.press(getByText('Clear Search'));
    expect(onAction).toHaveBeenCalled();
  });
});

describe('NetworkError', () => {
  it('renders the network error message', () => {
    const { getByText } = render(<NetworkError onRetry={() => {}} />);
    expect(getByText('Connection Problem')).toBeTruthy();
    expect(
      getByText('Please check your internet connection and try again.')
    ).toBeTruthy();
  });

  it('calls onRetry when the retry button is pressed', () => {
    const onRetry = jest.fn();
    const { getByText } = render(<NetworkError onRetry={onRetry} />);
    fireEvent.press(getByText('Retry'));
    expect(onRetry).toHaveBeenCalled();
  });
});

describe('LocationError', () => {
  it('renders the location error message', () => {
    const { getByText } = render(
      <LocationError error="Could not get location" />
    );
    expect(getByText('Location Issue')).toBeTruthy();
    expect(getByText('Could not get location')).toBeTruthy();
  });

  it('calls onRetry when the retry button is pressed', () => {
    const onRetry = jest.fn();
    const { getByText } = render(
      <LocationError error="..." onRetry={onRetry} />
    );
    fireEvent.press(getByText('Try Again'));
    expect(onRetry).toHaveBeenCalled();
  });

  it('calls onManualLocation when the manual location button is pressed', () => {
    const onManualLocation = jest.fn();
    const { getByText } = render(
      <LocationError error="..." onManualLocation={onManualLocation} />
    );
    fireEvent.press(getByText('Enter Manually'));
    expect(onManualLocation).toHaveBeenCalled();
  });
});
