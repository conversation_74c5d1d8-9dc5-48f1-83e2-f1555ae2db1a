"use server";

import { createClient } from "@/utils/supabase/server";
import { Tables } from "@/types/supabase";

type ProductsServices = Tables<'products_services'>;

type FetchedProduct = Pick<
  ProductsServices,
  "id" | "name" | "base_price" | "discounted_price" | "image_url" | "slug"
>;

/**
 * Fetch products by their IDs using admin client to bypass RLS
 * This is used for displaying linked products in feed posts
 */
export async function fetchProductsByIds(productIds: string[]): Promise<{
  success: boolean;
  data?: FetchedProduct[];
  error?: string;
}> {
  if (!productIds || productIds.length === 0) {
    return {
      success: true,
      data: [],
    };
  }

  try {
    // Use admin client to bypass RLS policies
    const supabase = await createClient();

    const { data, error } = await supabase
      .from("products_services")
      .select("id, name, base_price, discounted_price, image_url, slug")
      .in("id", productIds)
      .eq("is_available", true);

    if (error) {
      console.error("Error fetching products by IDs:", error);
      return {
        success: false,
        error: "Failed to fetch products",
      };
    }

    return {
      success: true,
      data: data || [],
    };
  } catch (error) {
    console.error("Error in fetchProductsByIds:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}
