import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import DailyVisitTrendChart from '../../../../../../../app/(dashboard)/dashboard/business/analytics/components/DailyVisitTrendChart';
import { useIsMobile } from '@/hooks/use-mobile';
import { formatIndianNumberShort } from '@/lib/utils';

// Mock external modules
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...rest }: any) => <div {...rest}>{children}</div>,
  },
}));

jest.mock('@/hooks/use-mobile', () => ({
  useIsMobile: jest.fn(() => false), // Default to desktop
}));

jest.mock('@/lib/utils', () => ({
  formatIndianNumberShort: jest.fn((num) => num.toString()),
  cn: jest.fn((...args) => args.filter(Boolean).join(' ')),
}));

jest.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select-wrapper">
      <button data-testid="select-trigger">
        <span data-testid="select-value">{value}</span>
      </button>
      <div data-testid="select-content">
        <button data-testid="select-item-7days" onClick={() => onValueChange('7days')}>
          Last 7 Days
        </button>
        <button data-testid="select-item-30days" onClick={() => onValueChange('30days')}>
          Last 30 Days
        </button>
      </div>
    </div>
  ),
  SelectTrigger: ({ children }: any) => <>{children}</>,
  SelectValue: ({ placeholder }: any) => <>{placeholder}</>,
  SelectContent: ({ children }: any) => <>{children}</>,
  SelectItem: ({ children, value }: any) => <>{children}</>,
}));

jest.mock('@/components/ui/chart', () => ({
  ChartContainer: ({ children }: any) => <div data-testid="chart-container">{children}</div>,
  ChartTooltip: ({ content }: any) => <div data-testid="chart-tooltip">{content}</div>,
  ChartTooltipContent: ({ labelFormatter, formatter }: any) => (
    <div data-testid="chart-tooltip-content">
      <p>Label: {labelFormatter('mock-label')}</p>
      <p>Value: {formatter(123)[0]} {formatter(123)[1]}</p>
    </div>
  ),
}));

jest.mock('recharts', () => {
  const { useIsMobile } = require('@/hooks/use-mobile');
  const { formatIndianNumberShort } = require('@/lib/utils');

  return {
    AreaChart: ({ children, data }: any) => {
      const isMobile = useIsMobile();
      const formatXAxisTick = (value: string) => {
        if (isMobile) {
          return new Date(value).getDate().toString();
        }
        return value;
      };

      const formatYAxisTick = (value: number) => {
        formatIndianNumberShort(value);
        return value.toString();
      };

      return (
        <div data-testid="area-chart" data-chart-data={JSON.stringify({ data })}>
          {JSON.stringify({ data })}
          <div data-testid="cartesian-grid" />
          <div data-testid="xaxis">
            <p>XAxis DataKey: formattedDate</p>
            <p>XAxis Tick: {formatXAxisTick('2025-07-19')}</p>
          </div>
          <div data-testid="yaxis">
            <p>YAxis Tick: {formatYAxisTick(100)}</p>
          </div>
          <div data-testid="chart-tooltip">
            <div data-testid="chart-tooltip-content">
              <p>Label: Date: mock-label</p>
              <p>Value: 123 Visits</p>
            </div>
          </div>
          <div data-testid="area" />
        </div>
      );
    },
    Area: () => <div data-testid="area" />,
    XAxis: ({ tickFormatter, dataKey }: any) => (
      <div data-testid="xaxis">
        <p>XAxis DataKey: {dataKey}</p>
        <p>XAxis Tick: {tickFormatter ? tickFormatter('2025-07-19') : '2025-07-19'}</p>
      </div>
    ),
    YAxis: ({ tickFormatter }: any) => (
      <div data-testid="yaxis">
        <p>YAxis Tick: {tickFormatter ? tickFormatter(100) : '100'}</p>
      </div>
    ),
    CartesianGrid: () => <div data-testid="cartesian-grid" />,
  };
});

jest.mock('../../../../../../../app/(dashboard)/dashboard/business/analytics/components/PremiumFeatureLock', () => {
  const MockPremiumFeatureLock = ({ title, description }: any) => (
    <div data-testid="premium-feature-lock">
      <h3>{title}</h3>
      <p>{description}</p>
    </div>
  );
  MockPremiumFeatureLock.displayName = 'MockPremiumFeatureLock';
  return MockPremiumFeatureLock;
});

const mockTrend7Days = [
  { date: '2025-07-13', visits: 10 },
  { date: '2025-07-14', visits: 15 },
  { date: '2025-07-15', visits: 20 },
  { date: '2025-07-16', visits: 25 },
  { date: '2025-07-17', visits: 30 },
  { date: '2025-07-18', visits: 35 },
  { date: '2025-07-19', visits: 40 },
];

const mockTrend30Days = [
  { date: '2025-06-20', visits: 5 },
  { date: '2025-06-21', visits: 10 },
  { date: '2025-07-19', visits: 50 },
];

describe('DailyVisitTrendChart', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useIsMobile as jest.Mock).mockReturnValue(false); // Reset to desktop
    (formatIndianNumberShort as jest.Mock).mockImplementation((num) => num.toString());
  });

  it('renders premium feature lock if user is not premium', () => {
    render(
      <DailyVisitTrendChart
        trend7Days={mockTrend7Days}
        trend30Days={mockTrend30Days}
        userPlan="free"
      />
    );
    expect(screen.getByTestId('premium-feature-lock')).toBeInTheDocument();
    expect(screen.getByText('Daily Visit Trend')).toBeInTheDocument();
    expect(screen.getByText('Upgrade to Growth plan or higher to see detailed daily visit trends for your business.')).toBeInTheDocument();
  });

  it('renders chart for premium user', () => {
    render(
      <DailyVisitTrendChart
        trend7Days={mockTrend7Days}
        trend30Days={mockTrend30Days}
        userPlan="growth"
      />
    );
    expect(screen.queryByTestId('premium-feature-lock')).not.toBeInTheDocument();
    expect(screen.getByTestId('area-chart')).toBeInTheDocument();
    expect(screen.getByText('Daily Visit Trend')).toBeInTheDocument();
  });

  it('displays 7-day trend by default', () => {
    render(
      <DailyVisitTrendChart
        trend7Days={mockTrend7Days}
        trend30Days={mockTrend30Days}
        userPlan="growth"
      />
    );
    const chartElement = screen.getByTestId('area-chart');
    const chartData = JSON.parse(chartElement.getAttribute('data-chart-data') || '{}').data;
    expect(chartData.length).toBe(7);
    expect(chartData[6].visits).toBe(40); // Last day of 7-day trend
  });

  it('switches to 30-day trend when selected', () => {
    render(
      <DailyVisitTrendChart
        trend7Days={mockTrend7Days}
        trend30Days={mockTrend30Days}
        userPlan="growth"
      />
    );

    fireEvent.click(screen.getByTestId('select-trigger'));
    fireEvent.click(screen.getByTestId('select-item-30days'));

    const chartData = JSON.parse(screen.getByTestId('area-chart').dataset.chartData || '{}').data;
    expect(chartData.length).toBe(30);
    expect(chartData[29].visits).toBe(50); // Last day of 30-day trend
  });

  it('formats X-axis ticks correctly for desktop', () => {
    render(
      <DailyVisitTrendChart
        trend7Days={mockTrend7Days}
        trend30Days={mockTrend30Days}
        userPlan="growth"
      />
    );
    expect(screen.getByText('XAxis Tick: 2025-07-19')).toBeInTheDocument();
  });

  it('formats X-axis ticks correctly for mobile', () => {
    (useIsMobile as jest.Mock).mockReturnValue(true);
    render(
      <DailyVisitTrendChart
        trend7Days={mockTrend7Days}
        trend30Days={mockTrend30Days}
        userPlan="growth"
      />
    );
    expect(screen.getByText('XAxis Tick: 19')).toBeInTheDocument();
  });

  it('formats Y-axis ticks using formatIndianNumberShort', () => {
    render(
      <DailyVisitTrendChart
        trend7Days={mockTrend7Days}
        trend30Days={mockTrend30Days}
        userPlan="growth"
      />
    );
    expect(formatIndianNumberShort).toHaveBeenCalledWith(100);
    expect(screen.getByText('YAxis Tick: 100')).toBeInTheDocument();
  });

  it('handles empty trend data', () => {
    render(
      <DailyVisitTrendChart
        trend7Days={[]}
        trend30Days={[]}
        userPlan="growth"
      />
    );
    const chartElement = screen.getByTestId('area-chart');
    const chartData = JSON.parse(chartElement.getAttribute('data-chart-data') || '{}').data;
    expect(chartData.length).toBe(7); // Still 7 days, but all visits should be 0
    expect(chartData.every((item: any) => item.visits === 0)).toBe(true);
  });

  it('ChartTooltipContent formats label and value correctly', () => {
    render(
      <DailyVisitTrendChart
        trend7Days={mockTrend7Days}
        trend30Days={mockTrend30Days}
        userPlan="growth"
      />
    );
    expect(screen.getByText('Label: Date: mock-label')).toBeInTheDocument();
    expect(screen.getByText('Value: 123 Visits')).toBeInTheDocument();
  });
});
