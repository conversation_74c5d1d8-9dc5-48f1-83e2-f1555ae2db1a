import React from 'react';
import { render, fireEvent, waitFor, act, screen } from '@testing-library/react-native';
import PasswordUpdateSection from '@/app/(dashboard)/customer/settings/components/PasswordUpdateSection';
import { useTheme } from '@/src/hooks/useTheme';
import { Alert, TextInput, TouchableOpacity, Text } from 'react-native';

// Mock external modules and components
jest.mock('@/src/hooks/useTheme');
jest.mock('@/src/components/ui/Input', () => ({
  Input: ({ onChangeText, value, ...props }: any) => (
    <TextInput testID={props.label} onChangeText={onChangeText} value={value} {...props} />
  ),
}));
jest.mock('@/src/components/ui/Button', () => ({
  Button: ({ title, onPress, disabled, loading, ...props }: any) => (
    <TouchableOpacity testID={title} onPress={onPress} disabled={disabled} {...props}>
      {loading ? <Text>Loading...</Text> : <Text>{title}</Text>}
    </TouchableOpacity>
  ),
}));
jest.mock('lucide-react-native', () => ({
  KeyRound: 'KeyRound',
  Info: 'Info',
}));
jest.mock('@/styles/dashboard/customer/settings/password-update', () => ({
  createPasswordUpdateSectionStyles: jest.fn(() => ({
    container: {},
    header: {},
    titleContainer: {},
    iconContainer: {},
    titleContent: {},
    title: {},
    subtitle: {},
    content: {},
    infoContainer: {},
    infoContent: {},
    infoText: {},
    form: {},
    inputContainer: {},
    helperText: {},
    buttonContainer: {},
    submitButton: {},
  })),
}));

describe('PasswordUpdateSection', () => {
  const mockTheme = {
    colors: {
      primary: '#D4AF37',
      textPrimary: '#000',
      textSecondary: '#666',
    },
  };

  beforeEach(() => {
    (useTheme as jest.Mock).mockReturnValue(mockTheme);
    (Alert.alert as jest.Mock).mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly for google registration type', () => {
    const { getByText } = render(<PasswordUpdateSection registrationType="google" />);
    expect(getByText('Password')).toBeDefined();
    expect(getByText('You signed up with Google. Password management is handled by your Google account.')).toBeDefined();
    expect(screen.queryByTestId('Current Password')).toBeNull(); // Should not show password fields
  });

  it('renders correctly for email registration type', () => {
    const { getByText, getByTestId } = render(<PasswordUpdateSection registrationType="email" />);
    expect(getByText('Password')).toBeDefined();
    expect(getByTestId('Current Password')).toBeDefined();
    expect(getByTestId('New Password')).toBeDefined();
    expect(getByTestId('Confirm Password')).toBeDefined();
    expect(getByText('Change Password')).toBeDefined();
  });

  it('shows validation errors for empty fields on submit', async () => {
    const { getByText, getByTestId } = render(<PasswordUpdateSection registrationType="email" />);
    fireEvent.press(getByTestId('Change Password'));

    await waitFor(() => {
      expect(getByText('Current password is required')).toBeDefined();
      expect(getByText('New password is required')).toBeDefined();
      expect(getByText('Please confirm your password')).toBeDefined();
    });
  });

  it('shows validation errors for password complexity', async () => {
    const { getByText, getByTestId } = render(<PasswordUpdateSection registrationType="email" />);
    fireEvent.changeText(getByTestId('New Password'), 'short');
    fireEvent.changeText(getByTestId('Confirm Password'), 'short');
    fireEvent.press(getByTestId('Change Password'));

    await waitFor(() => {
      expect(getByText('Password must be at least 6 characters')).toBeDefined();
    });
  });

  it('shows validation error for password mismatch', async () => {
    const { getByText, getByTestId } = render(<PasswordUpdateSection registrationType="email" />);
    fireEvent.changeText(getByTestId('New Password'), 'Password123!');
    fireEvent.changeText(getByTestId('Confirm Password'), 'Password123');
    fireEvent.press(getByTestId('Change Password'));

    await waitFor(() => {
      expect(getByText('Passwords do not match')).toBeDefined();
    });
  });

  it('calls Alert.alert with success message on successful password update', async () => {
    const { getByText, getByTestId } = render(<PasswordUpdateSection registrationType="email" />);

    fireEvent.changeText(getByTestId('Current Password'), 'OldPassword123!');
    fireEvent.changeText(getByTestId('New Password'), 'NewPassword123!');
    fireEvent.changeText(getByTestId('Confirm Password'), 'NewPassword123!');

    fireEvent.press(getByTestId('Change Password'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Password updated successfully!');
    });
  });

  it('calls Alert.alert with error message on failed password update', async () => {
    // Simulate an error during the update process
    // For this mock, we'll just let the try-catch block in the component handle it
    // as there's no actual backend call mocked here.
    const { getByText, getByTestId } = render(<PasswordUpdateSection registrationType="email" />);

    fireEvent.changeText(getByTestId('Current Password'), 'OldPassword123!');
    fireEvent.changeText(getByTestId('New Password'), 'NewPassword123!');
    fireEvent.changeText(getByTestId('Confirm Password'), 'NewPassword123!');

    // Intentionally cause an error by not mocking a successful update
    // In a real scenario, you'd mock the backend call to return an error.
    // For now, we'll just check the generic error alert.
    fireEvent.press(getByTestId('Change Password'));

    await waitFor(() => {
      // Since there's no actual backend call, it will fall into the catch block
      // and show the generic error.
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'An unexpected error occurred');
    });
  });
});
