import { supabase } from "@/lib/supabase";
import { Tables, Json } from "@/src/types/supabase";
import { BusinessDiscoveryData, BusinessProfile } from "@/src/types/business";



/**
 * Discovers and fetches business data by slug for QR code scanning
 * @param businessSlug - The business slug from QR code
 * @returns Business discovery result
 */
export async function discoverBusinessBySlug(
  businessSlug: string
): Promise<BusinessDiscoveryData> {
  if (!businessSlug || typeof businessSlug !== "string") {
    return {
      success: false,
      error: "Invalid business slug",
    };
  }

  const cleanSlug = businessSlug.trim().toLowerCase();

  if (!cleanSlug) {
    return {
      success: false,
      error: "Empty business slug",
    };
  }

  try {
    // Fetch business profile with all necessary data for display using regular client with public read access
    const { data: businessProfile, error } = await supabase
      .from("business_profiles")
      .select(
        `
        id,
        business_name,
        contact_email,
        has_active_subscription,
        trial_end_date,
        created_at,
        updated_at,
        logo_url,
        member_name,
        phone,
        instagram_url,
        whatsapp_number,
        status,
        title,
        address_line,
        city,
        state,
        pincode,
        locality,
        about_bio,
        facebook_url,
        average_rating,
        total_likes,
        total_subscriptions,
        theme_color,
        business_hours,
        delivery_info,
        total_visits,
        today_visits,
        yesterday_visits,
        visits_7_days,
        visits_30_days,
        business_category,
        business_slug,
        gallery,
        city_slug,
        state_slug,
        locality_slug,
        custom_branding,
        custom_ads,
        established_year,
        latitude,
        longitude,
        google_maps_url
      `
      )
      .eq("business_slug", cleanSlug)
      .eq("status", "online")
      .maybeSingle() as { data: BusinessProfile | null; error: any };

    if (error) {
      console.error("Error discovering business:", error);
      return {
        success: false,
        error: "Failed to load business information",
      };
    }

    if (!businessProfile) {
      return {
        success: false,
        error: "Business not found or is currently offline",
      };
    }

    // Fetch subscription data to get plan information
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from("public_subscription_status")
      .select("plan_id, subscription_status")
      .eq("business_profile_id", businessProfile.id)
      .maybeSingle();

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
      // Continue without subscription data
    }

    // Combine business profile with subscription data
    const businessWithPlan: BusinessProfile & { user_plan?: string } = {
      ...businessProfile,
      user_plan: subscriptionData?.plan_id || "free", // Default to free if no subscription
    };

    return {
      success: true,
      data: businessWithPlan,
    };
  } catch (error) {
    console.error("Exception discovering business:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}

/**
 * Gets basic business information for quick preview
 * @param businessSlug - The business slug
 * @returns Basic business info or null
 */
export async function getBusinessPreview(
  businessSlug: string
): Promise<Partial<BusinessProfile> | null> {
  try {
    const { data, error } = await supabase
      .from("business_profiles")
      .select("business_name, logo_url, member_name, city, state")
      .eq("business_slug", businessSlug)
      .eq("status", "online")
      .maybeSingle();

    if (error || !data) {
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error getting business preview:", error);
    return null;
  }
}

/**
 * Checks if business is accessible and returns basic status
 * @param businessSlug - The business slug
 * @returns Business accessibility status
 */
export async function checkBusinessAccessibility(
  businessSlug: string
): Promise<{
  accessible: boolean;
  exists: boolean;
  isOnline: boolean;
  businessName?: string;
}> {
  try {
    const { data, error } = await supabase
      .from("business_profiles")
      .select("business_name, status")
      .eq("business_slug", businessSlug)
      .maybeSingle();

    if (error) {
      return {
        accessible: false,
        exists: false,
        isOnline: false,
      };
    }

    if (!data) {
      return {
        accessible: false,
        exists: false,
        isOnline: false,
      };
    }

    const isOnline = data.status === "online";

    return {
      accessible: isOnline,
      exists: true,
      isOnline,
      businessName: data.business_name,
    };
  } catch (error) {
    console.error("Error checking business accessibility:", error);
    return {
      accessible: false,
      exists: false,
      isOnline: false,
    };
  }
}

/**
 * Discovers business with user-friendly error messages
 * @param businessSlug - The business slug from QR code
 * @returns User-friendly discovery result
 */
export async function discoverBusinessForUser(
  businessSlug: string
): Promise<BusinessDiscoveryData> {
  const result = await discoverBusinessBySlug(businessSlug);

  if (!result.success && result.error) {
    // Convert technical errors to user-friendly messages
    const userFriendlyErrors: Record<string, string> = {
      "Invalid business slug": "Invalid business URL.",
      "Empty business slug": "Business URL is empty.",
      "Business not found or is currently offline":
        "This business is not available right now. It may be temporarily offline or the QR code may be outdated.",
      "Failed to load business information":
        "Unable to load business information. Please check your internet connection and try again.",
      "An unexpected error occurred": "Something went wrong. Please try again.",
    };

    const userFriendlyError = userFriendlyErrors[result.error] || result.error;

    result.error = userFriendlyError;
    return result;
  }

  return result;
}