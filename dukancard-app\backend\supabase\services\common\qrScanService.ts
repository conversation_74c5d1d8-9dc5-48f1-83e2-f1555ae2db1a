import { validateQRCodeForUser } from '@/src/utils/qrCodeUtils';
import { validateBusinessSlug } from '@/backend/supabase/utils/businessSlugValidation';
import { discoverBusinessForUser } from '@/backend/supabase/services/business/businessDiscovery';
import { BusinessDiscoveryData } from "@/src/types/business";

export interface QRScanResult {
  success: boolean;
  businessSlug?: string;
  businessData?: BusinessDiscoveryData;
  error?: string;
}

export interface QRScanOptions {
  validateOnly?: boolean; // Only validate, don't fetch full business data
  requireOnline?: boolean; // Require business to be online (default: true)
}

/**
 * Comprehensive QR scanning service that handles the complete flow
 * from QR code data to business information retrieval.
 * 
 * This service is reusable across customer feed, business dashboard,
 * and any other part of the app that needs QR scanning functionality.
 */
export class QRScanService {
  /**
   * Process a scanned QR code and return business information
   * @param qrData - Raw QR code data from scanner
   * @param options - Scanning options
   * @returns Complete scan result with business data
   */
  static async processQRScan(
    qrData: string,
    options: QRScanOptions = {}
  ): Promise<QRScanResult> {
    const { validateOnly = false, requireOnline = true } = options;

    try {
      // Input validation
      if (!qrData || typeof qrData !== 'string') {
        return {
          success: false,
          error: 'Invalid QR code data'
        };
      }

      const trimmedData = qrData.trim();
      if (!trimmedData) {
        return {
          success: false,
          error: 'Empty QR code data'
        };
      }

      // Step 1: Validate QR code URL and extract business slug
      const qrValidation = validateQRCodeForUser(trimmedData);

      if (!qrValidation.isValid) {
        return {
          success: false,
          error: qrValidation.error || 'Invalid QR code'
        };
      }

      const businessSlug = qrValidation.businessSlug!;

      // Step 2: Validate business exists and is accessible with network error handling
      let businessValidation;
      try {
        businessValidation = await validateBusinessSlug(businessSlug);
      } catch (error) {
        console.error('QRScanService: Network error during business validation:', error);

        if (error instanceof Error) {
          if (error.message.includes('timeout')) {
            return {
              success: false,
              businessSlug,
              error: 'Connection timeout'
            };
          }
          if (error.message.includes('fetch') || error.message.includes('network')) {
            return {
              success: false,
              businessSlug,
              error: 'Network error'
            };
          }
        }

        return {
          success: false,
          businessSlug,
          error: 'Failed to load business information'
        };
      }

      if (!businessValidation.exists) {
        return {
          success: false,
          businessSlug,
          error: businessValidation.error || 'Business not found'
        };
      }

      // Step 3: Check online status if required
      if (requireOnline && !businessValidation.isOnline) {
        return {
          success: false,
          businessSlug,
          error: businessValidation.error || 'Business is currently offline'
        };
      }

      // Step 4: Return early if only validation is needed
      if (validateOnly) {
        return {
          success: true,
          businessSlug
        };
      }

      // Step 5: Fetch complete business data for display with enhanced error handling
      let businessDiscovery;
      try {
        businessDiscovery = await this.fetchBusinessDataWithRetry(businessSlug);
      } catch (error) {
        console.error('QRScanService: Error fetching business data:', error);

        if (error instanceof Error) {
          if (error.message.includes('timeout')) {
            return {
              success: false,
              businessSlug,
              error: 'Connection timeout'
            };
          }
          if (error.message.includes('network') || error.message.includes('fetch')) {
            return {
              success: false,
              businessSlug,
              error: 'Network error'
            };
          }
          if (error.message.includes('not found') || error.message.includes('404')) {
            return {
              success: false,
              businessSlug,
              error: 'Business not found'
            };
          }
        }

        return {
          success: false,
          businessSlug,
          error: 'Failed to load business information'
        };
      }

      if (!businessDiscovery || !businessDiscovery.success) {
        return {
          success: false,
          businessSlug,
          error: businessDiscovery?.error || 'Business profile is incomplete'
        };
      }

      return {
        success: true,
        businessSlug,
        businessData: businessDiscovery.data
      };

    } catch (error) {
      console.error('QRScanService: Unexpected error processing QR scan:', error);

      // Provide more specific error messages based on error type
      if (error instanceof TypeError) {
        return {
          success: false,
          error: 'Invalid QR code data'
        };
      }

      if (error instanceof Error) {
        if (error.message.includes('fetch') || error.message.includes('network')) {
          return {
            success: false,
            error: 'Network error'
          };
        }
        if (error.message.includes('timeout')) {
          return {
            success: false,
            error: 'Connection timeout'
          };
        }
      }

      return {
        success: false,
        error: 'An unexpected error occurred while processing the QR code'
      };
    }
  }

  /**
   * Fetch business data with retry logic and timeout handling
   */
  private static async fetchBusinessDataWithRetry(
    businessSlug: string,
    maxRetries: number = 2,
    timeoutMs: number = 10000
  ): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Create a timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Connection timeout')), timeoutMs);
        });

        // Race between the actual request and timeout
        const businessDataPromise = discoverBusinessForUser(businessSlug);
        const result = await Promise.race([businessDataPromise, timeoutPromise]);

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.warn(`QRScanService: Attempt ${attempt + 1} failed:`, lastError.message);

        // Don't retry on certain errors
        if (lastError.message.includes('not found') || lastError.message.includes('404')) {
          throw lastError;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw lastError || new Error('Failed to fetch business data after retries');
  }

  /**
   * Quick validation of QR code without fetching business data
   * Useful for preview or quick checks
   */
  static async validateQRCode(qrData: string): Promise<QRScanResult> {
    return this.processQRScan(qrData, { validateOnly: true });
  }

  /**
   * Process QR code allowing offline businesses
   * Useful for business dashboard where owners might want to scan offline businesses
   */
  static async processQRScanAllowOffline(qrData: string): Promise<QRScanResult> {
    return this.processQRScan(qrData, { requireOnline: false });
  }

  /**
   * Get user-friendly error message for common QR scan issues
   */
  static getErrorMessage(error: string): string {
    const errorMessages: Record<string, string> = {
      // QR Code validation errors
      'Invalid QR code': 'This QR code is not valid. Please try scanning again.',
      'Invalid QR code data': 'Unable to read QR code. Please ensure it\'s clearly visible and try again.',
      'Empty QR code data': 'QR code appears to be empty. Please try scanning again.',
      'QR code does not contain a valid URL': 'This QR code doesn\'t contain a valid web address.',
      'QR code is not from Dukancard': 'This QR code is not from Dukancard. Please scan a Dukancard business QR code.',
      'QR code does not contain a business profile URL': 'This QR code doesn\'t link to a business profile.',
      'Invalid business URL format': 'The business URL in this QR code is not valid.',

      // Business validation errors
      'Business not found': 'This business was not found. The QR code may be outdated or the business may have been removed.',
      'Business is currently offline': 'This business is temporarily unavailable. Please try again later.',
      'Business profile is incomplete': 'This business profile is not yet complete. Please try again later.',

      // Network and connectivity errors
      'Failed to load business information': 'Unable to load business details. Please check your internet connection and try again.',
      'Network error': 'Network connection failed. Please check your internet connection and try again.',
      'Connection timeout': 'Request timed out. Please check your internet connection and try again.',
      'Server error': 'Server is temporarily unavailable. Please try again in a few moments.',

      // Camera and permission errors
      'Camera permission denied': 'Camera access is required to scan QR codes. Please enable camera permissions in settings.',
      'Camera not available': 'Camera is not available on this device.',
      'Gallery permission denied': 'Gallery access is required to select images. Please enable gallery permissions in settings.',

      // Image processing errors
      'No QR code found in image': 'No QR code was found in the selected image. Please select an image containing a clear QR code.',
      'Image processing failed': 'Failed to process the selected image. Please try with a different image.',
      'Image too large': 'The selected image is too large. Please try with a smaller image.',
      'Image format not supported': 'This image format is not supported. Please try with a JPEG or PNG image.',

      // Generic errors
      'An unexpected error occurred while processing the QR code': 'Something went wrong while processing the QR code. Please try scanning again.',
      'Service temporarily unavailable': 'The QR scanning service is temporarily unavailable. Please try again later.',
      'Rate limit exceeded': 'Too many scan attempts. Please wait a moment before trying again.'
    };

    return errorMessages[error] || `Scan failed: ${error}. Please try again.`;
  }

  /**
   * Get error recovery suggestions based on error type
   */
  static getErrorRecoveryAction(error: string): {
    action: 'retry' | 'permission' | 'network' | 'contact_support' | 'none';
    message: string;
  } {
    const recoveryActions: Record<string, { action: 'retry' | 'permission' | 'network' | 'contact_support' | 'none'; message: string }> = {
      // Retry actions
      'Invalid QR code': { action: 'retry', message: 'Try scanning the QR code again with better lighting.' },
      'Business not found': { action: 'retry', message: 'Verify the QR code is current and try again.' },
      'No QR code found in image': { action: 'retry', message: 'Select a clearer image with a visible QR code.' },

      // Permission actions
      'Camera permission denied': { action: 'permission', message: 'Enable camera permissions in device settings.' },
      'Gallery permission denied': { action: 'permission', message: 'Enable gallery permissions in device settings.' },

      // Network actions
      'Network error': { action: 'network', message: 'Check your internet connection and try again.' },
      'Connection timeout': { action: 'network', message: 'Check your internet connection and try again.' },
      'Failed to load business information': { action: 'network', message: 'Check your internet connection and try again.' },

      // Contact support actions
      'Service temporarily unavailable': { action: 'contact_support', message: 'If this persists, please contact support.' },
      'Server error': { action: 'contact_support', message: 'If this persists, please contact support.' }
    };

    return recoveryActions[error] || { action: 'retry', message: 'Please try again.' };
  }

  /**
   * Check if a URL is a valid Dukancard business QR code
   */
  static isDukancardQR(qrData: string): boolean {
    const validation = validateQRCodeForUser(qrData);
    return validation.isValid;
  }

  /**
   * Extract business slug from QR data without validation
   * Useful for quick slug extraction
   */
  static extractBusinessSlug(qrData: string): string | null {
    const validation = validateQRCodeForUser(qrData);
    return validation.businessSlug || null;
  }
}

/**
 * Convenience function for the most common use case
 * Process QR code and get business data for customer feed
 */
export async function scanQRForCustomerFeed(qrData: string): Promise<QRScanResult> {
  return QRScanService.processQRScan(qrData, { requireOnline: true });
}

/**
 * Convenience function for business dashboard QR scanning
 * Allows scanning offline businesses for business owners
 */
export async function scanQRForBusinessDashboard(qrData: string): Promise<QRScanResult> {
  return QRScanService.processQRScanAllowOffline(qrData);
}

/**
 * Quick QR validation without fetching business data
 */
export async function validateQROnly(qrData: string): Promise<QRScanResult> {
  return QRScanService.validateQRCode(qrData);
}
