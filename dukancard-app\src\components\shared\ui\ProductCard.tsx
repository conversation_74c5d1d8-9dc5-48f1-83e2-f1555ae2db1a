import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from "react-native";
import { ProductsServices } from "@/backend/supabase/services/business/businessProductsService";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { router } from "expo-router";
import { useLocation } from "@/src/contexts/LocationContext";
import {
  calculateDistanceWithFallback,
  formatDistance,
} from "@/src/utils/distanceCalculation";

interface ProductCardProps {
  product: ProductsServices;
  isClickable?: boolean;
  width?: number;
  variant?: "default" | "compact" | "large";
  // Optional business location data for distance calculation
  businessLatitude?: number | null;
  businessLongitude?: number | null;
  showDistance?: boolean;
}

const { width: screenWidth } = Dimensions.get("window");
const DEFAULT_PRODUCT_WIDTH = (screenWidth - 60) / 2; // Account for padding and gap

export function ProductCard({
  product,
  isClickable = true,
  width,
  variant = "default",
  businessLatitude,
  businessLongitude,
  showDistance = true,
}: ProductCardProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";
  const { currentLocation } = useLocation();
  const [imageError, setImageError] = useState(false);

  // Calculate distance if business location is provided
  const getDistanceText = () => {
    if (!showDistance || !businessLatitude || !businessLongitude) {
      return null;
    }

    const distance = calculateDistanceWithFallback(
      currentLocation,
      null, // No profile location fallback for products
      null,
      businessLatitude,
      businessLongitude
    );

    if (distance !== null) {
      return formatDistance(distance);
    }

    return null;
  };

  // Calculate dynamic width based on variant or provided width
  const cardWidth =
    width ||
    (variant === "compact"
      ? DEFAULT_PRODUCT_WIDTH * 0.8
      : variant === "large"
      ? DEFAULT_PRODUCT_WIDTH * 1.2
      : DEFAULT_PRODUCT_WIDTH);

  // Calculate image height based on card width to match Next.js aspect-square design
  const imageHeight = cardWidth; // Square aspect ratio like Next.js

  // Theme colors
  const backgroundColor = isDark ? "#1a1a1a" : "#ffffff";
  const borderColor = isDark ? "#333333" : "#e5e5e5";
  const textColor = isDark ? "#ffffff" : "#000000";
  const subtitleColor = isDark ? "#a1a1aa" : "#6b7280";
  const goldColor = "#D4AF37";

  // Format currency
  const formatCurrency = (amount: number | undefined | null): string => {
    if (amount === null || amount === undefined) return "";
    return `₹${amount.toLocaleString("en-IN")}`;
  };

  const formattedBasePrice = formatCurrency(product.base_price);
  const formattedDiscountedPrice = formatCurrency(product.discounted_price);

  // Determine final price and discount
  let finalPrice = formattedBasePrice;
  let priceToShowStrikethrough: string | null = null;
  let discountPercentage = 0;

  const hasDiscountedPrice =
    product.discounted_price !== null &&
    product.discounted_price !== undefined &&
    product.discounted_price > 0;
  const hasBasePrice =
    product.base_price !== null &&
    product.base_price !== undefined &&
    product.base_price > 0;

  if (
    hasDiscountedPrice &&
    hasBasePrice &&
    product.discounted_price! < product.base_price!
  ) {
    finalPrice = formattedDiscountedPrice;
    priceToShowStrikethrough = formattedBasePrice;
    discountPercentage = Math.round(
      ((product.base_price! - product.discounted_price!) /
        product.base_price!) *
        100
    );
  }

  const handlePress = () => {
    if (isClickable) {
      router.push(`/product/${product.id}`);
    }
  };

  const content = (
    <View
      style={[
        styles.container,
        {
          backgroundColor,
          borderColor,
          width: cardWidth,
        },
      ]}
    >
      {/* Product Image */}
      <View style={[styles.imageContainer, { height: imageHeight }]}>
        {product.image_url && !imageError ? (
          <Image
            source={{ uri: product.image_url }}
            style={styles.productImage}
            onError={() => setImageError(true)}
            resizeMode="cover"
          />
        ) : (
          <View
            style={[styles.placeholderImage, { backgroundColor: borderColor }]}
          >
            <Text style={[styles.placeholderText, { color: subtitleColor }]}>
              {product.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}

        {/* Distance Badge - Bottom Left */}
        {getDistanceText() && (
          <View
            style={[
              styles.distanceBadge,
              { backgroundColor: isDark ? "#000000" : "#FFFFFF" },
            ]}
          >
            <Text
              style={[
                styles.distanceBadgeText,
                { color: isDark ? "#FFFFFF" : "#000000" },
              ]}
            >
              {getDistanceText()}
            </Text>
          </View>
        )}

        {/* Out of Stock Overlay */}
        {!product.is_available && (
          <View style={styles.outOfStockOverlay}>
            <View
              style={[
                styles.outOfStockBadge,
                { backgroundColor: backgroundColor + "CC" },
              ]}
            >
              <Text style={[styles.outOfStockText, { color: textColor }]}>
                OUT OF STOCK
              </Text>
            </View>
          </View>
        )}

        {/* Discount Badge */}
        {discountPercentage > 0 && (
          <View style={styles.discountBadge}>
            <Text style={styles.discountSaveText}>SAVE</Text>
            <Text style={styles.discountPercentText}>
              {discountPercentage}%
            </Text>
          </View>
        )}
      </View>

      {/* Product Info */}
      <View style={styles.productInfo}>
        <Text
          style={[styles.productName, { color: textColor }]}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {product.name}
        </Text>

        {/* Price Section */}
        <View style={styles.priceContainer}>
          <View style={styles.priceGroup}>
            {finalPrice && finalPrice.length > 0 && (
              <Text
                style={[styles.finalPrice, { color: textColor }]}
                numberOfLines={1}
              >
                {finalPrice}
              </Text>
            )}

            {priceToShowStrikethrough &&
              priceToShowStrikethrough.length > 0 && (
                <Text
                  style={[styles.originalPrice, { color: subtitleColor }]}
                  numberOfLines={1}
                >
                  {priceToShowStrikethrough}
                </Text>
              )}
          </View>
        </View>
      </View>
    </View>
  );

  if (isClickable) {
    return (
      <TouchableOpacity
        onPress={handlePress}
        style={[styles.touchable, { width: cardWidth }]}
      >
        {content}
      </TouchableOpacity>
    );
  }

  // Return content without TouchableOpacity if not clickable
  return (
    <View style={[styles.touchable, { width: cardWidth }]}>{content}</View>
  );
}

const styles = StyleSheet.create({
  touchable: {
    // Width will be set dynamically
  },
  container: {
    // Width will be set dynamically
    borderRadius: 8,
    borderWidth: 1,
    overflow: "hidden",
    marginBottom: 8,
    padding: 2, // Reduced padding to match Next.js design
  },
  imageContainer: {
    width: "100%",
    // Height will be set dynamically
    borderRadius: 8,
    overflow: "hidden",
    position: "relative",
  },
  productImage: {
    width: "100%",
    height: "100%",
    borderRadius: 8,
  },
  placeholderImage: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: "bold",
  },
  productInfo: {
    padding: 6, // Reduced padding to match Next.js compact design
  },
  productName: {
    fontSize: 12, // Smaller font to match Next.js compact design
    fontWeight: "600",
    marginBottom: 4, // Reduced margin
    lineHeight: 16,
  },
  priceContainer: {
    paddingTop: 4,
  },
  priceGroup: {
    flexDirection: "row",
    alignItems: "baseline",
    gap: 6,
    flexWrap: "wrap",
  },
  finalPrice: {
    fontSize: 12, // Smaller font to match Next.js
    fontWeight: "bold",
    flex: 1,
  },
  originalPrice: {
    fontSize: 10,
    textDecorationLine: "line-through",
    opacity: 0.6,
  },
  outOfStockOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  outOfStockBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  outOfStockText: {
    fontSize: 10,
    fontWeight: "600",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  discountBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "#EF4444",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  discountSaveText: {
    fontSize: 7,
    fontWeight: "500",
    color: "#FFFFFF",
    textAlign: "center",
  },
  discountPercentText: {
    fontSize: 10,
    fontWeight: "bold",
    color: "#FFFFFF",
    lineHeight: 12,
  },
  distanceBadge: {
    position: "absolute",
    bottom: 6,
    left: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  distanceBadgeText: {
    fontSize: 12,
    fontWeight: "700",
    textAlign: "center",
  },
});
