import { renderHook, act } from '@testing-library/react-hooks';
import { useDiscoveryNavigation } from '@/src/components/discovery/NavigationHandlers';
import { useRouter } from 'expo-router';
import { useToast } from '@/src/components/ui/Toast';
import { Share } from 'react-native';

// Mock dependencies
jest.mock('expo-router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('@/src/components/ui/Toast', () => ({
  useToast: jest.fn(),
}));
jest.mock('react-native', () => ({
  Share: {
    share: jest.fn(),
  },
}));

describe('useDiscoveryNavigation', () => {
  const mockRouter = {
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
    canGoBack: jest.fn(() => true),
  };
  const mockToast = {
    error: jest.fn(),
  };

  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useToast as jest.Mock).mockReturnValue(mockToast);
    jest.clearAllMocks();
  });

  it('navigates to business profile', () => {
    const { result } = renderHook(() => useDiscoveryNavigation());
    const business = { business_slug: 'test-business' };

    act(() => {
      result.current.navigateToBusinessProfile(business as any);
    });

    expect(mockRouter.push).toHaveBeenCalledWith('/business/test-business');
  });

  it('navigates to product detail', () => {
    const { result } = renderHook(() => useDiscoveryNavigation());
    const product = { id: 'test-product' };

    act(() => {
      result.current.navigateToProductDetail(product as any);
    });

    expect(mockRouter.push).toHaveBeenCalledWith('/product/test-product');
  });

  it('handles navigation errors gracefully', () => {
    (useRouter as jest.Mock).mockImplementation(() => {
      throw new Error('Test error');
    });

    const { result } = renderHook(() => useDiscoveryNavigation());

    act(() => {
      result.current.navigateToHome();
    });

    expect(mockToast.error).toHaveBeenCalledWith(
      'Navigation Error',
      'Failed to navigate to home'
    );
  });

  it('shares a business profile', async () => {
    const { result } = renderHook(() => useDiscoveryNavigation());
    const business = { business_name: 'Test Biz', business_slug: 'test-biz' };

    await act(async () => {
      await result.current.shareBusinessProfile(business as any);
    });

    expect(Share.share).toHaveBeenCalledWith({
      message: 'Check out Test Biz on DukanCard!\n\nhttps://dukancard.com/business/test-biz',
      url: 'https://dukancard.com/business/test-biz',
      title: 'Test Biz - DukanCard',
    });
  });
});