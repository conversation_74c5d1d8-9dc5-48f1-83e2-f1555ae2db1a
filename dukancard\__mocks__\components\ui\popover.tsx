import React from 'react';

export const Popover = ({ children }: any) => (
  <div data-testid="popover">{children}</div>
);

export const PopoverContent = ({ children }: any) => (
  <div data-testid="popover-content">{children}</div>
);

export const PopoverTrigger = ({ children, asChild }: any) => {
  if (asChild) {
    return React.cloneElement(children);
  }
  return <div data-testid="popover-trigger">{children}</div>;
};
