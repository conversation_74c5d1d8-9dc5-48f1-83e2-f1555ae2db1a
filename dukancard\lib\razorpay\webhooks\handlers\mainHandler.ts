import { createClient } from "@/utils/supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { verifyWebhookSignature } from "../../utils/auth";
import { RazorpayWebhookData } from "../../types/api";
import { RazorpaySubscriptionEventType } from "../types";
import { logWebhookError, updateWebhookErrorLog } from "../errorTracking";
import { isWebhookEventProcessed, markWebhookEventAsProcessed } from "../idempotency";

// Import handlers
import {
  handlePaymentAuthorized,
  handlePaymentCaptured,
  handlePaymentFailed,
  handleInvoicePaid,
  handleRefundCreated,
  handleRefundProcessed,
  handleRefundFailed
} from './index';

import {
  handleSubscriptionAuthenticated,
  handleSubscriptionActivated,
  handleSubscriptionCharged,
  handleSubscriptionPending,
  handleSubscriptionHalted,
  handleSubscriptionCancelled,
  handleSubscriptionCompleted,
  handleSubscriptionExpired,
  handleSubscriptionUpdated
} from './subscriptionEventHandlers';

// Extended webhook data type with index signature for compatibility
export interface ExtendedRazorpayWebhookData extends RazorpayWebhookData {
  [key: string]: unknown;
}

/**
 * Handle Razorpay webhook events
 *
 * This function handles webhook events from Razorpay, including:
 * - Verifying the webhook signature
 * - Checking for duplicate events (idempotency using x-razorpay-event-id)
 * - Processing the event based on its type
 * - Logging errors for tracking and retry
 *
 * @param payload The webhook payload (parsed JSON)
 * @param signature The webhook signature
 * @param errorId Optional error ID for retry attempts
 * @param rawBody Optional raw request body as string (for signature verification)
 * @param razorpayEventId Optional unique Razorpay event ID from x-razorpay-event-id header
 * @returns The result of handling the webhook
 */
export async function handleRazorpayWebhook(
  payload: ExtendedRazorpayWebhookData,
  signature: string,
  errorId?: string,
  rawBody?: string,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string; error_id?: string }> {
  try {
    // Process webhook event

    // Extract subscription ID for error tracking
    const subscriptionId = payload.payload.subscription?.id ||
                          payload.payload.payment?.entity?.notes?.subscription_id ||
                          undefined;

    // Extract the relevant entity ID for tracking and legacy support
    let entityId: string | undefined;

    if (payload.event.startsWith('subscription.')) {
      // For subscription events, use subscription ID
      // FIXED: Use the existing extractEntityId function for consistency
      const { extractEntityId } = await import('../validation');
      entityId = extractEntityId(payload) || undefined;
    } else if (payload.event.startsWith('payment.')) {
      // For payment events, use payment ID
      entityId = payload.payload.payment?.entity?.id;
    } else if (payload.event.startsWith('refund.')) {
      // For refund events, use refund ID
      entityId = payload.payload.refund?.entity?.id;
    } else if (payload.event.startsWith('invoice.')) {
      // For invoice events, use payment ID (invoices are linked to payments)
      entityId = payload.payload.payment?.entity?.id;
    }



    // Verify webhook signature (skip for retry attempts and testing)
    if (!errorId && signature && signature.trim() !== '') {
      const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;

      if (!webhookSecret) {
        console.error("[RAZORPAY_WEBHOOK] Webhook secret not configured");

        // Log the error for tracking
        const logResult = await logWebhookError(
          payload.event,
          entityId,
          subscriptionId,
          "Webhook secret not configured",
          payload as unknown as Record<string, unknown>
        );

        return {
          success: false,
          message: "Webhook secret not configured",
          error_id: logResult.error_id
        };
      }

      // Use the raw body for signature verification if provided, otherwise stringify the payload
      const payloadForVerification = rawBody || JSON.stringify(payload);
      const isValid = verifyWebhookSignature(
        payloadForVerification,
        signature,
        webhookSecret
      );

      if (!isValid) {
        console.error("[RAZORPAY_WEBHOOK] Invalid webhook signature");

        // Log the error for tracking
        const logResult = await logWebhookError(
          payload.event,
          entityId,
          subscriptionId,
          "Invalid webhook signature",
          payload as unknown as Record<string, unknown>
        );

        return {
          success: false,
          message: "Invalid webhook signature",
          error_id: logResult.error_id
        };
      }
    } else if (!errorId && signature === '') {
      console.log("[RAZORPAY_WEBHOOK] Skipping signature verification for testing");
    }

    // Process the webhook event
    const eventType = payload.event;

    // Get admin Supabase client for all webhook operations to bypass RLS
    const supabase: SupabaseClient = await createClient();

    // Idempotency check using Razorpay Event ID (skip for retry attempts)
    if (!errorId && razorpayEventId) {
      // Check if this event has already been processed using the unique Razorpay event ID
      const isProcessed = await isWebhookEventProcessed(
        razorpayEventId,
        eventType,
        payload,
        supabase
      );

      if (isProcessed) {
        return {
          success: true,
          message: "Event already processed (idempotent)"
        };
      }
    }

    // Process the webhook based on event type
    let result;

    try {
      switch (eventType) {
        // Subscription events
        case RazorpaySubscriptionEventType._SUBSCRIPTION_AUTHENTICATED:
          result = await handleSubscriptionAuthenticated(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._SUBSCRIPTION_ACTIVATED:
          result = await handleSubscriptionActivated(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._SUBSCRIPTION_CHARGED:
          result = await handleSubscriptionCharged(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._SUBSCRIPTION_PENDING:
          result = await handleSubscriptionPending(payload, supabase, razorpayEventId);
          break; 

        case RazorpaySubscriptionEventType._SUBSCRIPTION_HALTED:
          result = await handleSubscriptionHalted(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._SUBSCRIPTION_CANCELLED:
          result = await handleSubscriptionCancelled(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._SUBSCRIPTION_COMPLETED:
          result = await handleSubscriptionCompleted(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._SUBSCRIPTION_EXPIRED:
          result = await handleSubscriptionExpired(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._SUBSCRIPTION_UPDATED:
          result = await handleSubscriptionUpdated(payload, supabase, razorpayEventId);
          break;

        // Payment events
        case RazorpaySubscriptionEventType._PAYMENT_AUTHORIZED:
          result = await handlePaymentAuthorized(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._PAYMENT_CAPTURED:
          result = await handlePaymentCaptured(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._PAYMENT_FAILED:
          result = await handlePaymentFailed(payload, { eventId: razorpayEventId || `payment_failed_${Date.now()}` });
          break;

        // Invoice events
        case RazorpaySubscriptionEventType._INVOICE_PAID:
          result = await handleInvoicePaid(payload, { eventId: razorpayEventId || `invoice_paid_${Date.now()}` });
          break;

        // Refund events
        case RazorpaySubscriptionEventType._REFUND_CREATED:
          result = await handleRefundCreated(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._REFUND_PROCESSED:
          result = await handleRefundProcessed(payload, supabase, razorpayEventId);
          break;

        case RazorpaySubscriptionEventType._REFUND_FAILED:
          result = await handleRefundFailed(payload, supabase, razorpayEventId);
          break;

        default:
          result = { success: true, message: "Event acknowledged but not processed" };
      }

      // If this was a retry attempt and it succeeded, update the error log
      if (errorId && result.success) {
        await updateWebhookErrorLog(errorId, 'resolved');
      }

      // Mark the event as processed (for idempotency)
      if (!errorId && result.success && razorpayEventId) {
        await markWebhookEventAsProcessed(
          razorpayEventId,
          eventType,
          payload,
          'processed',
          undefined,
          entityId
        );
      }

      return result;
    } catch (processingError) {
      console.error(`[RAZORPAY_WEBHOOK] Error processing ${eventType} webhook:`, processingError);

      // If this was a retry attempt, update the error log
      if (errorId) {
        const errorMessage = processingError instanceof Error ? processingError.message : String(processingError);
        await updateWebhookErrorLog(
          errorId,
          'retrying',
          undefined,
          errorMessage
        );

        return {
          success: false,
          message: `Error processing webhook: ${errorMessage}`,
          error_id: errorId
        };
      }

      // Log the error for tracking
      const errorMessage = processingError instanceof Error ? processingError.message : String(processingError);
      const logResult = await logWebhookError(
        eventType,
        entityId,
        subscriptionId,
        errorMessage,
        payload as unknown as Record<string, unknown>,
        supabase
      );

      return {
        success: false,
        message: `Error processing webhook: ${errorMessage}`,
        error_id: logResult.error_id
      };
    }
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Unhandled error in webhook handler:", error);

    // Log the error for tracking
    const errorMessage = error instanceof Error ? error.message : String(error);
    const logResult = await logWebhookError(
      payload.event || 'unknown',
      undefined,
      undefined,
      errorMessage,
      payload as unknown as Record<string, unknown>
    );

    return {
      success: false,
      message: `Unhandled error in webhook handler: ${errorMessage}`,
      error_id: logResult.error_id
    };
  }
}
