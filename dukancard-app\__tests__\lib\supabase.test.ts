/**
 * Supabase Library Tests
 * Tests the main supabase client and utility functions
 */

import { SUPABASE_CONFIG } from '../../src/config/publicKeys';

const mockSupabaseClient = {
  auth: {
    getSession: jest.fn(),
    signOut: jest.fn(),
    startAutoRefresh: jest.fn(),
    stopAutoRefresh: jest.fn(),
  },
};

// Mock the Supabase client creation
const mockCreateClient = jest.fn(() => mockSupabaseClient);

// Mock all dependencies before importing the module
jest.mock('@supabase/supabase-js', () => ({
  createClient: mockCreateClient,
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

const mockAddEventListener = jest.fn();
jest.mock('react-native', () => ({
  AppState: {
    addEventListener: mockAddEventListener,
    removeEventListener: jest.fn(),
  },
}));

// Mock the config/supabase module to return our mock client
jest.mock('../../src/config/supabase', () => ({
  supabase: mockSupabaseClient,
  default: mockSupabaseClient,
}));

// Import the actual module after all mocks are set up
import { getUserDisplayName } from '@/lib/supabase';

describe('Supabase Configuration', () => {
  it('should have valid configuration values', () => {
    expect(SUPABASE_CONFIG.url).toBeDefined();
    expect(SUPABASE_CONFIG.anonKey).toBeDefined();
    expect(SUPABASE_CONFIG.url).toContain('supabase.co');
    expect(SUPABASE_CONFIG.anonKey).toMatch(/^eyJ/); // JWT tokens start with eyJ
  });
});

describe('getUserDisplayName', () => {
  it('should return full_name if available', () => {
    const user = { id: '1', user_metadata: { full_name: 'John Doe' } };
    expect(getUserDisplayName(user)).toBe('John Doe');
  });

  it('should return name if full_name is not available but name is', () => {
    const user = { id: '1', user_metadata: { name: 'Jane Smith' } };
    expect(getUserDisplayName(user)).toBe('Jane Smith');
  });

  it('should return email prefix if full_name and name are not available', () => {
    const user = { id: '1', email: '<EMAIL>' };
    expect(getUserDisplayName(user)).toBe('test');
  });

  it('should return "User" if no name or email is available', () => {
    const user = { id: '1' };
    expect(getUserDisplayName(user)).toBe('User');
  });

  it('should return empty string if user is null', () => {
    expect(getUserDisplayName(null)).toBe('');
  });
});

describe('Supabase Module Structure', () => {
  it('should have proper module structure', () => {
    // Test that the module can be imported without errors
    expect(getUserDisplayName).toBeDefined();
    expect(typeof getUserDisplayName).toBe('function');
  });
});