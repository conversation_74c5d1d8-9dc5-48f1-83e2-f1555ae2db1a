import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ProfileRequirementDialog from '@/app/(dashboard)/dashboard/customer/profile/components/ProfileRequirementDialog';
import { useSearchParams, useRouter } from 'next/navigation';

// Mock next/navigation hooks
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
  useRouter: jest.fn(() => ({
    replace: jest.fn(),
  })),
}));

// Mock UI components (Dialog, Button, etc.)
jest.mock('@/components/ui/dialog', () => ({
  Dialog: ({ open, onOpenChange, children }: any) => (
    open ? <div data-testid="mock-dialog" data-open={open}>{children}</div> : null
  ),
  DialogContent: ({ children }: any) => <div data-testid="mock-dialog-content">{children}</div>,
  DialogDescription: ({ children }: any) => <p data-testid="mock-dialog-description">{children}</p>,
  DialogTitle: ({ children }: any) => <h2 data-testid="mock-dialog-title">{children}</h2>,
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ onClick, children }: any) => <button onClick={onClick}>{children}</button>,
}));

jest.mock('lucide-react', () => ({
  AlertCircle: () => <svg data-testid="alert-circle-icon" />,
  Mail: () => <svg data-testid="mail-icon" />,
  Phone: () => <svg data-testid="phone-icon" />,
  MapPin: () => <svg data-testid="map-pin-icon" />,
  CheckCircle: () => <svg data-testid="check-circle-icon" />,
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children }: any) => <div>{children}</div>,
  },
}));

describe('ProfileRequirementDialog', () => {
  const mockRouterReplace = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({
      replace: mockRouterReplace,
    });
    // Default mock for useSearchParams to return empty params
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams());

    // Mock window.location.pathname
    jest.spyOn(window, 'location', 'get').mockReturnValue({
      ...window.location,
      pathname: '/dashboard/customer/profile',
    });
  });

  it('does not render dialog when no missing params and address is complete', () => {
    render(<ProfileRequirementDialog hasCompleteAddress={true} />);
    expect(screen.queryByTestId('mock-dialog')).not.toBeInTheDocument();
  });

  it('renders dialog when missing param is present in URL', async () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams('missing=email,phone'));
    render(<ProfileRequirementDialog hasCompleteAddress={true} />);

    await waitFor(() => {
      expect(screen.getByTestId('mock-dialog')).toBeInTheDocument();
      expect(screen.getByText('Complete Your Profile')).toBeInTheDocument();
      expect(screen.getByText('Email Address')).toBeInTheDocument();
      expect(screen.getByText('Mobile Number')).toBeInTheDocument();
    });
    expect(mockRouterReplace).toHaveBeenCalledWith('/dashboard/customer/profile', { scroll: false });
  });

  it('renders dialog when message param is present in URL', async () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams('message=Some message'));
    render(<ProfileRequirementDialog />);

    await waitFor(() => {
      expect(screen.getByTestId('mock-dialog')).toBeInTheDocument();
      expect(screen.getByText('Complete Your Profile')).toBeInTheDocument();
    });
    expect(mockRouterReplace).toHaveBeenCalledWith('/dashboard/customer/profile', { scroll: false });
  });

  it('renders dialog when hasCompleteAddress is false and there are URL params', async () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams('missing=email'));
    render(<ProfileRequirementDialog hasCompleteAddress={false} />);

    await waitFor(() => {
      expect(screen.getByTestId('mock-dialog')).toBeInTheDocument();
      expect(screen.getByText('Address Information')).toBeInTheDocument();
    });
  });

  it('prioritizes hasCompleteAddress over missing param if both present', async () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams('missing=email'));
    render(<ProfileRequirementDialog hasCompleteAddress={false} />);

    await waitFor(() => {
      expect(screen.getByTestId('mock-dialog')).toBeInTheDocument();
      expect(screen.getByText('Address Information')).toBeInTheDocument();
      expect(screen.queryByText('Email Address')).not.toBeInTheDocument();
    });
  });

  it('closes dialog when Got it button is clicked', async () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams('missing=address'));
    render(<ProfileRequirementDialog hasCompleteAddress={true} />);

    await waitFor(() => {
      expect(screen.getByTestId('mock-dialog')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByRole('button', { name: /Got it, let me complete my profile/i }));

    await waitFor(() => {
      expect(screen.queryByTestId('mock-dialog')).not.toBeInTheDocument();
    });
  });

  it('displays correct field info for email', () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams('missing=email'));
    render(<ProfileRequirementDialog hasCompleteAddress={true} />);
    expect(screen.getByText('Email Address')).toBeInTheDocument();
    expect(screen.getByText('Required for account notifications and password reset')).toBeInTheDocument();
    expect(screen.getByTestId('mail-icon')).toBeInTheDocument();
  });

  it('displays correct field info for phone', () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams('missing=phone'));
    render(<ProfileRequirementDialog hasCompleteAddress={true} />);
    expect(screen.getByText('Mobile Number')).toBeInTheDocument();
    expect(screen.getByText('Required for account access and verification')).toBeInTheDocument();
    expect(screen.getByTestId('phone-icon')).toBeInTheDocument();
  });

  it('displays correct field info for address', () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams('missing=address'));
    render(<ProfileRequirementDialog hasCompleteAddress={true} />);
    expect(screen.getByText('Address Information')).toBeInTheDocument();
    expect(screen.getByText('Required for location-based services')).toBeInTheDocument();
    expect(screen.getByTestId('map-pin-icon')).toBeInTheDocument();
  });

  it('displays correct field info for unknown field', () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams('missing=unknownField'));
    render(<ProfileRequirementDialog hasCompleteAddress={true} />);
    expect(screen.getByText('unknownField')).toBeInTheDocument();
    expect(screen.getByText('Required information')).toBeInTheDocument();
    expect(screen.getAllByTestId('alert-circle-icon')).toHaveLength(2); // One in header, one for field
  });
});
