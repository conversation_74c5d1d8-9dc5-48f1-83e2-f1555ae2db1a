import React from 'react';
import { render, waitFor, fireEvent } from '@testing-library/react-native';
import CustomerProfileScreen from '@/app/(dashboard)/customer/profile';
import { useAuth } from '@/src/contexts/AuthContext';
import { useRouter } from 'expo-router';
import { getCustomerMetrics } from '@/backend/supabase/services/common/metricsService';
import { getCustomerProfile } from '@/backend/supabase/services/common/profileService';

// Mock external modules
jest.mock('@/src/contexts/AuthContext');
jest.mock('expo-router');
jest.mock('@/backend/supabase/services/common/metricsService');
jest.mock('@/backend/supabase/services/common/profileService');
jest.mock('@/styles/dashboard/customer/profile-styles', () => ({
  createCustomerProfileStyles: jest.fn(() => ({
    container: {},
    profileContent: {},
    separator: {},
    profileMenuItem: {},
    menuItemText: {},
    themeSection: {},
    themeSectionHeader: {},
    themeSectionTitle: {},
    logoutButton: {},
    logoutButtonText: {},
    errorContainer: {},
  })),
}));
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: jest.fn(() => 'light'),
}));
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: jest.fn(() => ({})),
}));
jest.mock('@/src/components/shared/layout/DashboardLayout', () => ({
  DashboardLayout: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));
jest.mock('@/src/components/shared/ProfileHeader', () => ({
  ProfileHeader: () => <></>,
}));
jest.mock('@/src/components/ui/SkeletonLoader', () => ({
  ProfileSkeleton: () => <></>,
}));
jest.mock('@/src/components/ui/ErrorState', () => ({
  ErrorState: () => <></>,
}));
jest.mock('@/src/components/ui/ThemeToggleButton', () => ({
  ThemeToggleButton: () => <></>,
}));
jest.mock('@/lib/utils', () => ({
  formatIndianNumberShort: jest.fn((num) => num.toString()),
}));
jest.mock('@/src/components/ui/ComingSoonModal', () => ({
  __esModule: true,
  default: () => <></>,
}));
jest.mock('@/src/components/modals/customer/EditProfileModal', () => ({
  __esModule: true,
  default: () => <></>,
}));
jest.mock('@/src/components/modals/customer/LikesModal', () => ({
  __esModule: true,
  default: () => <></>,
}));
jest.mock('@/src/components/modals/customer/FollowingModal', () => ({
  __esModule: true,
  default: () => <></>,
}));
jest.mock('@/src/components/modals/customer/ReviewsModal', () => ({
  __esModule: true,
  default: () => <></>,
}));
jest.mock('@react-navigation/native', () => ({
  useFocusEffect: jest.fn(),
}));


describe('CustomerProfileScreen', () => {
  const mockUser = { id: '123', user_metadata: { name: 'Test User' }, email: '<EMAIL>' };
  const mockProfileStatus = { roleStatus: { hasCustomerProfile: true } };
  const mockCustomerProfile = { name: 'Test User', avatar_url: 'http://example.com/avatar.png' };
  const mockMetrics = { reviewCount: 10, subscriptionCount: 5, likesCount: 20 };

  beforeEach(() => {
    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
      profileStatus: mockProfileStatus,
      signOut: jest.fn(),
    });
    (useRouter as jest.Mock).mockReturnValue({
      push: jest.fn(),
    });
    (getCustomerMetrics as jest.Mock).mockResolvedValue({ success: true, data: mockMetrics });
    (getCustomerProfile as jest.Mock).mockResolvedValue({ success: true, data: mockCustomerProfile });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly when data is loaded', async () => {
    const { getByText } = render(<CustomerProfileScreen />);

    await waitFor(() => {
      expect(getByText('Edit Profile')).toBeDefined();
      expect(getByText('My Likes')).toBeDefined();
      expect(getByText('Following')).toBeDefined();
      expect(getByText('My Reviews')).toBeDefined();
      expect(getByText('App Theme')).toBeDefined();
      expect(getByText('Logout')).toBeDefined();
    });
  });

  it('shows loading skeleton initially', () => {
    (getCustomerMetrics as jest.Mock).mockReturnValue(new Promise(() => {})); // Never resolve
    (getCustomerProfile as jest.Mock).mockReturnValue(new Promise(() => {})); // Never resolve

    const { queryByText } = render(<CustomerProfileScreen />);
    expect(queryByText('Edit Profile')).toBeNull(); // Should not be visible yet
  });

  it('displays error state if profile loading fails', async () => {
    (getCustomerProfile as jest.Mock).mockResolvedValue({ success: false, error: 'Profile error' });
    const { findByText } = render(<CustomerProfileScreen />);
    await findByText('Unable to load profile');
  });

  it('displays error state if metrics loading fails', async () => {
    (getCustomerMetrics as jest.Mock).mockResolvedValue({ success: false, error: 'Metrics error' });
    const { findByText } = render(<CustomerProfileScreen />);
    await findByText('Unable to load data'); // Generic error message from ErrorState
  });

  it('opens Edit Profile modal when "Edit Profile" is pressed', async () => {
    const { getByText } = render(<CustomerProfileScreen />);
    await waitFor(() => expect(getByText('Edit Profile')).toBeDefined());
    fireEvent.press(getByText('Edit Profile'));
    // In a real test, you'd assert that the modal component is visible.
    // For this placeholder, we're just simulating the press.
  });

  it('calls signOut when Logout is pressed', async () => {
    const signOutMock = jest.fn().mockResolvedValue({ error: null });
    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
      profileStatus: mockProfileStatus,
      signOut: signOutMock,
    });

    const { getByText } = render(<CustomerProfileScreen />);
    await waitFor(() => expect(getByText('Logout')).toBeDefined());
    fireEvent.press(getByText('Logout'));
    await waitFor(() => expect(signOutMock).toHaveBeenCalledTimes(1));
  });

  // Add more tests for:
  // - Navigation to other screens (Likes, Following, Reviews)
  // - Refresh control functionality
  // - Theme toggle interaction
  // - Coming soon modal interactions
  // - Edge cases for data loading (empty profile, zero metrics)
});