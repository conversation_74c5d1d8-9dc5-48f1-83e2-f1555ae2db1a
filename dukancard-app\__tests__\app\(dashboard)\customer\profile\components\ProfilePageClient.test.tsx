import React from 'react';
import { render, waitFor, fireEvent } from '@testing-library/react-native';
import ProfilePageClient from '../../../../../../app/(dashboard)/customer/profile/components/ProfilePageClient';
import ProfileForm from '@/src/components/profile/ProfileForm';
import AvatarUpload from '../../../../../../app/(dashboard)/customer/profile/components/AvatarUpload';
import AddressForm from '../../../../../../app/(dashboard)/customer/profile/components/AddressForm';
import ProfileRequirementDialog from '../../../../../../app/(dashboard)/customer/profile/components/ProfileRequirementDialog';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useTheme } from '@/src/hooks/useTheme';
import { profileStyles } from '@/styles/dashboard/customer/profile';

// Mock external modules and components
jest.mock('@/src/components/profile/ProfileForm');
jest.mock('../../../../../../app/(dashboard)/customer/profile/components/AvatarUpload');
jest.mock('../../../../../../app/(dashboard)/customer/profile/components/AddressForm');
jest.mock('../../../../../../app/(dashboard)/customer/profile/components/ProfileRequirementDialog');
jest.mock('@/src/hooks/useColorScheme');
jest.mock('@/src/hooks/useTheme');
jest.mock('@/styles/dashboard/customer/profile', () => ({
  profileStyles: jest.fn(() => ({
    container: {},
    card: {},
    cardHeader: {},
    iconContainer: {},
    headerTextContainer: {},
    cardTitle: {},
    cardSubtitle: {},
    avatarContainer: {},
    formContainer: {},
  })),
}));
jest.mock('lucide-react-native', () => ({
  User: 'User',
}));
jest.mock('@/lib/theme/colors', () => ({
  responsiveFontSize: jest.fn((size) => size),
}));

describe('ProfilePageClient', () => {
  const mockTheme = {
    spacing: { md: 16, xl: 24 },
    typography: { fontSize: { sm: 12, base: 16, lg: 20 } },
    colors: { textPrimary: '#000', textSecondary: '#666', primary: '#D4AF37', card: '#fff' },
  };

  beforeEach(() => {
    (useColorScheme as jest.Mock).mockReturnValue('light');
    (useTheme as jest.Mock).mockReturnValue(mockTheme);
    (ProfileForm as jest.Mock).mockReturnValue(<></>);
    (AvatarUpload as jest.Mock).mockReturnValue(<></>);
    (AddressForm as jest.Mock).mockReturnValue(<></>);
    (ProfileRequirementDialog as jest.Mock).mockReturnValue(<></>);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with initial data', () => {
    const initialProps = {
      initialName: 'John Doe',
      initialAvatarUrl: 'http://example.com/avatar.png',
      initialAddressData: {
        address: '123 Main St',
        pincode: '123456',
      },
      hasCompleteAddress: true,
    };
    const { getByText } = render(<ProfilePageClient {...initialProps} />);

    expect(getByText('Profile Information')).toBeDefined();
    expect(getByText('Update your personal details')).toBeDefined();

    // Verify that child components receive correct props
    expect(ProfileRequirementDialog).toHaveBeenCalledWith(
      expect.objectContaining({
        hasCompleteAddress: true,
      }),
      {}
    );
    expect(AvatarUpload).toHaveBeenCalledWith(
      expect.objectContaining({
        initialAvatarUrl: 'http://example.com/avatar.png',
        userName: 'John Doe',
      }),
      {}
    );
    expect(ProfileForm).toHaveBeenCalledWith(
      expect.objectContaining({
        initialName: 'John Doe',
      }),
      {}
    );
    expect(AddressForm).toHaveBeenCalledWith(
      expect.objectContaining({
        initialData: {
          address: '123 Main St',
          pincode: '123456',
        },
      }),
      {}
    );
  });

  it('updates avatarUrl state when onUpdateAvatar is called', () => {
    let avatarUploadProps: any;
    (AvatarUpload as jest.Mock).mockImplementation((props) => {
      avatarUploadProps = props;
      return <></>;
    });

    const { rerender } = render(
      <ProfilePageClient initialName="Test" />
    );

    // Simulate onUpdateAvatar being called by the AvatarUpload component
    if (avatarUploadProps && avatarUploadProps.onUpdateAvatar) {
      avatarUploadProps.onUpdateAvatar('http://example.com/new-avatar.png');
    }

    rerender(<ProfilePageClient initialName="Test" initialAvatarUrl="http://example.com/new-avatar.png" />);

    expect(AvatarUpload).toHaveBeenCalledWith(
      expect.objectContaining({
        initialAvatarUrl: 'http://example.com/new-avatar.png',
      }),
      {}
    );
  });
});
