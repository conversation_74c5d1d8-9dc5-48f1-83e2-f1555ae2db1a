/**
 * ResultsList component for React Native Discovery Screen
 * Based on dukancard-app/src/components/feed/UnifiedFeedList.tsx
 */

import React, { useCallback, useRef } from "react";
import {
  View,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Text,
  ListRenderItem,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import {
  BusinessCardData,
  NearbyProduct,
  ViewType,
} from "@/src/types/discovery";
import { Tables } from "@dukancard-types/supabase";
import { BusinessCard } from "./BusinessCard";
import { ProductCard } from "@/src/components/shared/ui/ProductCard";
import { EmptyState } from "./ErrorComponents";
import { BusinessCardSkeleton } from "./DiscoverySkeletons";
import { ProductCardSkeleton } from "@/src/components/ui/ProductSkeleton";
import { createResultsListStyles } from "./styles/ResultsListStyles";

interface ResultsListProps {
  viewType: ViewType;
  businesses: BusinessCardData[];
  products: NearbyProduct[];
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
  onRefresh: () => void;
  onBusinessPress: (business: BusinessCardData) => void;
  onProductPress: (product: NearbyProduct) => void;
  refreshing?: boolean;
  error?: string | null;
  onScroll?: (event: any) => void;
  scrollEventThrottle?: number;
}

export const ResultsList: React.FC<ResultsListProps> = ({
  viewType,
  businesses,
  products,
  isLoading,
  isLoadingMore,
  hasMore,
  onLoadMore,
  onRefresh,
  onBusinessPress,
  onProductPress,
  refreshing = false,
  error,
  onScroll,
  scrollEventThrottle = 16,
}) => {
  const { colors } = useTheme();
  const flatListRef = useRef<FlatList>(null);
  const styles = createResultsListStyles(colors);

  // Determine data source based on view type
  const data = viewType === "cards" ? businesses : products;
  const isEmpty = data.length === 0;

  // Handle end reached for infinite scroll
  const handleEndReached = useCallback(() => {
    if (!isLoading && !isLoadingMore && hasMore && data.length > 0) {
      onLoadMore();
    }
  }, [isLoading, isLoadingMore, hasMore, data.length, onLoadMore]);

  // Render business card item
  const renderBusinessItem: ListRenderItem<BusinessCardData> = useCallback(
    ({ item }) => (
      <BusinessCard
        business={item}
        onPress={onBusinessPress}
        showDistance={true}
      />
    ),
    [onBusinessPress]
  );

  // Render product item
  const renderProductItem: ListRenderItem<NearbyProduct> = useCallback(
    ({ item }) => {
      // Convert NearbyProduct to ProductsServices format for ProductCard
      const productData: Tables<'products_services'> = {
        id: item.id || "",
        slug: item.slug || null,
        business_id: item.business_id || "",
        product_type: item.product_type,
        name: item.name,
        description: item.description || null,
        base_price: item.base_price,
        discounted_price: item.discounted_price || null,
        image_url: item.image_url || null,
        is_available: item.is_available,
        created_at: item.created_at || new Date().toISOString(),
        updated_at: item.updated_at || new Date().toISOString(),
        images: item.image_url ? [item.image_url] : null,
        featured_image_index: item.image_url ? 0 : null,
      };

      return (
        <View style={styles.productContainer}>
          <ProductCard
            product={productData}
            businessLatitude={item.businessLatitude}
            businessLongitude={item.businessLongitude}
            showDistance={true}
            isClickable={true}
            variant="default"
          />
        </View>
      );
    },
    [styles.productContainer]
  );

  // Render item based on view type
  const renderItem =
    viewType === "cards" ? renderBusinessItem : renderProductItem;

  // Render loading skeleton based on view type
  const renderLoadingSkeleton = () => (
    <View style={styles.footerLoader}>
      {viewType === "cards" ? (
        // Show business card skeletons
        <View>
          {Array.from({ length: 5 }).map((_, index) => (
            <BusinessCardSkeleton key={index} />
          ))}
        </View>
      ) : (
        // Show product grid skeletons
        <View>
          {Array.from({ length: 3 }).map((_, rowIndex) => (
            <View key={rowIndex} style={styles.productSkeletonRow}>
              <View style={styles.productSkeletonItem}>
                <ProductCardSkeleton />
              </View>
              <View style={styles.productSkeletonItem}>
                <ProductCardSkeleton />
              </View>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  // Render footer loading indicator with skeleton items
  const renderFooter = () => {
    if (!isLoadingMore) return null;

    return (
      <View style={styles.footerLoader}>
        {viewType === "cards" ? (
          // Show 2 business card skeletons for loading more
          <View>
            <BusinessCardSkeleton />
            <BusinessCardSkeleton />
          </View>
        ) : (
          // Show 2 rows of product skeletons (4 products total) for loading more
          <View>
            <View style={styles.productSkeletonRow}>
              <View style={styles.productSkeletonItem}>
                <ProductCardSkeleton />
              </View>
              <View style={styles.productSkeletonItem}>
                <ProductCardSkeleton />
              </View>
            </View>
            <View style={styles.productSkeletonRow}>
              <View style={styles.productSkeletonItem}>
                <ProductCardSkeleton />
              </View>
              <View style={styles.productSkeletonItem}>
                <ProductCardSkeleton />
              </View>
            </View>
          </View>
        )}
      </View>
    );
  };

  // Render empty state
  const renderEmptyState = () => {
    if (isLoading) {
      return renderLoadingSkeleton();
    }

    if (error) {
      return (
        <EmptyState
          title="Search Error"
          description={error}
          icon="alert-circle"
          actionText="Try Again"
          onAction={onRefresh}
        />
      );
    }

    return (
      <EmptyState
        title={`No ${viewType === "cards" ? "Businesses" : "Products"} Found`}
        description={`We couldn't find any ${
          viewType === "cards" ? "businesses" : "products"
        } matching your search criteria. Try adjusting your filters or search terms.`}
        icon="search"
        actionText="Refresh"
        onAction={onRefresh}
      />
    );
  };

  return (
    <View style={styles.container}>
      {viewType === "cards" ? (
        <FlatList<BusinessCardData>
          key="business-cards-list" // Force re-render when switching view types
          ref={flatListRef}
          data={businesses}
          renderItem={renderBusinessItem}
          keyExtractor={(item, index) => item.id || index.toString()}
          contentContainerStyle={[
            styles.listContent,
            isEmpty && styles.emptyListContent,
          ]}
          showsVerticalScrollIndicator={false}
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          updateCellsBatchingPeriod={50}
          initialNumToRender={10}
          windowSize={10}
          getItemLayout={(data, index) => ({
            length: 200, // Approximate height of business card
            offset: 200 * index,
            index,
          })}
        />
      ) : (
        <FlatList<NearbyProduct>
          key="products-grid-list" // Force re-render when switching view types
          ref={flatListRef}
          data={products}
          renderItem={renderProductItem}
          keyExtractor={(item, index) => item.id || index.toString()}
          numColumns={2}
          contentContainerStyle={[
            styles.listContent,
            isEmpty && styles.emptyListContent,
          ]}
          showsVerticalScrollIndicator={false}
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.1}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          updateCellsBatchingPeriod={50}
          initialNumToRender={10}
          windowSize={10}
        />
      )}
    </View>
  );
};
