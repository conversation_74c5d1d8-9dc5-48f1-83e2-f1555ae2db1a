"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { addProductFormSchema, ProductServiceData } from "./schemas";
import { handleBaseProductImageUpload } from "./imageHandlers";


// Add a new product/service
export async function addProductService(
  formData: FormData
): Promise<{ success: boolean; error?: string; data?: ProductServiceData }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  // Define a more specific type for form values before validation
  type RawFormValues = Record<
    string,
    string | File | undefined | boolean | number | null | string[]
  >; // Allow string[] for categories/tags
  const formValues: RawFormValues = {};
  const productImages: (File | null)[] = [];
  let featuredImageIndex = 0;

  // Parse form data
  for (const [key, value] of formData.entries()) {
    if (key.startsWith("productImage_")) {
      // Multi-image upload format: productImage_0, productImage_1, etc.
      const index = parseInt(key.split('_')[1], 10);
      if (value instanceof File && value.size > 0) {
        // Ensure array has enough slots
        while (productImages.length <= index) {
          productImages.push(null);
        }
        productImages[index] = value;
      }
    } else if (key === "featuredImageIndex") {
      featuredImageIndex = parseInt(value as string, 10) || 0;
    } else {
      formValues[key] = value;
    }
  }

  // categories/tags assignment removed

  // Coerce numeric/boolean fields (simplified)
  if (formValues.product_type && typeof formValues.product_type !== "string") {
    // Handle potential non-string values if necessary, though enum should handle it
    formValues.product_type = String(formValues.product_type);
  }
  if (formValues.base_price)
    formValues.base_price = Number(formValues.base_price);
  // offering_price removed
  if (formValues.discounted_price)
    // Coerce discounted_price
    formValues.discounted_price = Number(formValues.discounted_price);
  // inventory_count removed
  // display_order removed
  formValues.is_available =
    formValues.is_available === "true" || formValues.is_available === "on";

  // Validate extracted form values (simplified schema)
  const validatedFields = addProductFormSchema.safeParse(formValues);
  if (!validatedFields.success) {
    console.error(
      "Add Product Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    const errors = validatedFields.error.flatten().fieldErrors;
    // Safely join error messages
    const errorMessages = Object.entries(errors)
      .map(
        ([field, messages]) =>
          `${field}: ${
            Array.isArray(messages) ? messages.join(", ") : messages
          }`
      )
      .join("; ");
    return { success: false, error: `Invalid data: ${errorMessages}` };
  }

  // Note: Product limit enforcement is now handled by database triggers
  // The check_product_limit() trigger function will automatically enforce limits
  // when products are inserted or updated

  // Prepare data for insertion (simplified)
  const dataToInsert = {
    ...validatedFields.data,
    business_id: user.id,
    image_url: null, // Start with null image_url
  };

  // 1. Insert product
  const { data: insertedProduct, error: insertError } = await supabase
    .from("products_services")
    .insert(dataToInsert)
    .select(
      "id, product_type, name, description, base_price, discounted_price, is_available, image_url, images, featured_image_index, created_at, updated_at, slug"
    ) // Added images, featured_image_index to the select
    .single();
  if (insertError || !insertedProduct) {
    // Check if this is a product limit error from the database trigger
    if (insertError?.message?.includes('Cannot make product available') &&
        insertError?.message?.includes('reached the limit')) {
      return {
        success: false,
        error: insertError.message, // Use the database error message directly as it's user-friendly
      };
    }
    return {
      success: false,
      error: `Failed to add product/service: ${insertError?.message}`,
    };
  }

  const productId = insertedProduct.id;
  let finalImageUrl: string | null = insertedProduct.image_url; // Use initial value
  let finalImages: (string | null)[] = insertedProduct.images || []; // Initialize with existing images or empty array

  // 2. Handle Image Upload
  if (productImages.length > 0) {
    console.log('Starting image upload with:', {
      userId: user.id,
      userIdType: typeof user.id,
      productId,
      productIdType: typeof productId,
      imageCount: productImages.length
    });

    // Use the base product image upload function
    const uploadResult = await handleBaseProductImageUpload(
      user.id,
      productId,
      productImages
    );

    if (uploadResult.error) {
      console.error(`Image upload failed: ${uploadResult.error}`);
      return {
        success: false,
        error: `Product created, but image upload failed: ${uploadResult.error}`,
      };
    }

    finalImages = uploadResult.urls;

    // Set the featured image URL as the main image_url
    if (finalImages.length > 0) {
      const featuredIdx = Math.min(featuredImageIndex, finalImages.length - 1);
      finalImageUrl = finalImages[featuredIdx];

      // Ensure images is properly formatted as an array for PostgreSQL JSONB column
      const updateData = {
        image_url: finalImageUrl,
        images: Array.isArray(finalImages) ? finalImages.filter((image): image is string => image !== null) : [],
        featured_image_index: featuredIdx
      };

      const { data: _updateResult, error: updateImageError } = await supabase
        .from("products_services")
        .update(updateData)
        .eq("id", productId)
        .select("id, image_url, images, featured_image_index");

      if (updateImageError) {
        console.error(`Failed to update product with image URLs: ${updateImageError.message}`);
        return {
          success: false,
          error: `Product created, images uploaded, but failed to save URLs: ${updateImageError.message}`,
        };
      }

    }
  }

  // 3. Handle Categories - Removed
  // 4. Handle Tags - Removed

  revalidatePath("/dashboard/business/products");

  // Get the latest product data to ensure we have the most up-to-date values
  const { data: latestProduct, error: fetchError } = await supabase
    .from("products_services")
    .select("id, product_type, name, description, base_price, discounted_price, is_available, image_url, images, featured_image_index, created_at, updated_at, slug")
    .eq("id", productId)
    .single();

  if (fetchError) {
    console.error(`Failed to fetch latest product data: ${fetchError.message}`);
    // Continue with what we have, don't fail the operation
  }

  const returnData = {
    ...(latestProduct || insertedProduct),
    // If we couldn't fetch the latest data, use our tracked values
    image_url: latestProduct?.image_url || finalImageUrl,
    images: latestProduct?.images || (Array.isArray(finalImages) ? finalImages.filter((image): image is string => image !== null) : []),
    featured_image_index: latestProduct?.featured_image_index || featuredImageIndex,
    created_at: latestProduct?.created_at ? new Date(latestProduct.created_at) : new Date(insertedProduct.created_at),
    updated_at: latestProduct?.updated_at ? new Date(latestProduct.updated_at) : new Date(insertedProduct.updated_at),
    slug: latestProduct?.slug || insertedProduct.slug,
  };

  return { success: true, data: returnData as ProductServiceData };
}