import React from 'react';
import { render } from '@testing-library/react-native';
import TabLayout from '@/app/(tabs)/_layout';
import { Tabs } from 'expo-router';
import { useTheme } from '@/src/hooks/useTheme';
import { Text } from 'react-native';

// Mock external modules and components
jest.mock('expo-router', () => ({
  Tabs: ({ children, screenOptions }: any) => (
    <React.Fragment>
      <Text testID="tabs-screen-options">{JSON.stringify(screenOptions)}</Text>
      {children}
    </React.Fragment>
  ),
  Stack: {
    Screen: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  },
}));
jest.mock('@/src/components/HapticTab', () => ({
  HapticTab: 'HapticTab',
}));
jest.mock('@/src/components/ui/IconSymbol', () => ({
  IconSymbol: ({ name, size, color }: any) => (
    <Text testID={`icon-symbol-${name}`} style={{ fontSize: size, color }}>{name}</Text>
  ),
}));
jest.mock('@/src/components/ui/TabBarBackground', () => ({
  __esModule: true,
  default: 'TabBarBackground',
}));
jest.mock('@/src/hooks/useTheme');
jest.mock('@/lib/theme/colors', () => ({
  responsiveFontSize: jest.fn((size) => size),
}));

describe('TabLayout', () => {
  const mockTheme = {
    colors: {
      tabIconSelected: 'blue',
      iconMuted: 'gray',
    },
    borderRadius: { md: 8 },
  };

  beforeEach(() => {
    (useTheme as jest.Mock).mockReturnValue(mockTheme);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders Tabs with correct screenOptions', () => {
    const { getByTestId } = render(<TabLayout />);
    const screenOptionsText = getByTestId('tabs-screen-options').props.children;
    const screenOptions = JSON.parse(screenOptionsText);

    expect(screenOptions.tabBarActiveTintColor).toBe(mockTheme.colors.tabIconSelected);
    expect(screenOptions.headerShown).toBe(false);
    expect(screenOptions.tabBarButton).toBe('HapticTab');
    expect(screenOptions.tabBarBackground).toBe('TabBarBackground');
    // Platform specific styles are harder to test directly without mocking Platform.select
    // For now, we'll assume the mock works as expected.
  });

  it('renders Tabs.Screen for Home tab with correct options', () => {
    const { getByText, getByTestId } = render(<TabLayout />);

    // Mocking Tabs.Screen to render its children directly, so we look for the title and icon
    expect(getByText('Home')).toBeDefined();
    expect(getByTestId('icon-symbol-house.fill')).toBeDefined();
    expect(getByTestId('icon-symbol-house.fill').props.style.color).toBe(undefined); // Color is passed to IconSymbol, not directly rendered by ThemedText
  });

  it('renders Tabs.Screen for Explore tab with correct options', () => {
    const { getByText, getByTestId } = render(<TabLayout />);

    expect(getByText('Explore')).toBeDefined();
    expect(getByTestId('icon-symbol-paperplane.fill')).toBeDefined();
    expect(getByTestId('icon-symbol-paperplane.fill').props.style.color).toBe(undefined); // Color is passed to IconSymbol, not directly rendered by ThemedText
  });
});
