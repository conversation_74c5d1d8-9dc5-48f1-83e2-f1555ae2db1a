import { DashboardLayout } from '@/src/components/shared/layout/DashboardLayout';
import React, { useState, useEffect, useCallback } from 'react';
import {
  Text,
  View,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Alert
} from 'react-native';
import { createFavoritesStyles } from '@/styles/dashboard/customer/favorites-styles';
import { useTheme } from '@/src/hooks/useTheme';
import { supabase } from '@/lib/supabase';
import { LikeCard } from '@/src/components/social/LikeCard';
import { SearchComponent } from '@/src/components/social/SearchComponent';
import { EmptyState } from '@/src/components/shared/ui/EmptyState';
import { LoadingSpinner } from '@/src/components/shared/ui/LoadingSpinner';
import { LikeListSkeleton } from '@/src/components/social/SkeletonLoaders';
import { likesService } from '@/backend/supabase/services/posts/socialService';

interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  locality: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface LikeWithProfile {
  id: string;
  business_profiles: BusinessProfileData | null;
}

export default function CustomerLikesScreen() {
  const theme = useTheme();
  const styles = createFavoritesStyles(theme);
  const [likes, setLikes] = useState<LikeWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [user, setUser] = useState<any>(null);

  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user) {
        Alert.alert('Error', 'Please log in to view your liked businesses');
        return;
      }
      setUser(user);
    };
    getCurrentUser();
  }, []);

  // Fetch likes
  const fetchLikes = useCallback(async (page: number = 1, search: string = '', isRefresh: boolean = false) => {
    if (!user) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const result = await likesService.fetchLikes(user.id, page, 10, search);

      if (page === 1 || isRefresh) {
        setLikes(result.items);
      } else {
        setLikes(prev => [...prev, ...result.items]);
      }

      setTotalCount(result.totalCount);
      setHasMore(result.hasMore);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching likes:', error);
      Alert.alert('Error', 'Failed to load liked businesses. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  }, [user]);

  // Initial load
  useEffect(() => {
    if (user) {
      fetchLikes(1, searchTerm);
    }
  }, [user, fetchLikes, searchTerm]);

  // Handle search
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
    fetchLikes(1, term);
  }, [fetchLikes]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    setCurrentPage(1);
    fetchLikes(1, searchTerm, true);
  }, [fetchLikes, searchTerm]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMore) {
      fetchLikes(currentPage + 1, searchTerm);
    }
  }, [loadingMore, hasMore, currentPage, searchTerm, fetchLikes]);

  // Handle unlike
  const handleUnlike = useCallback(async (likeId: string) => {
    try {
      await likesService.unlike(likeId);
      setLikes(prev => prev.filter(like => like.id !== likeId));
      setTotalCount(prev => prev - 1);
      Alert.alert('Success', 'Successfully removed from liked businesses');
    } catch (error) {
      console.error('Error unliking:', error);
      Alert.alert('Error', 'Failed to unlike business. Please try again.');
    }
  }, []);

  // Render like item
  const renderLike = ({ item }: { item: LikeWithProfile }) => (
    <LikeCard
      like={item}
      onUnlike={handleUnlike}
    />
  );

  // Render footer
  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color="#D4AF37" />
      </View>
    );
  };

  if (loading) {
    return (
      <DashboardLayout
        userName={user?.user_metadata?.name || "User"}
        showNotifications={true}
      >
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>❤️ Liked Businesses</Text>
            <Text style={styles.subtitle}>Loading your liked businesses...</Text>
          </View>
          <View style={styles.listContainer}>
            <LikeListSkeleton count={5} />
          </View>
        </View>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      userName={user?.user_metadata?.name || "User"}
      showNotifications={true}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>❤️ Liked Businesses</Text>
          <Text style={styles.subtitle}>
            {totalCount} {totalCount === 1 ? 'business' : 'businesses'} you&apos;ve liked
          </Text>
        </View>

        <SearchComponent
          value={searchTerm}
          onChangeText={handleSearch}
          placeholder="Search liked businesses..."
        />

        {likes.length === 0 ? (
          <EmptyState
            title="No liked businesses yet"
            description="You haven&apos;t liked any businesses yet. Discover businesses and like them to see them here."
            actionText="Discover Businesses"
            onAction={() => {/* Navigate to discover */}}
            icon="heart-outline"
          />
        ) : (
          <FlatList
            data={likes}
            renderItem={renderLike}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={['#D4AF37']}
                tintColor="#D4AF37"
              />
            }
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.1}
            ListFooterComponent={renderFooter}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
    </DashboardLayout>
  );
}


