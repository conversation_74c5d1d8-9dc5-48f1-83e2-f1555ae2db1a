# DukanCard Web Application

## Overview

DukanCard is a live, production web application built with Next.js, designed as a comprehensive platform for **digital business card creation, local business discovery, and community engagement**. It serves as the web counterpart to the `dukancard-app` mobile application, sharing a common backend to ensure consistent functionality across platforms.

## Purpose

The primary purpose of DukanCard is to empower **local businesses** to establish a strong online presence through customizable digital business cards that act as mini-websites or storefronts. Concurrently, it enables **customers** to easily discover local businesses, products, and services, fostering community connections and supporting local commerce. The platform facilitates dynamic content sharing, engagement through likes and reviews, and offers tiered subscription plans for enhanced business features.

## Key Technologies

This application is built on a modern web stack, leveraging the power of **Next.js** for server-side rendering and static site generation, **React** for dynamic user interfaces, and **TypeScript** for enhanced code quality and maintainability. Styling is handled efficiently with **Tailwind CSS**.

For backend services, DukanCard integrates with **Supabase**, which provides a comprehensive suite of tools including authentication, PostgreSQL database management, and real-time capabilities. Payment processing is handled via **Razorpay**, enabling secure subscription management and transaction history.

## Project Structure

The project follows a well-organized structure, making it easy for new developers to navigate and understand the codebase. Key areas include dedicated directories for UI components, custom hooks, utility functions, and comprehensive testing setups for both unit and end-to-end testing. API endpoints are clearly separated for administrative, business, and customer functionalities.

## Getting Started

To get the DukanCard web application up and running on your local machine, please refer to the `development-workflow.md` document in the `docs` directory. This document will guide you through the necessary steps for setting up your development environment, running tests, and understanding the deployment process.

### Supabase Type Generation

To ensure your local environment has the latest Supabase TypeScript types, run the following command from the project root:

```bash
npm run gen-types
```

This command generates `types/supabase.ts` based on your Supabase schema.

## Project Knowledge Base

For comprehensive and up-to-date information on the Dukancard project, including product features, technical architecture, development guides, and user flows, please refer to the centralized Project Knowledge Base located in the `knowledge_base` directory:

`dukancard/knowledge_base/`

This knowledge base serves as the single source of truth for the entire project. We encourage all team members to familiarize themselves with its contents to gain a comprehensive understanding of the Dukancard web application.