import { createClient } from "@/utils/supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";
import { getPostFolderPath } from "@/lib/utils/storage-paths";
import { BUCKETS } from "../supabase/constants";

/**
 * Delete entire customer post folder and all its contents
 * This removes all files in the customer post folder, effectively deleting the folder
 */
export async function deleteCustomerPostMedia(
  userId: string,
  postId: string,
  createdAt: string
): Promise<{ success: boolean; error?: string }> {
  // Use admin client for storage operations to bypass RLS
  const adminSupabase = await createClient() as SupabaseClient<Database>;

  try {
    const bucketName = BUCKETS.CUSTOMERS;
    const postFolderPath = getPostFolderPath(userId, postId, createdAt);

    // List all files in the post folder
    const { data: files, error: listError } = await adminSupabase.storage
      .from(bucketName)
      .list(postFolderPath, {
        limit: 1000, // Set a reasonable limit for safety
        sortBy: { column: 'name', order: 'asc' }
      });

    if (listError) {
      console.error("Error listing customer post folder contents:", listError);
      return {
        success: false,
        error: `Failed to list customer post folder: ${listError.message}`,
      };
    }

    if (!files || files.length === 0) {
      // No files to delete, consider it successful
      return { success: true };
    }

    // Create full paths for all files in the folder
    const filePaths = files.map(file => `${postFolderPath}/${file.name}`);

    // Delete all files in the post folder using admin client
    // In object storage, deleting all files effectively removes the folder
    const { error: deleteError } = await adminSupabase.storage
      .from(bucketName)
      .remove(filePaths);

    if (deleteError) {
      console.error("Error deleting customer post folder contents:", deleteError);
      return {
        success: false,
        error: `Failed to delete customer post folder: ${deleteError.message}`,
      };
    }

    return { success: true };

  } catch (error) {
    console.error("Error in deleteCustomerPostMedia:", error);
    return {
      success: false,
      error: "An unexpected error occurred while deleting customer post folder."
    };
  }
}
