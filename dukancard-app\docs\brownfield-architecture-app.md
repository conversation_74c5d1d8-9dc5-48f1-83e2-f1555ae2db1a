# Dukancard-App Brownfield Architecture Document

## Introduction

This document captures the CURRENT STATE of the Dukancard-App codebase. It serves as a reference for AI agents working on enhancements and for creating a comprehensive test suite.

### Document Scope

Comprehensive documentation of the entire Dukancard React Native application.

### Change Log

| Date       | Version | Description                 | Author  |
| ---------- | ------- | --------------------------- | ------- |
| 2025-07-19 | 1.0     | Initial brownfield analysis | Mary (Analyst) |

## High Level Architecture

### Technical Summary

Dukancard-App is the mobile application version of the Dukancard platform, built with React Native and Expo. It allows users to interact with the Dukancard ecosystem on the go, providing access to features like viewing business cards, products, and the community feed.

### Actual Tech Stack (from package.json)

| Category | Technology | Version | Notes |
| --- | --- | --- | --- |
| Framework | React Native | 0.79.5 | |
| Toolkit | Expo | 53.0.19 | |
| Routing | Expo Router | ~5.1.3 | File-based routing for React Native |
| UI Components | React Native built-ins | | |
| Backend | Supabase | ^2.49.8 | Database, Auth, and other backend services |
| Forms | React Hook Form | ^7.59.0 | |
| Testing | Jest, Detox | ~29.7.0, ^20.40.0 | Unit and E2E testing |
| Navigation | React Navigation | ^7.1.6 | Bottom tabs navigation |

### Repository Structure Reality Check

- Type: Monorepo (contains `dukancard` and `dukancard-app`)
- Package Manager: npm
- Notable: This document focuses solely on the `dukancard-app` React Native application.

## Source Tree and Module Organization

### Project Structure (Actual)

```text
dukancard-app/
├── __tests__/         # Jest tests
├── app/               # Expo Router routes
│   ├── (auth)/        # Auth-related screens (login, signup)
│   ├── (dashboard)/   # Main application dashboard
│   ├── (onboarding)/  # User onboarding flow
│   ├── (tabs)/        # Main tab-based navigation
│   ├── business/      # Screens for viewing business details
│   ├── post/          # Screens for viewing post details
│   └── product/       # Screens for viewing product details
├── assets/            # Images, fonts, and other static assets
├── components/        # Reusable React Native components
├── lib/               # Core logic, services, and utilities
│   ├── actions/       # Server-side actions
│   ├── auth/          # Authentication-related logic
│   ├── schemas/       # Data validation schemas
│   └── ...
├── src/               # Source code
│   ├── components/    # Reusable components
│   ├── contexts/      # React Context providers
│   ├── hooks/         # Custom React hooks
│   └── services/      # Business logic
└── ...
```

### Key Modules and Their Purpose

*This section will be filled in as I analyze the code.*

## Data Models and APIs

*This section will be filled in as I analyze the code.*

## Technical Debt and Known Issues

1.  **Lack of Test Coverage:** Similar to the web app, the mobile app also has a lack of automated tests.
2.  **Potential for Inconsistent Patterns:** As with any rapidly developed project, there may be inconsistencies in coding patterns across different features.

## Integration Points and External Dependencies

| Service | Purpose | Integration Type | Key Files |
| --- | --- | --- | --- |
| Supabase | Backend Platform | SDK | `lib/supabase.ts` |
| Google Sign-In | Authentication | SDK | `@react-native-google-signin/google-signin` |

## Development and Deployment

### Local Development Setup

1.  Run `npm install` to install dependencies.
2.  Run `npm run android` or `npm run ios` to start the development server.

### Build and Deployment Process

- **Build Command**: `npm run build:secure-native`
- **Deployment**: Likely done through app stores (Google Play Store, Apple App Store).

## Testing Reality

### Current Test Coverage

- Unit/Integration Tests exist for some parts of the application.
- The primary testing framework is **Jest**.
- Tests are located in the `__tests__` directory, mirroring the project structure.
- End-to-end tests may exist using **Detox** (configured in `.detoxrc.js`).

### Testing Conventions (from `isolatedAuthErrorHandler.test.ts`)

- **Testing Hooks:** `@testing-library/react-hooks` is used to test custom hooks.
- **Mocking:** `jest.spyOn` is used to mock modules and hooks.
- **Structure:** Tests are organized with `describe` and `it` blocks.
- **Setup/Teardown:** `beforeEach` and `afterEach` are used for setting up and cleaning up spies and mocks.

---
