import { getSecureBusinessProfileBySlug, getSecureBusinessProfileWithProductsBySlug } from '@/lib/actions/businessProfiles/profileRetrieval';
import { createClient } from '@/utils/supabase/server';

// Mock the Supabase admin client
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));

describe('profileRetrieval', () => {
  let mockSupabase: any;
  let mockFrom: any;
  let mockSelect: any;
  let mockEq: any;
  let mockMaybeSingle: any;

  beforeEach(() => {
    mockMaybeSingle = jest.fn();
    mockEq = jest.fn(() => ({
      maybeSingle: mockMaybeSingle,
    }));
    mockSelect = jest.fn(() => ({
      eq: mockEq,
    }));
    mockFrom = jest.fn(() => ({
      select: mockSelect,
    }));
    mockSupabase = {
      from: mockFrom,
    };
    (createClient as jest.Mock).mockReturnValue(mockSupabase);

    // Suppress console.error for cleaner test output
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getSecureBusinessProfileBySlug', () => {
    it('should return an error if slug is empty', async () => {
      const result = await getSecureBusinessProfileBySlug('');
      expect(result).toEqual({ error: 'Business slug is required.' });
      expect(createClient).not.toHaveBeenCalled();
    });

    it('should return an error if Supabase query fails', async () => {
      mockMaybeSingle.mockResolvedValueOnce({ data: null, error: { message: 'DB Error' } });

      const result = await getSecureBusinessProfileBySlug('test-slug');
      expect(result).toEqual({ error: 'Failed to fetch business profile: DB Error' });
      expect(createClient).toHaveBeenCalled();
      expect(mockFrom).toHaveBeenCalledWith('business_profiles');
      expect(mockSelect).toHaveBeenCalled();
      expect(mockEq).toHaveBeenCalledWith('business_slug', 'test-slug');
    });

    it('should return an error if profile is not found', async () => {
      mockMaybeSingle.mockResolvedValueOnce({ data: null, error: null });

      const result = await getSecureBusinessProfileBySlug('non-existent-slug');
      expect(result).toEqual({ error: 'Profile not found.' });
    });

    it('should return the profile data with subscription status if successful', async () => {
      const mockProfileData = {
        id: '123',
        business_slug: 'test-slug',
        payment_subscriptions: { plan_id: 'pro', subscription_status: 'active' },
      };
      mockMaybeSingle.mockResolvedValueOnce({ data: mockProfileData, error: null });

      const result = await getSecureBusinessProfileBySlug('test-slug');
      expect(result).toEqual({
        data: {
          ...mockProfileData,
          subscription_status: 'active',
          plan_id: 'pro',
        },
      });
    });

    it('should return the profile data without subscription status if no subscription', async () => {
      const mockProfileData = {
        id: '123',
        business_slug: 'test-slug',
        payment_subscriptions: null,
      };
      mockMaybeSingle.mockResolvedValueOnce({ data: mockProfileData, error: null });

      const result = await getSecureBusinessProfileBySlug('test-slug');
      expect(result).toEqual({
        data: {
          ...mockProfileData,
          subscription_status: null,
          plan_id: null,
        },
      });
    });

    it('should handle unexpected errors during execution', async () => {
      mockMaybeSingle.mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });

      const result = await getSecureBusinessProfileBySlug('test-slug');
      expect(result).toEqual({ error: 'An unexpected error occurred.' });
    });
  });

  describe('getSecureBusinessProfileWithProductsBySlug', () => {
    it('should return an error if slug is empty', async () => {
      const result = await getSecureBusinessProfileWithProductsBySlug('');
      expect(result).toEqual({ error: 'Business slug is required.' });
      expect(createClient).not.toHaveBeenCalled();
    });

    it('should return an error if Supabase query fails', async () => {
      mockMaybeSingle.mockResolvedValueOnce({ data: null, error: { message: 'DB Error' } });

      const result = await getSecureBusinessProfileWithProductsBySlug('test-slug');
      expect(result).toEqual({ error: 'Failed to fetch business profile: DB Error' });
    });

    it('should return an error if profile is not found', async () => {
      mockMaybeSingle.mockResolvedValueOnce({ data: null, error: null });

      const result = await getSecureBusinessProfileWithProductsBySlug('non-existent-slug');
      expect(result).toEqual({ error: 'Profile not found.' });
    });

    it('should return the profile data with products if successful', async () => {
      const mockProfileData = {
        id: '123',
        business_slug: 'test-slug',
        products_services: [{ id: 'prod1', name: 'Product 1' }],
      };
      mockMaybeSingle.mockResolvedValueOnce({ data: mockProfileData, error: null });

      const result = await getSecureBusinessProfileWithProductsBySlug('test-slug');
      expect(result).toEqual({
        data: {
          ...mockProfileData,
          products_services: [{ id: 'prod1', name: 'Product 1' }],
        },
      });
    });

    it('should return the profile data with empty products array if no products', async () => {
      const mockProfileData = {
        id: '123',
        business_slug: 'test-slug',
        products_services: null,
      };
      mockMaybeSingle.mockResolvedValueOnce({ data: mockProfileData, error: null });

      const result = await getSecureBusinessProfileWithProductsBySlug('test-slug');
      expect(result).toEqual({
        data: {
          ...mockProfileData,
          products_services: [],
        },
      });
    });

    it('should handle unexpected errors during execution', async () => {
      mockMaybeSingle.mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });

      const result = await getSecureBusinessProfileWithProductsBySlug('test-slug');
      expect(result).toEqual({ error: 'An unexpected error occurred.' });
    });
  });
});
