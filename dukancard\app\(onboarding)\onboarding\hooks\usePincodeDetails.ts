"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { UseFormReturn } from "react-hook-form";
import { getPincodeDetails } from "@/lib/actions/location";

// Define the form data type for onboarding
interface OnboardingFormData {
  businessName: string;
  email: string;
  memberName: string;
  title: string;
  phone: string;
  businessCategory: string;
  businessSlug: string;
  addressLine: string;
  pincode: string;
  city: string;
  state: string;
  locality: string;
  businessStatus: "online" | "offline";
  planId: string;
}

interface UsePincodeDetailsOptions {
  form: UseFormReturn<OnboardingFormData>;
  initialPincode?: string;
  initialLocality?: string;
}

export function usePincodeDetails({
  form,
  initialPincode,
  initialLocality
}: UsePincodeDetailsOptions) {
  const [isPincodeLoading, setIsPincodeLoading] = useState(false);
  const [availableLocalities, setAvailableLocalities] = useState<string[]>([]);

  // Pincode change handler
  const handlePincodeChange = useCallback(async (pincode: string) => {
    if (pincode.length !== 6) return;

    setIsPincodeLoading(true);
    setAvailableLocalities([]);

    // Reset form fields
    form.setValue("locality", "");
    form.setValue("city", "");
    form.setValue("state", "");

    const result = await getPincodeDetails(pincode);
    setIsPincodeLoading(false);

    if (result.error) {
      toast.error(result.error);
    } else if (result.city && result.state && result.localities && result.localities.length > 0) {
      // Set city and state
      form.setValue("city", result.city, { shouldValidate: true });
      form.setValue("state", result.state, { shouldValidate: true });

      // Update localities
      setAvailableLocalities(result.localities);

      // If only one locality, auto-select it
      if (result.localities.length === 1) {
        form.setValue("locality", result.localities[0], {
          shouldValidate: true,
          shouldDirty: true
        });
      }

      toast.success("City and State auto-filled. Please select your locality.");
    }
  }, [form]);

  // Effect to fetch localities on initial load if pincode exists
  useEffect(() => {
    if (!initialPincode || initialPincode.length !== 6) return;

    const fetchAndValidateLocalities = async (pincode: string) => {
      setIsPincodeLoading(true);
      setAvailableLocalities([]);

      try {
        const result = await getPincodeDetails(pincode);

        if (result.error) {
          toast.error(`Failed to fetch details for pincode ${pincode}: ${result.error}`);
          setAvailableLocalities([]);
        } else if (result.city && result.state && result.localities && result.localities.length > 0) {
          // Set city/state
          form.setValue("city", result.city, { shouldValidate: true });
          form.setValue("state", result.state, { shouldValidate: true });
          setAvailableLocalities(result.localities);

          // Validate if the initial locality exists in the fetched localities
          if (initialLocality) {
            const localityExists = result.localities.some(
              (loc) => loc.toLowerCase() === initialLocality.toLowerCase()
            );

            if (localityExists) {
              // Set the locality if it exists
              form.setValue("locality", initialLocality, {
                shouldValidate: true,
                shouldDirty: true
              });
            } else {
              // Clear the locality if it doesn't exist for this pincode
              form.setValue("locality", "", {
                shouldValidate: true,
                shouldDirty: true
              });
              toast.warning(`The locality "${initialLocality}" is not available for pincode ${pincode}. Please select a valid locality.`);
            }
          }
        } else {
          setAvailableLocalities([]);
          toast.warning(`No localities found for pincode ${pincode}.`);
          if (initialLocality) {
            form.setValue("locality", "", {
              shouldValidate: true,
              shouldDirty: true
            });
          }
        }
      } catch (error) {
        console.error("Error fetching pincode details:", error);
        toast.error("An unexpected error occurred while fetching pincode details.");
        setAvailableLocalities([]);
        if (initialLocality) {
          form.setValue("locality", "", {
            shouldValidate: true,
            shouldDirty: true
          });
        }
      } finally {
        setIsPincodeLoading(false);
      }
    };

    fetchAndValidateLocalities(initialPincode);
  }, [initialPincode, initialLocality, form]);

  return {
    isPincodeLoading,
    availableLocalities,
    handlePincodeChange
  };
}
