import React from 'react';

export const InputOTP = ({ children, maxLength, value, onChange, className, ...props }: any) => (
  <div className={className} {...props}>
    <input
      type="text"
      maxLength={maxLength}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      aria-label="Enter verification code"
      data-testid="input-otp"
    />
    {children}
  </div>
);

export const InputOTPGroup = ({ children }: any) => (
  <div data-testid="input-otp-group">{children}</div>
);

export const InputOTPSlot = ({ index, className }: any) => (
  <div data-testid={`input-otp-slot-${index}`} className={className}>
    {index}
  </div>
);
