import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { useFormContext, Controller } from 'react-hook-form';
import Step2AddressLocation from '@/app/(auth)/components/Step2AddressLocation';

// Mock external modules and components
jest.mock('react-hook-form');
jest.mock('@/src/components/ui/Input', () => ({
  Input: 'Input',
}));
jest.mock('lucide-react-native', () => ({
  MapPin: 'MapPin',
  Building2: 'Building2',
  Loader2: 'Loader2',
}));
jest.mock('@/src/components/ui/LocationPicker', () => ({
  LocationPicker: 'LocationPicker',
}));
jest.mock('@/src/components/forms/FormPicker', () => ({
  FormPicker: 'FormPicker',
}));
jest.mock('@/src/components/forms/FormField', () => ({
  FormField: 'FormField',
}));
import { View, Text, TouchableOpacity, TextInput } from 'react-native';

jest.mock('@/src/components/pickers/LocalityBottomSheetPicker', () => {
  const React = require('react');
  const ReactNative = jest.requireActual('react-native');
  return {
    __esModule: true,
    default: React.forwardRef((props: any, ref: any) => React.createElement(ReactNative.View, { ref, ...props }, props.children)),
  };
});

describe('Step2AddressLocation', () => {
  const mockUseFormContext = {
    control: {},
    // Mock other methods if needed by the component
  };

  const mockLocalityPickerRef = { current: { present: jest.fn(), dismiss: jest.fn() } };
  const mockTheme = { colors: { textSecondary: '#000', primary: '#000' } };
  const mockStyles = {
    form: {},
    heroSection: {},
    heroTitle: {},
    heroSubtitle: {},
    formSections: {},
    locationPermissionMessage: {},
    locationPermissionMessageRow: {},
    locationPermissionMessageText: {},
    locationPermissionMessageSubText: {},
    dividerContainer: {},
    dividerLine: {},
    dividerText: {},
    formFieldContainer: {},
    cityStateRow: {},
    cityStateField: {},
  };
  const mockTextColor = '#000';
  const mockBorderColor = '#000';

  beforeEach(() => {
    (useFormContext as jest.Mock).mockReturnValue(mockUseFormContext);

    // Mock Controller to render its children directly
    (Controller as jest.Mock).mockImplementation(({ render }) =>
      render({
        field: { onChange: jest.fn(), onBlur: jest.fn(), value: '' },
        fieldState: { error: undefined },
      })
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with all elements', () => {
    const { getByText, getByPlaceholderText } = render(
      <Step2AddressLocation
        isPincodeLoading={false}
        availableLocalities={[]}
        locationPermission={{ granted: true }}
        hasGpsCoordinates={true}
        fetchPincodeDetails={jest.fn()}
        setGpsDetectedLocality={jest.fn()}
        handleLocationDetected={jest.fn()}
        handleAddressDetected={jest.fn()}
        handleLocalitySelect={jest.fn()}
        handlePincodeInputChange={jest.fn()}
        localityPickerRef={mockLocalityPickerRef}
        theme={mockTheme}
        styles={mockStyles}
        textColor={mockTextColor}
        borderColor={mockBorderColor}
      />
    );

    expect(getByText('Where are you located?')).toBeDefined();
    expect(getByPlaceholderText('Enter 6-digit pincode')).toBeDefined();
    expect(getByPlaceholderText('Select your locality')).toBeDefined();
    expect(getByPlaceholderText('Auto-filled from pincode')).toBeDefined(); // For City and State
    expect(getByPlaceholderText('e.g., House No., Street Name, Landmark')).toBeDefined();
  });

  it('shows location permission message when not granted and no GPS coordinates', () => {
    const { getByText } = render(
      <Step2AddressLocation
        isPincodeLoading={false}
        availableLocalities={[]}
        locationPermission={{ granted: false }}
        hasGpsCoordinates={false}
        fetchPincodeDetails={jest.fn()}
        setGpsDetectedLocality={jest.fn()}
        handleLocationDetected={jest.fn()}
        handleAddressDetected={jest.fn()}
        handleLocalitySelect={jest.fn()}
        handlePincodeInputChange={jest.fn()}
        localityPickerRef={mockLocalityPickerRef}
        theme={mockTheme}
        styles={mockStyles}
        textColor={mockTextColor}
        borderColor={mockBorderColor}
      />
    );

    expect(getByText('Location permission is required to continue')).toBeDefined();
  });

  it('does not show location permission message when granted', () => {
    const { queryByText } = render(
      <Step2AddressLocation
        isPincodeLoading={false}
        availableLocalities={[]}
        locationPermission={{ granted: true }}
        hasGpsCoordinates={false}
        fetchPincodeDetails={jest.fn()}
        setGpsDetectedLocality={jest.fn()}
        handleLocationDetected={jest.fn()}
        handleAddressDetected={jest.fn()}
        handleLocalitySelect={jest.fn()}
        handlePincodeInputChange={jest.fn()}
        localityPickerRef={mockLocalityPickerRef}
        theme={mockTheme}
        styles={mockStyles}
        textColor={mockTextColor}
        borderColor={mockBorderColor}
      />
    );

    expect(queryByText('Location permission is required to continue')).toBeNull();
  });

  it('calls handlePincodeInputChange on pincode input change', () => {
    const mockHandlePincodeInputChange = jest.fn();
    (Controller as jest.Mock).mockImplementationOnce(({ render }) =>
      render({
        field: { onChange: jest.fn(), onBlur: jest.fn(), value: '' },
        fieldState: { error: undefined },
      })
    );

    const { getByPlaceholderText } = render(
      <Step2AddressLocation
        isPincodeLoading={false}
        availableLocalities={[]}
        locationPermission={{ granted: true }}
        hasGpsCoordinates={true}
        fetchPincodeDetails={jest.fn()}
        setGpsDetectedLocality={jest.fn()}
        handleLocationDetected={jest.fn()}
        handleAddressDetected={jest.fn()}
        handleLocalitySelect={jest.fn()}
        handlePincodeInputChange={mockHandlePincodeInputChange}
        localityPickerRef={mockLocalityPickerRef}
        theme={mockTheme}
        styles={mockStyles}
        textColor={mockTextColor}
        borderColor={mockBorderColor}
      />
    );

    const pincodeInput = getByPlaceholderText('Enter 6-digit pincode');
    fireEvent.changeText(pincodeInput, '123456');
    expect(mockHandlePincodeInputChange).toHaveBeenCalledWith('123456');
  });

  it('calls localityPickerRef.current.present when Locality/Area picker is pressed', () => {
    const { getByPlaceholderText } = render(
      <Step2AddressLocation
        isPincodeLoading={false}
        availableLocalities={['Locality A', 'Locality B']}
        locationPermission={{ granted: true }}
        hasGpsCoordinates={true}
        fetchPincodeDetails={jest.fn()}
        setGpsDetectedLocality={jest.fn()}
        handleLocationDetected={jest.fn()}
        handleAddressDetected={jest.fn()}
        handleLocalitySelect={jest.fn()}
        handlePincodeInputChange={jest.fn()}
        localityPickerRef={mockLocalityPickerRef}
        theme={mockTheme}
        styles={mockStyles}
        textColor={mockTextColor}
        borderColor={mockBorderColor}
      />
    );

    const localityPicker = getByPlaceholderText('Select your locality');
    fireEvent.press(localityPicker);
    expect(mockLocalityPickerRef.current?.present).toHaveBeenCalled();
  });
});
