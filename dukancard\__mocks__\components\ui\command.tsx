import React from 'react';

export const Command = ({ children }: any) => (
  <div data-testid="command">{children}</div>
);

export const CommandEmpty = ({ children }: any) => (
  <div data-testid="command-empty">{children}</div>
);

export const CommandGroup = ({ children }: any) => (
  <div data-testid="command-group">{children}</div>
);

export const CommandInput = ({ placeholder, value, onValueChange }: any) => (
  <input 
    data-testid="command-input" 
    placeholder={placeholder}
    value={value}
    onChange={(e) => onValueChange?.(e.target.value)}
  />
);

export const CommandItem = ({ children, onSelect, value }: any) => (
  <div data-testid="command-item" onClick={() => onSelect?.(value)}>
    {children}
  </div>
);

export const CommandList = ({ children }: any) => (
  <div data-testid="command-list">{children}</div>
);
