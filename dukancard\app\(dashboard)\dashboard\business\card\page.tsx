import { Metadata } from "next";
import { getBusinessCardData } from "./actions";
import { defaultBusinessCardData } from "./schema";
import CardEditorClient, { UserPlanStatus } from "./CardEditorClient"; // Import UserPlanStatus type
import { createClient } from "@/utils/supabase/server";
import { COLUMNS, TABLES } from "@/lib/supabase/constants";

export const metadata: Metadata = {
  title: "Edit Business Card",
  description: "Manage and customize your digital business card.",
  robots: "noindex, nofollow", // Added robots meta tag
};

export default async function BusinessCardPage() {
  // Fetch initial data for the card
  const { data: initialData, error } = await getBusinessCardData();

  if (error) {
    // Handle error fetching data (e.g., show an error message)
    // For now, we'll proceed with defaults, but log the error
    console.error("Error fetching business card data:", error);
    // You might want to render an error component here
  }

  // Use fetched data or defaults if no profile exists yet
  const cardData = initialData ?? defaultBusinessCardData;

  // Fetch subscription data to get the plan_id and subscription_status
  const supabase = await createClient();
  const { data: subscription, error: subscriptionError } = await supabase
    .from(TABLES.PAYMENT_SUBSCRIPTIONS)
    .select(`${COLUMNS.PLAN_ID}, ${COLUMNS.SUBSCRIPTION_STATUS}`)
    .eq(COLUMNS.BUSINESS_PROFILE_ID, cardData.id || "")
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .limit(1)
    .maybeSingle();

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
  }

  const planId = subscription?.plan_id || "free";
  const subscriptionStatus = subscription?.subscription_status || null;

  return (
    <div className="w-full">
      <CardEditorClient
        initialData={cardData}
        currentUserPlan={planId as UserPlanStatus}
        subscriptionStatus={subscriptionStatus}
      />
    </div>
  );
}
