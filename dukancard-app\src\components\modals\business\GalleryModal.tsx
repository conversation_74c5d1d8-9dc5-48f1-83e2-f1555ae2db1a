import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, Modal, StyleSheet, TouchableOpacity, ScrollView, Image, ActivityIndicator, Alert } from 'react-native';
import { getBusinessGalleryImages } from '../../../../backend/supabase/services/gallery/galleryService';
import { GalleryImage } from '../../../../src/types/gallery';

interface GalleryModalProps {
  isVisible: boolean;
  onClose: () => void;
  businessId: string;
}

const GalleryModal: React.FC<GalleryModalProps> = ({ isVisible, onClose, businessId }) => {
  const [images, setImages] = useState<GalleryImage[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  const fetchImages = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const { images: fetchedImages, error: fetchError } = await getBusinessGalleryImages(businessId);
      if (fetchError) {
        throw new Error(fetchError);
      }
      setImages(fetchedImages);
    } catch (err) {
      console.error('Error fetching images:', err);
      setError('Failed to load images. Please try again.');
      Alert.alert('Error', 'Failed to load images. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [businessId]);

  useEffect(() => {
    if (isVisible) {
      fetchImages();
    }
  }, [isVisible, fetchImages]);

  const handleUploadImage = () => {
    console.log('Upload image clicked');
    // Implement image picker and upload to Supabase
  };

  const handleDeleteImages = () => {
    console.log('Delete selected images clicked:', selectedImages);
    // Implement deletion from Supabase
    setSelectedImages([]);
  };

  const toggleSelectImage = (imageUrl: string) => {
    setSelectedImages(prevSelected =>
      prevSelected.includes(imageUrl)
        ? prevSelected.filter(url => url !== imageUrl)
        : [...prevSelected, imageUrl]
    );
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <Text style={styles.modalTitle}>Business Gallery</Text>

          <ScrollView style={styles.imageGridContainer}>
            {loading ? (
              <ActivityIndicator size="large" color="#0000ff" />
            ) : error ? (
              <Text style={styles.errorText}>{error}</Text>
            ) : images.length === 0 ? (
              <Text>No images found. Upload some!</Text>
            ) : (
              <View style={styles.imageGrid}>
                {images.map((image, index) => (
                  <TouchableOpacity
                    key={image.id || index} // Use image.id if available, otherwise index
                    style={[
                      styles.imageWrapper,
                      selectedImages.includes(image.url) && styles.selectedImageWrapper,
                    ]}
                    onPress={() => toggleSelectImage(image.url)}
                  >
                    <Image source={{ uri: image.url }} style={styles.image} />
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.button} onPress={handleUploadImage}>
              <Text style={styles.buttonText}>Upload Image</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, selectedImages.length === 0 && styles.disabledButton]}
              onPress={handleDeleteImages}
              disabled={selectedImages.length === 0}
            >
              <Text style={styles.buttonText}>Delete Selected ({selectedImages.length})</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={onClose}>
              <Text style={styles.buttonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  imageGridContainer: {
    width: '100%',
    maxHeight: '70%',
    marginBottom: 20,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  imageWrapper: {
    width: 100,
    height: 100,
    margin: 5,
    borderRadius: 10,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedImageWrapper: {
    borderColor: 'blue',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  button: {
    backgroundColor: '#2196F3',
    borderRadius: 10,
    padding: 10,
    elevation: 2,
    marginHorizontal: 5,
  },
  disabledButton: {
    backgroundColor: '#A9A9A9',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginTop: 10,
  },
});

export default GalleryModal;
