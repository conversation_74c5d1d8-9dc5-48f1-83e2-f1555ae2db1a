import { ErrorType } from '@/src/components/ui/ErrorState';
import { AuthError } from "@supabase/supabase-js";

export interface AppError {
  type: ErrorType;
  title: string;
  message: string;
  code?: string;
  statusCode?: number;
  originalError?: any;
}

/**
 * User-friendly error messages for Supabase auth error codes
 */
const AUTH_ERROR_MESSAGES: Record<string, string> = {
  // Rate limiting errors
  over_email_send_rate_limit: "Email rate limit exceeded. Please wait before requesting another OTP.",
  over_request_rate_limit: "Too many requests. Please wait a few minutes before trying again.",
  over_sms_send_rate_limit: "Too many SMS messages sent. Please wait before requesting another OTP.",

  // OTP related errors
  otp_expired: "OTP has expired. Please request a new one.",
  otp_disabled: "OTP authentication is currently disabled.",

  // Token/JWT errors
  bad_jwt: "Invalid authentication token. Please sign in again.",
  session_expired: "Your session has expired. Please sign in again.",
  session_not_found: "Session not found. Please sign in again.",
  refresh_token_not_found: "Authentication expired. Please sign in again.",
  refresh_token_already_used: "Authentication expired. Please sign in again.",

  // User/account errors
  user_not_found: "User account not found.",
  user_banned: "Your account has been temporarily suspended.",
  email_not_confirmed: "Please verify your email address before signing in.",
  phone_not_confirmed: "Please verify your phone number before signing in.",
  invalid_credentials: "Invalid email or password.",

  // Signup/registration errors
  signup_disabled: "New account registration is currently disabled.",
  email_exists: "An account with this email already exists.",
  phone_exists: "An account with this phone number already exists.",
  weak_password: "Password does not meet security requirements.",
  email_address_invalid: "Please enter a valid email address.",
  email_address_not_authorized: "This email address is not authorized for registration.",

  // Provider/OAuth errors
  provider_disabled: "This sign-in method is currently disabled.",
  oauth_provider_not_supported: "This sign-in provider is not supported.",
  provider_email_needs_verification: "Please verify your email address to complete sign-in.",

  // Validation errors
  validation_failed: "Please check your input and try again.",
  bad_json: "Invalid request format. Please try again.",

  // MFA errors
  mfa_challenge_expired: "MFA challenge expired. Please try again.",
  mfa_verification_failed: "Invalid MFA code. Please try again.",
  insufficient_aal: "Additional authentication required.",

  // CAPTCHA errors
  captcha_failed: "CAPTCHA verification failed. Please try again.",

  // General errors
  conflict: "A conflict occurred. Please try again.",
  request_timeout: "Request timed out. Please try again.",
  unexpected_failure: "An unexpected error occurred. Please try again.",
  same_password: "New password must be different from your current password.",

  // Flow state errors
  flow_state_expired: "Authentication session expired. Please start over.",
  flow_state_not_found: "Authentication session not found. Please start over.",

  // Reauthentication errors
  reauthentication_needed: "Please verify your identity to continue.",
  reauthentication_not_valid: "Identity verification failed. Please try again.",
};

/**
 * Default error message for unknown error codes
 */
const DEFAULT_ERROR_MESSAGE = "An error occurred. Please try again.";

/**
 * Creates a standardized error object
 */
export function createAppError(
  type: ErrorType,
  title: string,
  message: string,
  options?: {
    code?: string;
    statusCode?: number;
    originalError?: any;
  }
): AppError {
  return {
    type,
    title,
    message,
    code: options?.code,
    statusCode: options?.statusCode,
    originalError: options?.originalError,
  };
}

/**
 * Handles network errors and converts them to user-friendly messages
 */
export function handleNetworkError(error: any): AppError {
  console.error('Network error:', error);

  // Check if it's a network connectivity issue
  if (!error.response && error.request) {
    return createAppError(
      'network',
      'No Internet Connection',
      'Please check your internet connection and try again.',
      { originalError: error }
    );
  }

  // Handle HTTP status codes
  if (error.response) {
    const statusCode = error.response.status;
    
    switch (statusCode) {
      case 400:
        return createAppError(
          'validation',
          'Invalid Request',
          'The request contains invalid data. Please check your input.',
          { statusCode, originalError: error }
        );
      
      case 401:
        return createAppError(
          'unauthorized',
          'Authentication Required',
          'Please sign in to access this content.',
          { statusCode, originalError: error }
        );
      
      case 403:
        return createAppError(
          'unauthorized',
          'Access Denied',
          'You don\'t have permission to access this content.',
          { statusCode, originalError: error }
        );
      
      case 404:
        return createAppError(
          'notFound',
          'Not Found',
          'The requested content could not be found.',
          { statusCode, originalError: error }
        );
      
      case 429:
        return createAppError(
          'server',
          'Too Many Requests',
          'You\'re making too many requests. Please wait a moment and try again.',
          { statusCode, originalError: error }
        );
      
      case 500:
      case 502:
      case 503:
      case 504:
        return createAppError(
          'server',
          'Server Error',
          'Something went wrong on our end. Please try again later.',
          { statusCode, originalError: error }
        );
      
      default:
        return createAppError(
          'generic',
          'Request Failed',
          'The request failed. Please try again.',
          { statusCode, originalError: error }
        );
    }
  }

  // Handle other types of errors
  if (error.message) {
    if (error.message.includes('timeout')) {
      return createAppError(
        'network',
        'Request Timeout',
        'The request took too long. Please try again.',
        { originalError: error }
      );
    }
    
    if (error.message.includes('Network Error')) {
      return createAppError(
        'network',
        'Network Error',
        'Unable to connect to the server. Please check your connection.',
        { originalError: error }
      );
    }
  }

  // Generic error fallback
  return createAppError(
    'generic',
    'Something went wrong',
    'An unexpected error occurred. Please try again.',
    { originalError: error }
  );
}

/**
 * Handles Supabase auth errors and returns user-friendly messages
 * @param error - The error object from Supabase
 * @returns User-friendly error message
 */
export function handleSupabaseError(error: AuthError | Error | null): AppError {
  console.error('Supabase error:', error);

  if (!error) {
    return createAppError(
      'generic',
      'Unknown Error',
      DEFAULT_ERROR_MESSAGE,
      { originalError: error }
    );
  }

  // Check if it's an AuthError with a code property
  if ('code' in error && typeof error.code === 'string') {
    const userMessage = AUTH_ERROR_MESSAGES[error.code];
    if (userMessage) {
      return createAppError(
        'validation',
        'Authentication Error',
        userMessage,
        { code: error.code, originalError: error }
      );
    }
  }

  // Check if it's an AuthError with a message we can parse for specific cases
  if (error.message) {
      const message = error.message.toLowerCase();
    
    // Handle common network-related messages first
    if (message.includes('failed to fetch') || message.includes('network request failed') || message.includes('network error')) {
      return createAppError(
        'network',
        'Network Error',
        "Network error. Please check your internet connection and try again.",
        { originalError: error }
      );
    }

    // Handle other common message patterns that might not have specific codes
    if (message.includes('token has expired') || message.includes('expired')) {
      return createAppError(
        'unauthorized',
        'Session Expired',
        "Your session has expired. Please sign in again.",
        { originalError: error }
      );
    }
    
    if (message.includes('invalid token') || message.includes('invalid otp')) {
      return createAppError(
        'validation',
        'Invalid Code',
        "Invalid code. Please check and try again.",
        { originalError: error }
      );
    }
    
    if (message.includes('rate limit') || message.includes('too many')) {
      return createAppError(
        'server',
        'Too Many Attempts',
        "Too many attempts. Please wait before trying again.",
        { originalError: error }
      );
    }
    
    if (message.includes('email already exists') || message.includes('user already exists')) {
      return createAppError(
        'validation',
        'Email Exists',
        "An account with this email already exists.",
        { originalError: error }
      );
    }
    
    if (message.includes('invalid email')) {
      return createAppError(
        'validation',
        'Invalid Email',
        "Please enter a valid email address.",
        { originalError: error }
      );
    }
    
    if (message.includes('weak password')) {
      return createAppError(
        'validation',
        'Weak Password',
        "Password does not meet security requirements.",
        { originalError: error }
      );
    }
  }

  // Handle Supabase database errors (if applicable, keep existing logic or refine)
  if ('code' in error && typeof error.code === 'string') {
    switch (error.code) {
      case 'PGRST116':
        return createAppError(
          'notFound',
          'Not Found',
          'The requested data could not be found.',
          { code: error.code, originalError: error }
        );
      
      case 'PGRST301':
        return createAppError(
          'validation',
          'Invalid Data',
          'The provided data is invalid or incomplete.',
          { code: error.code, originalError: error }
        );
      
      default:
        return createAppError(
          'server',
          'Database Error',
          'A database error occurred. Please try again.',
          { code: error.code, originalError: error }
        );
    }
  }

  // Generic Supabase error fallback
  return createAppError(
    'generic',
    'Service Error',
    error.message || DEFAULT_ERROR_MESSAGE,
    { originalError: error }
  );
}

/**
 * Logs errors for debugging and analytics
 */
export function logError(error: AppError | any, context?: string) {
  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    context,
    error: error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
    } : error,
  };

  // Log to console in development
  if (__DEV__) {
    console.error('Error logged:', logData);
  }

  // In production, you might want to send this to a logging service
  // Example: Analytics.logError(logData);
}

/**
 * Retry utility with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff: delay = baseDelay * 2^attempt
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
