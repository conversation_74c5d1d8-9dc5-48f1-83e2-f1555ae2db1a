import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import LinkEmailSection from '@/app/(dashboard)/customer/settings/components/LinkEmailSection';
import { useTheme } from '@/src/hooks/useTheme';
import { Alert, TextInput, TouchableOpacity, Text } from 'react-native';

// Mock external modules and components
jest.mock('@/src/hooks/useTheme');
jest.mock('@/src/components/ui/Input', () => ({
  Input: ({ onChangeText, value, ...props }: any) => (
    <TextInput testID={props.placeholder} onChangeText={onChangeText} value={value} {...props} />
  ),
}));
jest.mock('@/src/components/ui/Button', () => ({
  Button: ({ title, onPress, disabled, loading, icon, ...props }: any) => (
    <TouchableOpacity testID={title} onPress={onPress} disabled={disabled} {...props}>
      {loading ? <Text>Loading...</Text> : <Text>{title}</Text>}
      {icon}
    </TouchableOpacity>
  ),
}));
jest.mock('lucide-react-native', () => ({
  Mail: 'Mail',
  Loader2: 'Loader2',
  Link: 'Link',
  AlertCircle: 'AlertCircle',
}));
jest.mock('@/styles/dashboard/customer/settings/link-email', () => ({
  createLinkEmailSectionStyles: jest.fn(() => ({
    container: {},
    header: {},
    titleContainer: {},
    iconContainer: {},
    title: {},
    description: {},
    content: {},
    messageContainer: {},
    messageContent: {},
    messageText: {},
    section: {},
    label: {},
    readOnlyContainer: {},
    readOnlyText: {},
    helperText: {},
    inputContainer: {},
    buttonContainer: {},
    submitButton: {},
    otpHeader: {},
    emailDisplay: {},
    buttonRow: {},
    backButton: {},
    backButtonText: {},
  })),
}));

describe('LinkEmailSection', () => {
  const mockTheme = {
    colors: {
      primary: '#D4AF37',
      textPrimary: '#000',
      textSecondary: '#666',
      primaryForeground: '#fff',
    },
  };

  beforeEach(() => {
    (useTheme as jest.Mock).mockReturnValue(mockTheme);
    (Alert.alert as jest.Mock).mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly for google registration type', () => {
    const { getByText, getByPlaceholderText } = render(
      <LinkEmailSection currentEmail="<EMAIL>" registrationType="google" />
    );
    expect(getByText('Email Address (Google)')).toBeDefined();
    expect(getByText('Your email is linked to your Google account and cannot be changed here.')).toBeDefined();
    expect(getByText('<EMAIL>')).toBeDefined();
    expect(getByText('This email is managed by your Google account.')).toBeDefined();
    expect(getByPlaceholderText('<EMAIL>')).toBeNull(); // Should not show input
  });

  it('renders correctly for email registration type', () => {
    const { getByText, getByPlaceholderText } = render(
      <LinkEmailSection currentEmail="<EMAIL>" registrationType="email" />
    );
    expect(getByText('Update Email Address')).toBeDefined();
    expect(getByText('Update your email address. We\'ll send OTP verification to both old and new email addresses.')).toBeDefined();
    expect(getByPlaceholderText('<EMAIL>')).toBeDefined();
    expect(getByText('We\'ll send verification codes to both your current and new email addresses.')).toBeDefined();
    expect(getByText('Update Email')).toBeDefined();
  });

  it('renders correctly for phone registration type', () => {
    const { getByText, getByPlaceholderText } = render(
      <LinkEmailSection currentPhone="+************" registrationType="phone" />
    );
    expect(getByText('Link Email Address')).toBeDefined();
    expect(getByText('Add an email address to your account for additional login options and notifications.')).toBeDefined();
    expect(getByPlaceholderText('<EMAIL>')).toBeDefined();
    expect(getByText('We\'ll send a verification code to this email address.')).toBeDefined();
    expect(getByText('Link Email Address')).toBeDefined();
  });

  it('shows error alert for invalid email on submit', async () => {
    const { getByText, getByPlaceholderText } = render(
      <LinkEmailSection registrationType="phone" />
    );
    fireEvent.changeText(getByPlaceholderText('<EMAIL>'), 'invalid-email');
    fireEvent.press(getByText('Link Email Address'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Please enter a valid email address');
    });
  });

  it('transitions to OTP step after successful email submission for phone user', async () => {
    const { getByText, getByPlaceholderText } = render(
      <LinkEmailSection registrationType="phone" />
    );
    fireEvent.changeText(getByPlaceholderText('<EMAIL>'), '<EMAIL>');
    fireEvent.press(getByText('Link Email Address'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Verification code sent!');
      expect(getByText('We\'ve sent a 6-digit code to')).toBeDefined();
      expect(getByText('<EMAIL>')).toBeDefined();
      expect(getByPlaceholderText('123456')).toBeDefined();
      expect(getByText('Verify & Link Email')).toBeDefined();
    });
  });

  it('shows success message after successful OTP verification', async () => {
    const { getByText, getByPlaceholderText } = render(
      <LinkEmailSection registrationType="phone" />
    );
    fireEvent.changeText(getByPlaceholderText('<EMAIL>'), '<EMAIL>');
    fireEvent.press(getByText('Link Email Address'));

    await waitFor(() => {
      expect(getByPlaceholderText('123456')).toBeDefined();
    });

    fireEvent.changeText(getByPlaceholderText('123456'), '123456');
    fireEvent.press(getByText('Verify & Link Email'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Email linked successfully!');
      expect(getByText('Email address has been linked to your account.')).toBeDefined();
      expect(getByPlaceholderText('<EMAIL>')).toBeDefined(); // Back to email step
    });
  });

  it('transitions back to email step from OTP step', async () => {
    const { getByText, getByPlaceholderText } = render(
      <LinkEmailSection registrationType="phone" />
    );
    fireEvent.changeText(getByPlaceholderText('<EMAIL>'), '<EMAIL>');
    fireEvent.press(getByText('Link Email Address'));

    await waitFor(() => {
      expect(getByText('← Back to Email')).toBeDefined();
    });

    fireEvent.press(getByText('← Back to Email'));

    await waitFor(() => {
      expect(getByPlaceholderText('<EMAIL>')).toBeDefined();
      expect(getByText('Link Email Address')).toBeDefined();
    });
  });
});
