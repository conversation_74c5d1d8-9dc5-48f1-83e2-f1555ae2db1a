"use server";

import { createClient } from "@/utils/supabase/server";

import { getThemeSpecificHeaderImagePath } from "@/lib/utils/storage-paths";
import { BUCKETS } from "@/lib/supabase/constants";

export interface ThemeHeaderUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface ThemeHeaderDeleteResult {
  success: boolean;
  error?: string;
}

/**
 * Upload theme-specific custom header image with compression
 * This function only uploads to storage and returns the URL
 * Database update happens in the main save action
 */
export async function uploadThemeHeaderImage(
  imageFile: File,
  theme: 'light' | 'dark'
): Promise<ThemeHeaderUploadResult> {
  try {
    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // Validate file type
    if (!imageFile.type.startsWith("image/")) {
      return {
        success: false,
        error: "Invalid file type. Please upload an image file.",
      };
    }

    // Validate file size (5MB limit)
    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    if (imageFile.size > maxSizeBytes) {
      const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(1);
      return {
        success: false,
        error: `Image size (${fileSizeMB}MB) is too large. Please choose an image smaller than 5MB.`,
      };
    }

    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const imagePath = getThemeSpecificHeaderImagePath(user.id, timestamp, theme);

    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    // Upload to Supabase Storage using admin client
    const { error: uploadError } = await supabase.storage
      .from(BUCKETS.BUSINESS)
      .upload(imagePath, fileBuffer, {
        contentType: imageFile.type, // Use original file type (already compressed)
        upsert: true
      });

    if (uploadError) {
      console.error("Theme Header Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError.message}`,
      };
    }

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(BUCKETS.BUSINESS)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    return {
      success: true,
      url: urlData.publicUrl,
    };

  } catch (error) {
    console.error("Theme header upload error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during upload.",
    };
  }
}

/**
 * Delete theme-specific custom header image from storage
 * This function only deletes from storage
 * Database update happens in the main save action
 */
export async function deleteThemeHeaderImage(
  imageUrl: string
): Promise<ThemeHeaderDeleteResult> {
  try {
    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    if (!imageUrl || imageUrl.trim() === "") {
      return {
        success: true, // Nothing to delete
      };
    }

    try {
      // Extract the storage path from the URL
      const url = new URL(imageUrl);
      const pathParts = url.pathname.split('/');

      // The path will be in format like /storage/v1/object/public/business/userId/branding/header_theme_timestamp.webp
      const businessIndex = pathParts.findIndex(part => part === 'business');

      if (businessIndex !== -1 && businessIndex < pathParts.length - 1) {
        // Extract the path after 'business/'
        const storagePath = pathParts.slice(businessIndex + 1).join('/').split('?')[0];

        // Delete the file from storage using admin client
        const { error: deleteError } = await supabase.storage
          .from(BUCKETS.BUSINESS)
          .remove([storagePath]);

        if (deleteError && deleteError.message !== "The resource was not found") {
          console.error("Error deleting theme header from storage:", deleteError);
          return {
            success: false,
            error: `Failed to delete image: ${deleteError.message}`,
          };
        }
      }
    } catch (urlError) {
      console.error("Error processing image URL for deletion:", urlError);
      return {
        success: false,
        error: "Invalid image URL format.",
      };
    }

    return {
      success: true,
    };

  } catch (error) {
    console.error("Theme header deletion error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during deletion.",
    };
  }
}

/**
 * Clean up old theme-specific header images for a user
 * This is used to remove old images when new ones are uploaded
 */
export async function cleanupOldThemeHeaderImages(
  userId: string,
  theme: 'light' | 'dark',
  keepUrl?: string
): Promise<void> {
  try {
    const supabase = await createClient();
    
    // Get the user's branding folder path
    const userPath = userId.slice(0, 2) + '/' + userId.slice(2, 4) + '/' + userId;
    const brandingFolderPath = `${userPath}/branding/`;

    // List all files in the branding folder
    const { data: existingFiles, error: listError } = await supabase.storage
      .from(BUCKETS.BUSINESS)
      .list(brandingFolderPath, { limit: 20 });

    if (listError || !existingFiles) {
      console.error("Error listing branding files:", listError);
      return;
    }

    // Filter for theme-specific header files
    const themeHeaderFiles = existingFiles.filter((file: { name: string; }) => 
      file.name.startsWith(`header_${theme}_`) && file.name.endsWith('.webp')
    );

    // If we have a URL to keep, extract its filename
    let keepFilename: string | undefined;
    if (keepUrl) {
      try {
        const url = new URL(keepUrl);
        const pathParts = url.pathname.split('/');
        keepFilename = pathParts[pathParts.length - 1].split('?')[0];
      } catch (error) {
        console.error("Error extracting filename from keep URL:", error);
      }
    }

    // Delete old files (keep the current one if specified)
    const filesToDelete = themeHeaderFiles
      .filter((file: { name: string; }) => !keepFilename || file.name !== keepFilename)
      .map((file: { name: string; }) => `${brandingFolderPath}${file.name}`);

    if (filesToDelete.length > 0) {
      const { error: deleteError } = await supabase.storage
        .from(BUCKETS.BUSINESS)
        .remove(filesToDelete);

      if (deleteError) {
        console.error("Error cleaning up old theme header files:", deleteError);
      }
    }
  } catch (error) {
    console.error("Error in cleanup function:", error);
  }
}
