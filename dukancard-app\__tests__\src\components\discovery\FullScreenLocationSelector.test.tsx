import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { FullScreenLocationSelector } from '@/src/components/discovery/FullScreenLocationSelector';

// Mock dependencies
jest.mock('@/src/hooks/useColorScheme', () => () => 'light');
jest.mock('@/backend/supabase/services/location/locationService', () => ({
  LocationService: {
    getPincodeDetails: jest.fn(),
  },
  requestLocationPermission: jest.fn(),
  reverseGeocodeCoordinates: jest.fn(),
}));
jest.mock('@/backend/supabase/services/common/profileService', () => ({
  validatePincodeForCity: jest.fn(),
}));
jest.mock('expo-location', () => ({
  getCurrentPositionAsync: jest.fn(),
}));

describe('FullScreenLocationSelector', () => {
  const mockOnClose = jest.fn();
  const mockOnLocationSelect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly when visible', () => {
    const { getByText } = render(
      <FullScreenLocationSelector
        visible={true}
        onClose={mockOnClose}
        onLocationSelect={mockOnLocationSelect}
      />
    );

    expect(getByText('Use Current Location')).toBeTruthy();
    expect(getByText('City *')).toBeTruthy();
    expect(getByText('Pincode')).toBeTruthy();
    expect(getByText('Locality')).toBeTruthy();
  });

  it('does not render when not visible', () => {
    const { queryByText } = render(
      <FullScreenLocationSelector
        visible={false}
        onClose={mockOnClose}
        onLocationSelect={mockOnLocationSelect}
      />
    );

    expect(queryByText('Use Current Location')).toBeNull();
  });

  it('calls onClose when the close button is pressed', () => {
    const { getByTestId } = render(
      <FullScreenLocationSelector
        visible={true}
        onClose={mockOnClose}
        onLocationSelect={mockOnLocationSelect}
      />
    );

    fireEvent.press(getByTestId('close-button'));
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('calls onLocationSelect with the correct data when save is pressed', () => {
    const { getByText, getByPlaceholderText } = render(
      <FullScreenLocationSelector
        visible={true}
        onClose={mockOnClose}
        onLocationSelect={mockOnLocationSelect}
      />
    );

    fireEvent.changeText(getByPlaceholderText('Enter city name'), 'Bengaluru');
    fireEvent.changeText(getByPlaceholderText('Enter 6-digit pincode'), '560034');

    fireEvent.press(getByText('Save'));

    expect(mockOnLocationSelect).toHaveBeenCalledWith({
      city: 'Bengaluru',
      pincode: '560034',
      locality: '',
    });
    expect(mockOnClose).toHaveBeenCalled();
  });
});
