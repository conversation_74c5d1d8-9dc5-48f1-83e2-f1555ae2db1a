import { deleteProductVariant, deleteMultipleVariants, deleteAllProductVariants } from '@/app/(dashboard)/dashboard/business/products/actions/deleteVariant';
import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';
import { getScalableUserPath } from '@/lib/utils/storage-paths';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));
jest.mock('@/lib/utils/storage-paths', () => ({
  getScalableUserPath: jest.fn((userId) => `users/${userId}`),
}));

const mockSupabase = createClient as jest.Mock;

describe('Product Variant Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('deleteProductVariant', () => {
    let mockDelete: jest.Mock;
    let mockEq: jest.Mock;
    let mockSingle: jest.Mock;
    let mockRemove: jest.Mock;
    let mockList: jest.Mock;

    beforeEach(() => {
      jest.clearAllMocks();
      mockDelete = jest.fn().mockReturnThis();
      mockEq = jest.fn().mockReturnThis();
      mockSingle = jest.fn(); // This will be set in each test case as it depends on mockVariant
      mockRemove = jest.fn().mockResolvedValue({ data: null, error: null });
      mockList = jest.fn().mockResolvedValue({ data: [], error: null });
    });

    

    it('should return an error if the user is not authenticated', async () => {
      // Arrange
      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
        },
      });

      // Act
      const result = await deleteProductVariant('var-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('User not authenticated.');
    });

    it('should delete a variant successfully', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockVariant = { id: 'var-123', product_id: 'prod-123', products_services: { business_id: 'user-123' }, images: [] };
      mockDelete = jest.fn().mockReturnThis();
      mockEq = jest.fn().mockReturnThis();
      mockSingle = jest.fn().mockResolvedValue({ data: mockVariant, error: null });

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn(() => ({
          delete: mockDelete,
          eq: mockEq,
          select: jest.fn().mockReturnThis(),
          single: mockSingle,
        })),
        storage: {
            from: jest.fn().mockReturnThis(),
            remove: jest.fn().mockResolvedValue({ data: null, error: null }),
            list: jest.fn().mockResolvedValue({ data: [], error: null }),
        }
    });

      // Act
      const result = await deleteProductVariant('var-123');

      // Assert
      expect(result.success).toBe(true);
      expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
    });

    it('should delete a variant and its associated images successfully', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockVariant = { 
        id: 'var-123', 
        product_id: 'prod-123', 
        products_services: { business_id: 'user-123' }, 
        images: [
          'https://example.com/storage/v1/object/public/business/users/user-123/products/prod-123/var-123/image1.jpg',
          'https://example.com/storage/v1/object/public/business/users/user-123/products/prod-123/var-123/image2.png',
        ],
      };
      mockDelete = jest.fn().mockReturnThis();
      mockEq = jest.fn().mockReturnThis();
      mockSingle = jest.fn().mockResolvedValue({ data: mockVariant, error: null });
      mockRemove = jest.fn().mockResolvedValue({ data: null, error: null });
      mockList = jest.fn().mockResolvedValue({ data: [], error: null });

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn(() => ({
          delete: mockDelete,
          eq: mockEq,
          select: jest.fn().mockReturnThis(),
          single: mockSingle,
        })),
        storage: {
            from: jest.fn(() => ({
                remove: mockRemove,
                list: mockList,
            })),
        }
    });

      // Act
      const result = await deleteProductVariant('var-123');

      // Assert
      expect(result.success).toBe(true);
      expect(mockRemove).toHaveBeenCalledWith(['users/user-123/products/prod-123/var-123/image1.jpg']);
      expect(mockRemove).toHaveBeenCalledWith(['users/user-123/products/prod-123/var-123/image2.png']);
      expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
      expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products/prod-123');
    });

    it(`should return an error if it's the last available variant and others exist`, async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockVariantToDelete = {
        id: 'var-123',
        product_id: 'prod-123',
        is_available: true,
        products_services: { business_id: 'user-123' },
        images: []
      };
      const mockOtherVariant = {
        id: 'var-456',
        product_id: 'prod-123',
        is_available: false,
        products_services: { business_id: 'user-123' },
        images: []
      };

      // Mock for the first `from` call (for variantInfo)
      const mockFromVariantInfo = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockVariantToDelete, error: null }),
      };

      // Mock for the second `from` call (for availableVariants)
      const mockFromAvailableVariants = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({ data: [mockVariantToDelete, mockOtherVariant], error: null }),
      };

      // Mock for the delete call (should not be called in this test)
      const mockFromDelete = {
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
      };

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn()
          .mockImplementationOnce(() => mockFromVariantInfo) // First call to from
          .mockImplementationOnce(() => mockFromAvailableVariants) // Second call to from
          .mockImplementation(() => mockFromDelete), // Subsequent calls (e.g., for delete)
      });

      // Act
      const result = await deleteProductVariant('var-123');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('Cannot delete the last available variant. At least one variant must remain available.');
      expect(mockFromDelete.delete).not.toHaveBeenCalled(); // Use the specific mock
      expect(revalidatePath).not.toHaveBeenCalled();
    });

    it(`should delete the last available variant if it's the only variant`, async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockVariantToDelete = {
        id: 'var-123',
        product_id: 'prod-123',
        is_available: true,
        products_services: { business_id: 'user-123' },
        images: []
      };

      // Mock for the first `from` call (for variantInfo)
      const mockFromVariantInfo = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({ data: mockVariantToDelete, error: null }),
      };

      // Mock for the second `from` call (for availableVariants)
      const mockFromAvailableVariants = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({ data: [mockVariantToDelete], error: null }),
      };

      // Mock for the delete call
      const mockFromDelete = {
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({ error: null }),
      };

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn()
          .mockImplementationOnce(() => mockFromVariantInfo) // First call to from
          .mockImplementationOnce(() => mockFromAvailableVariants) // Second call to from
          .mockImplementation(() => mockFromDelete), // Subsequent calls (e.g., for delete)
        storage: {
          from: jest.fn().mockReturnThis(),
          remove: jest.fn().mockResolvedValue({ data: null, error: null }),
          list: jest.fn().mockResolvedValue({ data: [], error: null }),
        }
      });

      // Act
      const result = await deleteProductVariant('var-123');

      // Assert
      expect(result.success).toBe(true);
      expect(mockFromDelete.delete).toHaveBeenCalled(); // Use the specific mock
      expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
      expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products/prod-123');
    });
  });

  describe('deleteMultipleVariants', () => {
    it('should delete multiple variants successfully', async () => {
        // Arrange
        const mockUser = { id: 'user-123' };
        const mockVariants = [
            { id: 'var-1', product_id: 'prod-1', products_services: { business_id: 'user-123' }, images: [] },
            { id: 'var-2', product_id: 'prod-1', products_services: { business_id: 'user-123' }, images: [] }
        ];
        const mockDelete = jest.fn().mockResolvedValue({ error: null });
        const mockIn = jest.fn().mockReturnThis();
        const mockSelect = jest.fn().mockResolvedValue({ data: mockVariants, error: null });
  
        mockSupabase.mockReturnValue({
          auth: {
            getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
          },
          from: jest.fn((table: string) => {
            if (table === 'product_variants') {
              return {
                select: jest.fn().mockReturnThis(),
                in: jest.fn().mockResolvedValue({ data: mockVariants, error: null }),
                delete: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ error: null }),
              };
            }
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              delete: jest.fn().mockReturnThis(),
              in: jest.fn().mockReturnThis(),
            };
          }),
          storage: {
              from: jest.fn().mockReturnThis(),
              remove: jest.fn().mockResolvedValue({ data: null, error: null }),
              list: jest.fn().mockResolvedValue({ data: [], error: null }),
          }
        }); 
  
        // Act
        const result = await deleteMultipleVariants(['var-1', 'var-2']);
  
        // Assert
        expect(result.success).toBe(true);
        expect(result.deleted_count).toBe(2);
        expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
      });
  });

  describe('deleteAllProductVariants', () => {
    it('should delete all variants for a product', async () => {
        // Arrange
        const mockUser = { id: 'user-123' };
        const mockProduct = { id: 'prod-123', business_id: 'user-123' };
        const mockDelete = jest.fn().mockResolvedValue({ error: null });
        const mockEq = jest.fn().mockReturnThis();
        const mockSingle = jest.fn().mockResolvedValue({ data: mockProduct, error: null });
  
        mockSupabase.mockReturnValue({
          auth: {
            getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
          },
          from: jest.fn((table: string) => {
            if (table === 'products_services') {
              return {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockReturnThis(),
                single: jest.fn().mockResolvedValue({ data: mockProduct, error: null }),
              };
            }
            if (table === 'product_variants') {
              return {
                select: jest.fn().mockReturnThis(),
                eq: jest.fn().mockResolvedValue({ count: 2, error: null }),
                delete: jest.fn().mockReturnThis(),
              };
            }
            return {
              delete: jest.fn().mockReturnThis(),
              eq: jest.fn().mockResolvedValue({ error: null }),
            };
          }),
        });
  
        // Act
        const result = await deleteAllProductVariants('prod-123');
  
        // Assert
        expect(result.success).toBe(true);
      });
  });
});