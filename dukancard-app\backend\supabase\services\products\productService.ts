import { supabase } from "@/lib/supabase";
import { Tables, TablesInsert, TablesUpdate } from "../../../../src/types/supabase";

export type ProductsServices = Tables<"products_services">;
export type ProductsServicesInsert = TablesInsert<"products_services">;
export type ProductsServicesUpdate = TablesUpdate<"products_services">;

export class ProductService {
  static async searchBusinessProducts(userId: string, query: string) {
    return await supabase
      .from("products_services")
      .select(
        `
        id,
        name,
        slug,
        image_url,
        base_price,
        discounted_price,
        description,
        business_id
      `
      )
      .eq("business_id", userId)
      .ilike("name", `%${query}%`)
      .eq("is_available", true)
      .order("name")
      .limit(20);
  }

  static async getSelectedProducts(userId: string, productIds: string[]) {
    return await supabase
      .from("products_services")
      .select(
        `
        id,
        name,
        slug,
        image_url,
        base_price,
        discounted_price,
        description,
        business_id
      `
      )
      .in("id", productIds)
      .eq("business_id", userId)
      .eq("is_available", true);
  }

  static async getBusinessProducts(
    userId: string,
    offset: number,
    limit: number
  ) {
    return await supabase
      .from("products_services")
      .select(
        `
        id,
        name,
        slug,
        image_url,
        base_price,
        discounted_price,
        description,
        business_id
      `,
        { count: "exact" }
      )
      .eq("business_id", userId)
      .eq("is_available", true)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);
  }

  static async getProduct(userId: string, productId: string) {
    return await supabase
      .from("products_services")
      .select(
        `
        id,
        name,
        slug,
        image_url,
        base_price,
        discounted_price,
        description,
        business_id
      `
      )
      .eq("id", productId)
      .eq("business_id", userId)
      .eq("is_available", true)
      .single();
  }
}

export async function fetchProductsByIds(productIds: string[]) {
  return await supabase
    .from("products_services")
    .select(
      `
      id,
      name,
      slug,
      image_url,
      base_price,
      discounted_price,
      description,
      business_id,
      is_available,
      created_at,
      updated_at
    `
    )
    .in("id", productIds)
    .eq("is_available", true);
}

// Legacy alias for backward compatibility
export type ProductData = ProductsServices;
