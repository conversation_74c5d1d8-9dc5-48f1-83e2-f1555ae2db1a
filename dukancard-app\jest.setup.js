/* eslint-env jest */
import React from "react";
import "@testing-library/jest-native/extend-expect";



// Mock react-native-url-polyfill
jest.mock("react-native-url-polyfill/auto", () => {});

// Mock TurboModuleRegistry
jest.mock('react-native/Libraries/TurboModule/TurboModuleRegistry', () => ({
  getEnforcing: jest.fn(() => ({
    show: jest.fn(),
    reload: jest.fn(),
    debugRemoteJS: jest.fn(),
    setProfilingEnabled: jest.fn(),
    setHotLoadingEnabled: jest.fn(),
    setLiveReloadEnabled: jest.fn(),
    setElementInspectorEnabled: jest.fn(),
    setNetworkInspectorEnabled: jest.fn(),
    setVolumesEnabled: jest.fn(),
    getConstants: jest.fn(() => ({})),
  })),
  get: jest.fn(() => ({
    getConstants: jest.fn(() => ({})),
  })),
}));

// Mock NativeModules
jest.mock('react-native/Libraries/BatchedBridge/NativeModules', () => ({
  DeviceInfo: {
    getConstants: jest.fn(() => ({
      Dimensions: {
        window: { width: 375, height: 812, scale: 2, fontScale: 1 },
        screen: { width: 375, height: 812, scale: 2, fontScale: 1 },
      },
    })),
  },
  PlatformConstants: {
    getConstants: jest.fn(() => ({
      forceTouchAvailable: false,
      osVersion: '14.0',
      systemName: 'iOS',
      interfaceIdiom: 'phone',
    })),
  },
}));

// Mock the Dimensions module directly
jest.mock('react-native/Libraries/Utilities/Dimensions', () => {
  const mockDimensions = {
    window: { width: 375, height: 812, scale: 2, fontScale: 1 },
    screen: { width: 375, height: 812, scale: 2, fontScale: 1 },
  };

  return {
    get: jest.fn((dim) => mockDimensions[dim] || mockDimensions.window),
    set: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  };
});

// Mock PixelRatio
jest.mock('react-native/Libraries/Utilities/PixelRatio', () => ({
  get: jest.fn(() => 2),
  getFontScale: jest.fn(() => 1),
  getPixelSizeForLayoutSize: jest.fn((layoutSize) => layoutSize * 2),
  roundToNearestPixel: jest.fn((layoutSize) => Math.round(layoutSize)), // Mock to return a number
}));

// Mock Expo modules
jest.mock('expo', () => ({
  __esModule: true,
  NativeModules: {
    EXDevLauncher: {},
    // Add other native modules that Expo might try to access
  },
  requireNativeModule: jest.fn(() => ({})), // Added this line
  // Mock other top-level exports of the 'expo' package as needed
  // For example, if a module tries to access Expo.Constants
  Constants: {
    manifest: {},
    // Add other properties of Constants as needed
  },
  // Mock any other functions or objects directly exported by 'expo'
}));

jest.mock("expo-constants", () => ({
  expoConfig: {
    extra: {
      supabaseUrl: "mock-supabase-url",
      supabaseAnonKey: "mock-supabase-anon-key",
    },
  },
  // Mock any other properties or functions that expo-constants might export
}));

jest.mock('@react-native-community/netinfo', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(),
    fetch: jest.fn(() => Promise.resolve({ isConnected: true, isInternetReachable: true, type: 'wifi' })),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    // Mock the native module directly
    RNCNetInfo: {
      getCurrentState: jest.fn(() => Promise.resolve({ isConnected: true, isInternetReachable: true, type: 'wifi' })),
      addListener: jest.fn(),
      removeListeners: jest.fn(),
    },
  },
}));



jest.mock('expo-modules-core', () => ({
  NativeModulesProxy: {},
  requireNativeViewManager: jest.fn(),
  requireOptionalNativeModule: jest.fn(() => undefined),
  requireNativeModule: jest.fn(() => ({})),
}));

jest.mock('expo-blur', () => ({
  BlurView: 'View',
}));

jest.mock('expo-image-picker', () => ({
  launchCameraAsync: jest.fn(() => Promise.resolve({ cancelled: true })),
  launchImageLibraryAsync: jest.fn(() => Promise.resolve({ cancelled: true })),
  requestCameraPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
  requestMediaLibraryPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
  getMediaLibraryPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
  getCameraPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
}));

jest.mock("expo-router", () => ({
  router: {
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    canGoBack: jest.fn(() => true),
  },
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    canGoBack: jest.fn(() => true),
  })),
  useLocalSearchParams: jest.fn(() => ({})),
  useSegments: jest.fn(() => []),
  usePathname: jest.fn(() => "/"),
}));

jest.mock("expo-status-bar", () => ({
  StatusBar: "StatusBar",
}));

jest.mock("expo-web-browser", () => ({
  openBrowserAsync: jest.fn(() => Promise.resolve({ type: 'cancel' })),
  dismissBrowser: jest.fn(() => Promise.resolve()),
  mayInitWithUrlAsync: jest.fn(() => Promise.resolve()),
  warmUpAsync: jest.fn(() => Promise.resolve()),
  coolDownAsync: jest.fn(() => Promise.resolve()),
}));

// Mock Expo Secure Store
let mockSecureStore = {};

jest.mock("expo-secure-store", () => ({
  getItemAsync: jest.fn((key) => Promise.resolve(mockSecureStore[key] || null)),
  setItemAsync: jest.fn((key, value) => {
    mockSecureStore[key] = value;
    return Promise.resolve();
  }),
  deleteItemAsync: jest.fn((key) => {
    delete mockSecureStore[key];
    return Promise.resolve();
  }),
}));

jest.mock("expo-location", () => ({
  PermissionStatus: {
    GRANTED: "granted",
    UNDETERMINED: "undetermined",
    DENIED: "denied",
  },
  requestForegroundPermissionsAsync: jest.fn(() =>
    Promise.resolve({
      status: "granted",
      canAskAgain: true,
      granted: true,
      expires: "never",
    })
  ),
  getForegroundPermissionsAsync: jest.fn(() =>
    Promise.resolve({
      status: "granted",
      canAskAgain: true,
      granted: true,
      expires: "never",
    })
  ),
  getCurrentPositionAsync: jest.fn(() =>
    Promise.resolve({
      coords: {
        latitude: 28.6139,
        longitude: 77.209,
      },
    })
  ),
  reverseGeocodeAsync: jest.fn(() =>
    Promise.resolve([
      {
        postalCode: "110001",
        city: "New Delhi",
        region: "Delhi",
      },
    ])
  ),
  hasServicesEnabledAsync: jest.fn(() => Promise.resolve(true)),
  Accuracy: {
    Balanced: 3,
  },
}));

jest.mock("@react-native-google-signin/google-signin", () => ({
  GoogleSignin: {
    configure: jest.fn(),
    hasPlayServices: jest.fn(() => Promise.resolve(true)),
    signIn: jest.fn(() =>
      Promise.resolve({
        user: {
          id: "mock-user-id",
          name: "Mock User",
          email: "<EMAIL>",
        },
        idToken: "mock-id-token",
      })
    ),
    signOut: jest.fn(() => Promise.resolve()),
    isSignedIn: jest.fn(() => Promise.resolve(false)),
    getCurrentUser: jest.fn(() => Promise.resolve(null)),
    signInSilently: jest.fn(() => Promise.resolve(null)),
  },
  statusCodes: {
    SIGN_IN_CANCELLED: 'SIGN_IN_CANCELLED',
    IN_PROGRESS: 'IN_PROGRESS',
    PLAY_SERVICES_NOT_AVAILABLE: 'PLAY_SERVICES_NOT_AVAILABLE',
  },
}));

// Mock Supabase client with proper chaining
const createMockQueryBuilder = () => {
  const mockQueryBuilder = {
    select: jest.fn(() => mockQueryBuilder),
    insert: jest.fn(() => mockQueryBuilder),
    update: jest.fn(() => mockQueryBuilder),
    delete: jest.fn(() => mockQueryBuilder),
    eq: jest.fn(() => mockQueryBuilder),
    neq: jest.fn(() => mockQueryBuilder),
    gt: jest.fn(() => mockQueryBuilder),
    gte: jest.fn(() => mockQueryBuilder),
    lt: jest.fn(() => mockQueryBuilder),
    lte: jest.fn(() => mockQueryBuilder),
    like: jest.fn(() => mockQueryBuilder),
    ilike: jest.fn(() => mockQueryBuilder),
    is: jest.fn(() => mockQueryBuilder),
    in: jest.fn(() => mockQueryBuilder),
    contains: jest.fn(() => mockQueryBuilder),
    containedBy: jest.fn(() => mockQueryBuilder),
    rangeGt: jest.fn(() => mockQueryBuilder),
    rangeGte: jest.fn(() => mockQueryBuilder),
    rangeLt: jest.fn(() => mockQueryBuilder),
    rangeLte: jest.fn(() => mockQueryBuilder),
    rangeAdjacent: jest.fn(() => mockQueryBuilder),
    overlaps: jest.fn(() => mockQueryBuilder),
    textSearch: jest.fn(() => mockQueryBuilder),
    match: jest.fn(() => mockQueryBuilder),
    not: jest.fn(() => mockQueryBuilder),
    or: jest.fn(() => mockQueryBuilder),
    filter: jest.fn(() => mockQueryBuilder),
    order: jest.fn(() => mockQueryBuilder),
    limit: jest.fn(() => mockQueryBuilder),
    range: jest.fn(() => mockQueryBuilder),
    abortSignal: jest.fn(() => mockQueryBuilder),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    csv: jest.fn().mockResolvedValue({ data: '', error: null }),
    geojson: jest.fn().mockResolvedValue({ data: null, error: null }),
    explain: jest.fn().mockResolvedValue({ data: null, error: null }),
    rollback: jest.fn().mockResolvedValue({ data: null, error: null }),
    returns: jest.fn(() => mockQueryBuilder),
    then: jest.fn((resolve) => resolve({ data: [], error: null })),
    catch: jest.fn(),
  };
  return mockQueryBuilder;
};

const mockSupabase = {
  auth: {
    signInWithIdToken: jest.fn().mockResolvedValue({ data: null, error: null }),
    signOut: jest.fn().mockResolvedValue({ error: null }),
    getSession: jest
      .fn()
      .mockResolvedValue({ data: { session: null }, error: null }),
    onAuthStateChange: jest.fn(() => ({
      data: { subscription: { unsubscribe: jest.fn() } },
    })),
    getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
  },
  storage: {
    from: jest.fn(() => ({
      upload: jest.fn().mockResolvedValue({ data: { path: 'test-path' }, error: null }),
      remove: jest.fn().mockResolvedValue({ data: null, error: null }),
      getPublicUrl: jest.fn().mockReturnValue({ data: { publicUrl: 'http://public.url/file.txt' } }),
    })),
  },
  from: jest.fn(() => createMockQueryBuilder()),
  rpc: jest.fn().mockResolvedValue({ data: null, error: null }),
};

jest.mock("@supabase/supabase-js", () => ({
  createClient: jest.fn(() => mockSupabase),
}));

// Use react-native preset's built-in mocks

// Mock react-native completely to avoid dependency issues
jest.mock('react-native', () => {
  const React = require('react');

  // Create a generic mock component that correctly handles `ref`
  const MockComponent = React.forwardRef((props, ref) => {
    return React.createElement('View', {
      ...props,
      ref,
      testID: props.testID || props.accessibilityLabel || 'mock-component',
      accessibilityLabel: props.accessibilityLabel || props.testID || 'mock-component',
      accessibilityRole: props.accessibilityRole,
      style: props.style,
    }, props.children);
  });

  // Add mock properties to the component itself for easier mocking in tests
  Object.assign(MockComponent, {
    mockClear: jest.fn(),
    mockReset: jest.fn(),
    mockRestore: jest.fn(),
    getMockName: jest.fn(() => 'jest.fn(MockComponent)'),
    mockImplementation: jest.fn((fn) => {
      MockComponent.mockImplementationOnce(fn);
      return MockComponent;
    }),
    mockImplementationOnce: jest.fn((fn) => {
      MockComponent.mockImplementationOnce(fn);
      return MockComponent;
    }),
    mockReturnValue: jest.fn((value) => {
      MockComponent.mockReturnValueOnce(value);
      return MockComponent;
    }),
    mockReturnValueOnce: jest.fn((value) => {
      MockComponent.mockReturnValueOnce(value);
      return MockComponent;
    }),
    mockResolvedValue: jest.fn((value) => {
      MockComponent.mockResolvedValueOnce(value);
      return MockComponent;
    }),
    mockResolvedValueOnce: jest.fn((value) => {
      MockComponent.mockResolvedValueOnce(value);
      return MockComponent;
    }),
    mockRejectedValue: jest.fn((value) => {
      MockComponent.mockRejectedValueOnce(value);
      return MockComponent;
    }),
    mockRejectedValueOnce: jest.fn((value) => {
      MockComponent.mockRejectedValueOnce(value);
      return MockComponent;
    }),
  });

  return {
    StyleSheet: {
      create: (styles) => styles,
      flatten: jest.fn(),
      absoluteFillObject: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
      },
    },
    View: MockComponent,
    Text: MockComponent,
    TouchableOpacity: MockComponent,
    ScrollView: MockComponent,
    TextInput: MockComponent,
    Image: MockComponent,
    ActivityIndicator: MockComponent,
    FlatList: MockComponent,
    RefreshControl: MockComponent,
    StatusBar: MockComponent,
    SafeAreaView: MockComponent,
    KeyboardAvoidingView: MockComponent,
    Modal: MockComponent,
    Pressable: MockComponent,
    Switch: MockComponent,
    Alert: {
      alert: jest.fn(),
    },
    Animated: {
      Value: jest.fn(() => ({
        setValue: jest.fn(),
        addListener: jest.fn(),
        removeListener: jest.fn(),
        removeAllListeners: jest.fn(),
        interpolate: jest.fn(() => "0deg"),
      })),
      timing: jest.fn(() => ({
        start: jest.fn(callback => {
          if (callback) {
            callback({ finished: true });
          }
        }),
        stop: jest.fn(),
        reset: jest.fn(),
      })),
      spring: jest.fn(() => ({
        start: jest.fn(callback => {
          if (callback) {
            callback({ finished: true });
          }
        }),
        stop: jest.fn(),
        reset: jest.fn(),
      })),
      loop: jest.fn(() => ({
        start: jest.fn(),
        stop: jest.fn(),
        reset: jest.fn(),
      })),
      createAnimatedComponent: jest.fn(Component => Component),
    },
    Linking: {
      openURL: jest.fn(() => Promise.resolve()),
      canOpenURL: jest.fn(() => Promise.resolve(true)),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      getInitialURL: jest.fn(() => Promise.resolve(null)),
    },
    Platform: {
      OS: 'ios',
      select: jest.fn((obj) => obj.ios || obj.default),
    },
    Dimensions: {
      get: jest.fn(() => ({ width: 375, height: 812, scale: 2, fontScale: 1 })),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
    AppState: {
      currentState: 'active',
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
  };
});

jest.mock("@/lib/theme/colors", () => ({
  SCREEN_WIDTH: 375,
  BASE_WIDTH: 375,
}));

jest.mock("@expo/vector-icons", () => ({
  Ionicons: "Ionicons",
  MaterialCommunityIcons: "MaterialCommunityIcons",
  FontAwesome: "FontAwesome",
  // Add other icon sets as needed
}));

// Mock safe area context
jest.mock("react-native-safe-area-context", () => ({
  SafeAreaProvider: ({ children }) => children,
  SafeAreaView: ({ children }) => children,
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}));

// Mock Async Storage
let mockAsyncStorage = {};

jest.mock("@react-native-async-storage/async-storage", () => ({
  getItem: jest.fn((key) => Promise.resolve(mockAsyncStorage[key] || null)),
  setItem: jest.fn((key, value) => {
    mockAsyncStorage[key] = value;
    return Promise.resolve();
  }),
  removeItem: jest.fn((key) => {
    delete mockAsyncStorage[key];
    return Promise.resolve();
  }),
  clear: jest.fn(() => {
    mockAsyncStorage = {};
    return Promise.resolve();
  }),
}));

// Mock NetInfo
jest.mock("@/src/utils/networkStatus", () => ({
  isOnline: jest.fn(() => Promise.resolve(true)),
  isInternetReachable: jest.fn(() => Promise.resolve(true)),
  useNetworkStatus: jest.fn(() => ({
    isConnected: true,
    isInternetReachable: true,
    type: 'wifi',
  })),
}));

// Mock Toast utility
jest.mock("@/src/utils/toast", () => ({
  setGlobalToastInstance: jest.fn(),
  Toast: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn(),
    dismiss: jest.fn(),
  },
}));

jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn(),
    dismiss: jest.fn(),
  }),
  ToastProvider: ({ children }) => children,
}));

jest.mock('@/src/components/providers/AlertProvider', () => ({
  useAlertDialog: () => ({
    alert: jest.fn(),
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn(),
    confirm: jest.fn(),
    logout: jest.fn(),
    delete: jest.fn(),
  }),
  AlertProvider: ({ children }) => children,
}));

// react-native-svg is mocked via __mocks__/react-native-svg.js

// Helper function for creating mock icons
const createMockIcon = (name) => {
  const MockIcon = (props) => React.createElement('Text', { testID: `${name}-icon`, ...props }, name);
  MockIcon.displayName = `Mock${name}`;
  return MockIcon;
};

// Mock lucide-react-native
jest.mock('lucide-react-native', () => ({
  __esModule: true,
  default: {
    ArrowLeft: createMockIcon('ArrowLeft'),
    LogOut: createMockIcon('LogOut'),
    XCircle: createMockIcon('XCircle'),
    Camera: createMockIcon('Camera'),
    Loader2: createMockIcon('Loader2'),
  },
}));

// Mock Discovery Service
jest.mock("@/services/discovery/DiscoveryService", () => ({
  DiscoveryService: {
    getBusinessProfiles: jest.fn(),
    getBusinessProfile: jest.fn(),
    getProducts: jest.fn(),
    // Add other methods as needed
  },
}));

jest.mock("@/src/hooks/useTheme", () => ({
  useTheme: () => ({
    colors: {
      primary: "#D4AF37",
      secondary: "#666666",
      background: "#FFFFFF",
      cardBackground: "#F0F0F0",
      border: "#E5E5E5",
      textPrimary: "#000000",
      textSecondary: "#666666",
      destructive: "#FF0000",
      muted: "#F5F5F5",
      error: "#FF0000",
      shadow: "#000000",
      white: "#FFFFFF",
    },
    isDark: false,
    isLight: true,
    colorScheme: 'light',
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
      xxl: 48,
      xxxl: 64,
    },
    typography: {
      fontSize: {
        xs: 12,
        sm: 14,
        base: 16,
        lg: 18,
        xl: 20,
        xxl: 24,
      },
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },
      lineHeight: {
        normal: 1.4,
        relaxed: 1.6,
      },
    },
    borderRadius: {
      sm: 4,
      md: 8,
      lg: 12,
      xl: 16,
    },
    shadows: {
      sm: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 1,
      },
      md: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 2,
      },
      lg: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 6,
        elevation: 3,
      },
    },
    animations: {},
    breakpoints: {},
    brandColors: {
      gold: "#D4AF37",
    },
    getColor: jest.fn(),
    getSpacing: jest.fn(),
    getShadow: jest.fn(),
    getBorderRadius: jest.fn(),
    getFontSize: jest.fn(),
    getFontWeight: jest.fn(),
    getLineHeight: jest.fn(),
  }),
}));

// Global test environment setup
global.__DEV__ = true;

// Reset all mocks before each test
beforeEach(() => {
  // Clear storage mocks
  mockAsyncStorage = {};
  mockSecureStore = {};

  // Reset Supabase mock
  Object.keys(mockSupabase).forEach((key) => {
    if (
      typeof mockSupabase[key] === "function" &&
      mockSupabase[key].mockClear
    ) {
      mockSupabase[key].mockClear();
    }
  });
  Object.keys(mockSupabase.auth).forEach((key) => {
    if (
      typeof mockSupabase.auth[key] === "function" &&
      mockSupabase.auth[key].mockClear
    ) {
      mockSupabase.auth[key].mockClear();
    }
  });

  // Restore default mock implementations
  mockSupabase.from.mockImplementation(() => mockSupabase.from);
  mockSupabase.auth.getSession.mockResolvedValue({
    data: { session: null },
    error: null,
  });
  mockSupabase.auth.getUser.mockResolvedValue({
    data: { user: null },
    error: null,
  });
  mockSupabase.auth.onAuthStateChange.mockReturnValue({
    data: { subscription: { unsubscribe: jest.fn() } },
  });
  mockSupabase.rpc.mockResolvedValue({ data: null, error: null });
});

// Silence console warnings during tests
const originalWarn = console.warn;
console.warn = (...args) => {
  if (
    typeof args[0] === "string" &&
    args[0].includes("Warning: ReactDOM.render is no longer supported")
  ) {
    return;
  }
  originalWarn.call(console, ...args);
};

// Note: Fake timers removed as they interfere with async operations in tests
// Individual tests can use jest.useFakeTimers() if needed
