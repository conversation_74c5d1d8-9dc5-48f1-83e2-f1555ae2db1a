import { getSecureBusinessProfilesForDiscover, getSecureBusinessProfileIdsForDiscover } from '@/lib/actions/businessProfiles/discovery';
import { createClient } from '@/utils/supabase/server';
import { applySorting } from '@/lib/actions/businessProfiles/utils';

// Mock external dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));
jest.mock('@/lib/actions/businessProfiles/utils', () => ({
  applySorting: jest.fn((query) => query), // Mock applySorting to return the query as is
}));

describe('Business Profile Discovery Actions', () => {
  let mockSupabase: any;
  let mockFrom: jest.Mock;
  let mockSelect: jest.Mock;
  let mockEq: jest.Mock;
  let mockIn: jest.Mock;
  let mockRange: jest.Mock;
  let mockMaybeSingle: jest.Mock;

  beforeEach(() => {
    mockMaybeSingle = jest.fn();
    mockRange = jest.fn();
    mockEq = jest.fn();
    mockIn = jest.fn();
    mockSelect = jest.fn();
    mockFrom = jest.fn();

    const mockQueryBuilder = {
      eq: mockEq.mockReturnThis(),
      in: mockIn.mockReturnThis(),
      range: mockRange,
      maybeSingle: mockMaybeSingle,
      select: mockSelect.mockReturnThis(),
    };

    mockFrom.mockReturnValue(mockQueryBuilder);
    (createClient as jest.Mock).mockReturnValue(mockSupabase);

    (applySorting as jest.Mock).mockImplementation((queryBuilder) => queryBuilder);

    // Suppress console.error for cleaner test output
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getSecureBusinessProfilesForDiscover', () => {
    it('should return an error if pincodes is empty', async () => {
      const result = await getSecureBusinessProfilesForDiscover([]);
      expect(result).toEqual({ error: 'At least one pincode is required.' });
      expect(createClient).not.toHaveBeenCalled();
    });

    it('should return an error if count query fails', async () => {
      mockSelect.mockImplementationOnce(() => ({
        then: (callback: (value: any) => any) => Promise.resolve({ count: null, error: { message: 'Count Error' } }).then(callback),
      }));

      const result = await getSecureBusinessProfilesForDiscover(['12345']);
      expect(result).toEqual({ error: 'Database error counting profiles.' });
    });

    it('should return empty data and count if no profiles are found', async () => {
      mockSelect.mockImplementationOnce(() => ({
        then: (callback: (value: any) => any) => Promise.resolve({ count: 0, error: null }).then(callback),
      }));
      mockRange.mockResolvedValueOnce({ data: [], error: null });

      const result = await getSecureBusinessProfilesForDiscover(['12345']);
      expect(result).toEqual({ data: [], count: 0 });
    });

    it('should successfully fetch profiles with default parameters', async () => {
      const mockProfiles = [{ id: '1', business_slug: 'biz1' }];
      mockSelect.mockImplementationOnce(() => ({
        then: (callback: (value: any) => any) => Promise.resolve({ count: 1, error: null }).then(callback),
      }));
      mockRange.mockResolvedValueOnce({ data: mockProfiles, error: null });

      const result = await getSecureBusinessProfilesForDiscover(['12345']);
      expect(result.data).toHaveLength(1);
      expect(result.count).toBe(1);
      expect(applySorting).toHaveBeenCalled();
    });

    it('should successfully fetch profiles with locality filter', async () => {
      const mockProfiles = [{ id: '1', business_slug: 'biz1', locality: 'Loc1' }];
      mockSelect.mockImplementationOnce(() => ({
        then: (callback: (value: any) => any) => Promise.resolve({ count: 1, error: null }).then(callback),
      }));
      mockRange.mockResolvedValueOnce({ data: mockProfiles, error: null });

      const result = await getSecureBusinessProfilesForDiscover(['12345'], 'Loc1');
      expect(result.data).toHaveLength(1);
      expect(mockEq).toHaveBeenCalledWith('locality', 'Loc1');
    });

    it('should successfully fetch profiles with custom page and limit', async () => {
      const mockProfiles = [{ id: '1', business_slug: 'biz1' }];
      mockSelect.mockImplementationOnce(() => ({
        then: (callback: (value: any) => any) => Promise.resolve({ count: 10, error: null }).then(callback),
      }));
      mockRange.mockResolvedValueOnce({ data: mockProfiles, error: null });

      const result = await getSecureBusinessProfilesForDiscover(['12345'], null, 2, 5);
      expect(mockRange).toHaveBeenCalledWith(5, 9);
    });

    it('should handle unexpected errors during execution', async () => {
      mockFrom.mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });

      const result = await getSecureBusinessProfilesForDiscover(['12345']);
      expect(result).toEqual({ error: 'An unexpected error occurred.' });
    });
  });

  describe('getSecureBusinessProfileIdsForDiscover', () => {
    it('should return an error if pincodes is empty', async () => {
      const result = await getSecureBusinessProfileIdsForDiscover([]);
      expect(result).toEqual({ error: 'At least one pincode is required.' });
      expect(createClient).not.toHaveBeenCalled();
    });

    it('should return an error if Supabase query fails', async () => {
      mockSelect.mockResolvedValueOnce({ data: null, error: { message: 'DB Error' } });

      const result = await getSecureBusinessProfileIdsForDiscover(['12345']);
      expect(result).toEqual({ error: 'Database error fetching profile IDs.' });
    });

    it('should return empty data if no profile IDs are found', async () => {
      mockSelect.mockResolvedValueOnce({ data: [], error: null });

      const result = await getSecureBusinessProfileIdsForDiscover(['12345']);
      expect(result).toEqual({ data: [] });
    });

    it('should successfully fetch profile IDs with default sorting', async () => {
      const mockProfileIds = [{ id: 'id1' }, { id: 'id2' }];
      mockSelect.mockResolvedValueOnce({ data: mockProfileIds, error: null });

      const result = await getSecureBusinessProfileIdsForDiscover(['12345']);
      expect(result).toEqual({ data: ['id1', 'id2'] });
      expect(applySorting).toHaveBeenCalled();
    });

    it('should successfully fetch profile IDs with locality filter', async () => {
      const mockProfileIds = [{ id: 'id1' }];
      mockSelect.mockResolvedValueOnce({ data: mockProfileIds, error: null });

      const result = await getSecureBusinessProfileIdsForDiscover(['12345'], 'LocalityA');
      expect(result).toEqual({ data: ['id1'] });
      expect(mockEq).toHaveBeenCalledWith('locality', 'LocalityA');
    });

    it('should handle unexpected errors during execution', async () => {
      mockFrom.mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });

      const result = await getSecureBusinessProfileIdsForDiscover(['12345']);
      expect(result).toEqual({ error: 'An unexpected error occurred.' });
    });
  });
});