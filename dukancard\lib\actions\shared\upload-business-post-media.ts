import { createClient } from "@/utils/supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";

import { getPostImagePath } from "@/lib/utils/storage-paths";

export interface BusinessPostMediaUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * Upload and process image for business post
 * Future-proof structure: {userId}/business-posts/{year}/{month}/{postId}/image_0_{timestamp}.webp
 */
export async function uploadBusinessPostImage(
  formData: FormData,
  postId: string,
  postCreatedAt?: string
): Promise<BusinessPostMediaUploadResult> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const userId = user.id;
  const imageFile = formData.get("imageFile") as File | null;

  if (!imageFile) {
    return { success: false, error: "No image file provided." };
  }

  // Validate file size (15MB max)
  const maxFileSize = 15 * 1024 * 1024; // 15MB
  if (imageFile.size > maxFileSize) {
    const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(1);
    return {
      success: false,
      error: `File size (${fileSizeMB}MB) exceeds the 15MB limit.`
    };
  }

  // Validate file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
  if (!allowedTypes.includes(imageFile.type)) {
    return {
      success: false,
      error: "Invalid file type. Please select JPG, PNG, WebP, or GIF images."
    };
  }

  // Validate file name
  if (!imageFile.name || imageFile.name.trim() === '') {
    return {
      success: false,
      error: "Invalid file name."
    };
  }

  // Check if user has a business profile
  const { data: businessProfile, error: profileError } = await supabase
    .from('business_profiles')
    .select('id')
    .eq('id', userId)
    .single();

  if (profileError || !businessProfile) {
    return {
      success: false,
      error: "Business profile not found. Please complete your business profile first."
    };
  }

  try {
    // Create scalable path structure for billions of users
    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const bucketName = "business";
    const imagePath = getPostImagePath(userId, postId, 0, timestamp, postCreatedAt);

    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    // Use admin client for storage operations to bypass RLS
    const adminSupabase = await createClient() as SupabaseClient<Database>;

    // Upload to Supabase Storage using admin client
    const { error: uploadError } = await adminSupabase.storage
      .from(bucketName)
      .upload(imagePath, fileBuffer, {
        contentType: imageFile.type, // Use original file type (already compressed)
        upsert: true
      });

    if (uploadError) {
      console.error("Business Post Image Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError.message}`,
      };
    }

    // Get the public URL using admin client
    const { data: urlData } = adminSupabase.storage
      .from(bucketName)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    return {
      success: true,
      url: urlData.publicUrl,
    };

  } catch (error) {
    console.error("Error processing business post image:", error);
    return {
      success: false,
      error: "Failed to process image. Please try a different image."
    };
  }
}
