"use server";

import { createClient } from "@/utils/supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";
import { getGalleryLimit } from "@/app/(dashboard)/dashboard/business/gallery/utils";
import { Tables } from "@/types/supabase";

type BusinessProfiles = Tables<'business_profiles'>;
type PaymentSubscriptions = Tables<'payment_subscriptions'>;

export interface GalleryImage {
  id: string;
  url: string; // Public URL for display
  path: string; // Storage path
  created_at: string;
}

/**
 * Get gallery images for a specific business
 * @param businessId The business ID
 * @returns Gallery images
 */
export async function getBusinessGalleryImages(businessId: string): Promise<{
  images: GalleryImage[];
  error?: string;
}> {
  const supabase = (await createClient()) as SupabaseClient<Database>;

  try {
    const { data: profileData, error } = await supabase
      .from("business_profiles")
      .select("gallery")
      .eq("id", businessId)
      .single<Pick<BusinessProfiles, "gallery">>();

    if (error) {
      console.error("Error fetching business profile:", error);
      return {
        images: [],
        error: `Failed to fetch gallery images: ${error.message}`,
      };
    }

    const gallery = profileData?.gallery || [];
    const images = Array.isArray(gallery) ? gallery : [];

    // Sort by created_at in descending order
    images.sort((a, b) => {
      return (
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    });

    return { images };
  } catch (error) {
    console.error("Unexpected error fetching gallery images:", error);
    return {
      images: [],
      error: `An unexpected error occurred: ${
        error instanceof Error ? error.message : String(error)
      }`,
    };
  }
}

/**
 * Get gallery images for a business by slug (limited to 4 for gallery tab)
 * @param businessSlug The business slug
 * @returns Gallery images (max 4) and total count
 */
export async function getBusinessGalleryImagesForTab(
  businessSlug: string
): Promise<{
  images: GalleryImage[];
  totalCount: number;
  error?: string;
}> {
  const supabase = (await createClient()) as SupabaseClient<Database>;

  try {
    // First, get the business ID from the slug
    const { data: business, error: businessError } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("business_slug", businessSlug)
      .eq("status", "online")
      .single<Pick<BusinessProfiles, "id">>();

    if (businessError || !business) {
      console.error("Error fetching business profile:", businessError);
      return { images: [], totalCount: 0, error: "Business not found" };
    }

    // Get the business's current plan from payment_subscriptions
    const { data: subscription, error: subscriptionError } = await supabase
      .from("public_subscription_status")
      .select("plan_id")
      .eq("business_profile_id", business.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle<Pick<PaymentSubscriptions, "plan_id">>();

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
      // Continue with default free plan if there's an error
    }

    // Default to free plan if no subscription found
    const planId = subscription?.plan_id || "free";

    // Get the gallery limit for the current plan
    const galleryLimit = getGalleryLimit(planId);

    // Get the business profile with gallery data
    const { data: profileData, error: galleryError } = await supabase
      .from("business_profiles")
      .select("gallery")
      .eq("id", business.id)
      .single<Pick<BusinessProfiles, "gallery">>();

    if (galleryError) {
      console.error("Error fetching business gallery:", galleryError);
      return {
        images: [],
        totalCount: 0,
        error: `Failed to fetch gallery images: ${galleryError.message}`,
      };
    }

    // Parse gallery data
    const gallery = profileData?.gallery || [];
    const images = Array.isArray(gallery) ? gallery : [];

    // Sort by created_at in descending order (newest first)
    images.sort((a, b) => {
      return (
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    });

    // Apply plan limit first, then limit to max 4 for gallery tab
    // This ensures we respect both plan restrictions and gallery tab display limits
    const planLimitedImages = images.slice(0, galleryLimit);
    const finalLimit = Math.min(planLimitedImages.length, 4);
    const limitedImages = planLimitedImages.slice(0, finalLimit);

    // Total count should also respect the plan limit, not show all images
    const totalCount = planLimitedImages.length;

    return { images: limitedImages, totalCount };
  } catch (error) {
    console.error("Unexpected error fetching gallery images for tab:", error);
    return {
      images: [],
      totalCount: 0,
      error: `An unexpected error occurred: ${
        error instanceof Error ? error.message : String(error)
      }`,
    };
  }
}

/**
 * Get gallery images for a business by slug
 * @param businessSlug The business slug
 * @returns Gallery images
 */
export async function getBusinessGalleryImagesBySlug(
  businessSlug: string
): Promise<{
  images: GalleryImage[];
  error?: string;
}> {
  const supabase = (await createClient()) as SupabaseClient<Database>;

  try {
    // First, get the business ID from the slug
    const { data: business, error: businessError } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("business_slug", businessSlug)
      .eq("status", "online")
      .single<Pick<BusinessProfiles, "id">>();

    if (businessError || !business) {
      console.error("Error fetching business profile:", businessError);
      return { images: [], error: "Business not found" };
    }

    // Get the business's current plan from payment_subscriptions
    const { data: subscription, error: subscriptionError } = await supabase
      .from("public_subscription_status")
      .select("plan_id")
      .eq("business_profile_id", business.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle<Pick<PaymentSubscriptions, "plan_id">>();

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
      // Continue with default free plan if there's an error
    }

    // Default to free plan if no subscription found
    const planId = subscription?.plan_id || "free";

    // Get the gallery limit for the current plan
    const galleryLimit = getGalleryLimit(planId);

    // Get the business profile with gallery data
    const { data: profileData, error: galleryError } = await supabase
      .from("business_profiles")
      .select("gallery")
      .eq("id", business.id)
      .single<Pick<BusinessProfiles, "gallery">>();

    if (galleryError) {
      console.error("Error fetching business gallery:", galleryError);
      return {
        images: [],
        error: `Failed to fetch gallery images: ${galleryError.message}`,
      };
    }

    // Parse gallery data
    const gallery = profileData?.gallery || [];
    const images = Array.isArray(gallery) ? gallery : [];

    // Sort by created_at in descending order (newest first)
    images.sort((a, b) => {
      return (
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    });

    // Limit the number of images based on the plan
    // This ensures we only return the number of images allowed by the plan
    const limitedImages = images.slice(0, galleryLimit);

    return { images: limitedImages };
  } catch (error) {
    console.error("Unexpected error fetching gallery images by slug:", error);
    return {
      images: [],
      error: `An unexpected error occurred: ${
        error instanceof Error ? error.message : String(error)
      }`,
    };
  }
}

/**
 * Get paginated gallery images for a business by slug
 * @param businessSlug The business slug
 * @param page Page number (1-based)
 * @param limit Number of images per page
 * @returns Paginated gallery images with metadata
 */
export async function getBusinessGalleryImagesPaginated(
  businessSlug: string,
  page: number = 1,
  limit: number = 20
): Promise<{
  images: GalleryImage[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  error?: string;
}> {
  const supabase = (await createClient()) as SupabaseClient<Database>;

  try {
    // First, get the business ID from the slug
    const { data: business, error: businessError } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("business_slug", businessSlug)
      .eq("status", "online")
      .single<Pick<BusinessProfiles, "id">>();

    if (businessError || !business) {
      console.error("Error fetching business profile:", businessError);
      return {
        images: [],
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        hasNextPage: false,
        hasPrevPage: false,
        error: "Business not found",
      };
    }

    // Get the business's current plan from payment_subscriptions
    const { data: subscription, error: subscriptionError } = await supabase
      .from("public_subscription_status")
      .select("plan_id")
      .eq("business_profile_id", business.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle<Pick<PaymentSubscriptions, "plan_id">>();

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
      // Continue with default free plan if there's an error
    }

    // Default to free plan if no subscription found
    const planId = subscription?.plan_id || "free";

    // Get the gallery limit for the current plan
    const galleryLimit = getGalleryLimit(planId);

    // Get the business profile with gallery data
    const { data: profileData, error: galleryError } = await supabase
      .from("business_profiles")
      .select("gallery")
      .eq("id", business.id)
      .single<Pick<BusinessProfiles, "gallery">>();

    if (galleryError) {
      console.error("Error fetching business gallery:", galleryError);
      return {
        images: [],
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        hasNextPage: false,
        hasPrevPage: false,
        error: `Failed to fetch gallery images: ${galleryError.message}`,
      };
    }

    // Parse gallery data
    const gallery = profileData?.gallery || [];
    const allImages = Array.isArray(gallery) ? gallery : [];

    // Sort by created_at in descending order (newest first)
    allImages.sort((a, b) => {
      return (
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    });

    // Apply plan limit first
    const planLimitedImages = allImages.slice(0, galleryLimit);
    const totalCount = planLimitedImages.length;

    // Calculate pagination
    const totalPages = Math.ceil(totalCount / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    // Get paginated images
    const paginatedImages = planLimitedImages.slice(startIndex, endIndex);

    return {
      images: paginatedImages,
      totalCount,
      totalPages,
      currentPage: page,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  } catch (error) {
    console.error("Unexpected error fetching paginated gallery images:", error);
    return {
      images: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: page,
      hasNextPage: false,
      hasPrevPage: false,
      error: `An unexpected error occurred: ${
        error instanceof Error ? error.message : String(error)
      }`,
    };
  }
}