/**
 * Business Profile Service for React Native
 *
 * Handles all business profile operations using direct Supabase client calls
 * with Row Level Security (RLS) policies for security.
 *
 * This service replaces the need for Next.js proxy API routes by leveraging:
 * - RLS policies for security (users can only access their own profiles)
 * - Public read access for profile discovery
 * - Direct Supabase client calls for better performance
 */

import { supabase } from "@/lib/supabase";
import { Tables, TablesInsert, TablesUpdate } from "../../../../src/types/supabase";

export type BusinessProfiles = Tables<"business_profiles">;
export type BusinessProfilesInsert = TablesInsert<"business_profiles">;
export type BusinessProfilesUpdate = TablesUpdate<"business_profiles">;

export interface ServiceResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Get the current user's business profile
 */
export async function getBusinessProfile(): Promise<
  ServiceResult<BusinessProfiles | null>
> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Authentication required" };
    }

    const { data: profile, error } = await supabase
      .from("business_profiles")
      .select("*")
      .eq("id", user.id)
      .maybeSingle();

    if (error) {
      console.error(
        "[BUSINESS_PROFILE_SERVICE] Error fetching profile:",
        error
      );
      return { success: false, error: "Failed to fetch business profile" };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error("[BUSINESS_PROFILE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Check if the current user has a business profile
 */
export async function checkBusinessProfileExists(): Promise<
  ServiceResult<boolean>
> {
  try {
    const result = await getBusinessProfile();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, data: !!result.data };
  } catch (error) {
    console.error(
      "[BUSINESS_PROFILE_SERVICE] Error checking profile existence:",
      error
    );
    return { success: false, error: "Failed to check profile existence" };
  }
}

/**
 * Create a new business profile
 * RLS policy ensures users can only create their own profile
 */
export async function createBusinessProfile(
  profileData: BusinessProfilesInsert,
  planId: string
): Promise<ServiceResult<BusinessProfiles>> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Authentication required" };
    }

    // Prepare the business profile data for the RPC
    const rpcBusinessData = {
      id: user.id,
      business_name: profileData.business_name || null,
      contact_email: profileData.contact_email || null,
      has_active_subscription: false, // Will be set by RPC based on plan
      member_name: profileData.member_name || null,
      title: profileData.title || null,
      phone: profileData.phone || null,
      business_slug: profileData.business_slug || null,
      business_category: profileData.business_category || null,
      address_line: profileData.address_line || null,
      pincode: profileData.pincode || null,
      city: profileData.city || null,
      state: profileData.state || null,
      locality: profileData.locality || null,
      status: profileData.status || "offline", // Default to offline if not provided
      latitude: profileData.latitude || null,
      longitude: profileData.longitude || null,
      // trial_end_date will be calculated by the RPC
    };

    // Prepare subscription data for the RPC
    const rpcSubscriptionData = {
      plan_id: planId,
      plan_cycle: "monthly", // Assuming monthly for initial onboarding
      subscription_status: planId === "free" ? "ACTIVE" : "TRIAL",
      ...(planId === "free" && { subscription_start_date: new Date().toISOString() }) // For free plan, set start date
    };

    // Call the atomic RPC function
    const { data: result, error: rpcError } = await supabase.rpc('create_business_profile_atomic', {
      p_business_data: rpcBusinessData,
      p_subscription_data: rpcSubscriptionData
    });

    if (rpcError) {
      console.error("[BUSINESS_PROFILE_SERVICE] RPC error creating business profile:", rpcError);
      return { success: false, error: "Failed to create business profile." };
    }

    if (!result?.success) {
      console.error("[BUSINESS_PROFILE_SERVICE] Business profile creation failed:", result?.error);
      if (result?.error_code === 'SLUG_EXISTS') {
        return {
          success: false,
          error: "This URL slug is already taken. Please choose another.",
        };
      }
      return { success: false, error: result?.error || "Failed to create business profile." };
    }

    // The RPC returns the created profile data on success
    return { success: true, data: result.profile };
  } catch (error) {
    console.error("[BUSINESS_PROFILE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Update the current user's business profile
 * RLS policy ensures users can only update their own profile
 */
export async function updateBusinessProfile(
  updates: BusinessProfilesUpdate
): Promise<ServiceResult<BusinessProfiles>> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Authentication required" };
    }

    // RLS policy will ensure user can only update their own profile
    const { data: profile, error } = await supabase
      .from("business_profiles")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("id", user.id)
      .select()
      .single();

    if (error) {
      console.error(
        "[BUSINESS_PROFILE_SERVICE] Error updating profile:",
        error
      );
      return { success: false, error: "Failed to update business profile" };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error("[BUSINESS_PROFILE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Get a business profile by ID (public read access)
 * Uses public RLS policy for read access
 */
export async function getBusinessProfileById(
  businessId: string
): Promise<ServiceResult<BusinessProfiles | null>> {
  try {
    const { data: profile, error } = await supabase
      .from("business_profiles")
      .select("*")
      .eq("id", businessId)
      .maybeSingle();

    if (error) {
      console.error(
        "[BUSINESS_PROFILE_SERVICE] Error fetching profile by ID:",
        error
      );
      return { success: false, error: "Failed to fetch business profile" };
    }

    return { success: true, data: profile };
  } catch (error) {
    console.error("[BUSINESS_PROFILE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Get multiple business profiles by IDs (public read access)
 * Uses public RLS policy for read access
 */
export async function getBusinessProfilesByIds(
  businessIds: string[]
): Promise<ServiceResult<BusinessProfiles[]>> {
  try {
    if (!Array.isArray(businessIds) || businessIds.length === 0) {
      return { success: true, data: [] };
    }

    const { data: profiles, error } = await supabase
      .from("business_profiles")
      .select("*")
      .in("id", businessIds);

    if (error) {
      console.error(
        "[BUSINESS_PROFILE_SERVICE] Error fetching profiles by IDs:",
        error
      );
      return { success: false, error: "Failed to fetch business profiles" };
    }

    return { success: true, data: profiles || [] };
  } catch (error) {
    console.error("[BUSINESS_PROFILE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Check if a business slug is available
 */
export async function checkSlugAvailability(
  slug: string
): Promise<ServiceResult<boolean>> {
  try {
    const { data: existing, error } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("business_slug", slug)
      .maybeSingle();

    if (error) {
      console.error(
        "[BUSINESS_PROFILE_SERVICE] Error checking slug availability:",
        error
      );
      return { success: false, error: "Failed to check slug availability" };
    }

    return { success: true, data: !existing };
  } catch (error) {
    console.error("[BUSINESS_PROFILE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Delete the current user's business profile
 * RLS policy ensures users can only delete their own profile
 */
export async function deleteBusinessProfile(): Promise<ServiceResult<void>> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Authentication required" };
    }

    // RLS policy will ensure user can only delete their own profile
    const { error } = await supabase
      .from("business_profiles")
      .delete()
      .eq("id", user.id);

    if (error) {
      console.error(
        "[BUSINESS_PROFILE_SERVICE] Error deleting profile:",
        error
      );
      return { success: false, error: "Failed to delete business profile" };
    }

    return { success: true };
  } catch (error) {
    console.error("[BUSINESS_PROFILE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}
