/**
 * Transaction utilities for webhook handlers
 * Ensures database consistency across multiple table updates
 */

import { createClient } from "@/utils/supabase/server";

/**
 * Webhook event priority mapping for handling out-of-order delivery
 * Higher numbers indicate higher priority (later events)
 */
const WEBHOOK_EVENT_PRIORITY = {
  'subscription.created': 1,
  'subscription.authenticated': 2,
  'subscription.activated': 3,
  'subscription.charged': 4,
  'subscription.pending': 2, // Can happen after authenticated
  'subscription.halted': 5,
  'subscription.cancelled': 10,
  'subscription.expired': 10,
  'subscription.completed': 10,
  'subscription.updated': 6,
  'payment.authorized': 2,
  'payment.captured': 3,
  'payment.failed': 5
} as const;

/**
 * Check if a webhook event should be processed based on event ordering
 * This prevents out-of-order webhook processing from causing inconsistencies
 */
export async function shouldProcessWebhookEvent(
  razorpay_subscription_id: string,
  event_type: string,
  webhook_timestamp: number
): Promise<{ shouldProcess: boolean; reason: string }> {
  const adminClient = await createClient();

  try {
    // Get the current subscription state
    const { data: subscription, error } = await adminClient
      .from("payment_subscriptions")
      .select("subscription_status, updated_at, plan_id")
      .eq("razorpay_subscription_id", razorpay_subscription_id)
      .maybeSingle();

    if (error) {
      console.error(`[WEBHOOK_ORDERING] Error checking subscription ${razorpay_subscription_id}:`, error);
      // If we can't check, allow processing to avoid blocking legitimate events
      return { shouldProcess: true, reason: "Unable to check current state, allowing processing" };
    }

    if (!subscription) {
      // New subscription, allow processing
      return { shouldProcess: true, reason: "New subscription, allowing processing" };
    }

    // Check if subscription is in terminal state
    const isTerminalState = subscription.plan_id === "free" ||
                           subscription.subscription_status === "cancelled" ||
                           subscription.subscription_status === "expired" ||
                           subscription.subscription_status === "completed";

    if (isTerminalState) {
      // For terminal states, only allow terminal events or events that can legitimately happen after termination
      const terminalEvents = ['subscription.cancelled', 'subscription.expired', 'subscription.completed'];
      const allowedAfterTerminal = ['subscription.authenticated']; // Trial users can authenticate after cancellation

      if (terminalEvents.includes(event_type) || allowedAfterTerminal.includes(event_type)) {
        return { shouldProcess: true, reason: `Terminal state allows ${event_type} event` };
      } else {
        return { shouldProcess: false, reason: `Subscription in terminal state, rejecting ${event_type} event` };
      }
    }

    // Check event priority
    const currentEventPriority = WEBHOOK_EVENT_PRIORITY[event_type as keyof typeof WEBHOOK_EVENT_PRIORITY] || 0;
    const currentStatePriority = WEBHOOK_EVENT_PRIORITY[`subscription.${subscription.subscription_status}` as keyof typeof WEBHOOK_EVENT_PRIORITY] || 0;

    // Allow processing if the new event has higher or equal priority
    if (currentEventPriority >= currentStatePriority) {
      return { shouldProcess: true, reason: `Event priority ${currentEventPriority} >= current state priority ${currentStatePriority}` };
    }

    // Check timestamp to handle same-priority events
    const subscriptionUpdatedAt = new Date(subscription.updated_at).getTime();
    const webhookTime = webhook_timestamp * 1000; // Convert to milliseconds

    // Allow if webhook is newer (with 5 minute tolerance for clock skew)
    const timeDiff = webhookTime - subscriptionUpdatedAt;
    if (timeDiff > -300000) { // 5 minutes tolerance
      return { shouldProcess: true, reason: `Webhook timestamp is recent enough (${timeDiff}ms difference)` };
    }

    return {
      shouldProcess: false,
      reason: `Event ${event_type} (priority ${currentEventPriority}) is older than current state ${subscription.subscription_status} (priority ${currentStatePriority})`
    };

  } catch (error) {
    console.error(`[WEBHOOK_ORDERING] Unexpected error:`, error);
    // If we can't check, allow processing to avoid blocking legitimate events
    return { shouldProcess: true, reason: "Error checking event order, allowing processing" };
  }
}

export interface SubscriptionUpdateData {
  subscription_id: string;
  business_profile_id: string;
  subscription_status: string;
  has_active_subscription: boolean;
  additional_data?: Record<string, unknown>;
}

/**
 * Update subscription and business profile in a transaction-like manner
 * This ensures consistency between payment_subscriptions and business_profiles tables
 */
export async function updateSubscriptionWithBusinessProfile(
  data: SubscriptionUpdateData
): Promise<{ success: boolean; message: string }> {
  const adminClient = await createClient();
  
  try {
    // First, update the subscription
    const { error: subscriptionError } = await adminClient
      .from('payment_subscriptions')
      .update({
        subscription_status: data.subscription_status,
        updated_at: new Date().toISOString(),
        ...data.additional_data
      })
      .eq('razorpay_subscription_id', data.subscription_id);

    if (subscriptionError) {
      console.error(`[TRANSACTION] Failed to update subscription ${data.subscription_id}:`, subscriptionError);
      return { 
        success: false, 
        message: `Failed to update subscription: ${subscriptionError.message}` 
      };
    }

    // ENHANCED: Use atomic RPC function instead of manual transaction handling
    const { data: atomicResult, error: atomicError } = await adminClient.rpc('update_subscription_atomic', {
      p_subscription_id: data.subscription_id,
      p_new_status: data.subscription_status,
      p_business_profile_id: data.business_profile_id,
      p_has_active_subscription: data.has_active_subscription,
      p_additional_data: data.additional_data || {},
      p_webhook_timestamp: null // Webhook timestamp not available in this context
    });

    if (atomicError || !atomicResult?.success) {
      console.error(`[TRANSACTION] Atomic RPC failed for subscription ${data.subscription_id}:`, atomicError || atomicResult?.error);
      return {
        success: false,
        message: `Atomic transaction failed: ${atomicError?.message || atomicResult?.error}`
      };
    }


    return { success: true, message: 'Atomic transaction completed successfully' };

  } catch (error) {
    console.error(`[TRANSACTION] Unexpected error:`, error);
    return { 
      success: false, 
      message: `Transaction failed: ${error instanceof Error ? error.message : String(error)}` 
    };
  }
}

/**
 * Verify subscription and business profile consistency
 * This function checks if the subscription status matches the business profile status
 */
export async function verifySubscriptionConsistency(
  subscription_id: string
): Promise<{ consistent: boolean; details: string }> {
  const adminClient = await createClient();

  try {
    // Get subscription data
    const { data: subscription, error: subError } = await adminClient
      .from('payment_subscriptions')
      .select('business_profile_id, subscription_status, plan_id')
      .eq('razorpay_subscription_id', subscription_id)
      .single();

    if (subError || !subscription) {
      return {
        consistent: false,
        details: `Subscription not found: ${subError?.message || 'Unknown error'}`
      };
    }

    // Get business profile data
    const { data: profile, error: profileError } = await adminClient
      .from('business_profiles')
      .select('has_active_subscription')
      .eq('id', subscription.business_profile_id)
      .single();

    if (profileError || !profile) {
      return {
        consistent: false,
        details: `Business profile not found: ${profileError?.message || 'Unknown error'}`
      };
    }

    // Check consistency using centralized logic
    const { SubscriptionStateManager } = await import('./subscription-state-manager');
    const shouldHaveActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(
      subscription.subscription_status,
      subscription.plan_id || 'free'
    );

    const isConsistent = profile.has_active_subscription === shouldHaveActiveSubscription;

    return {
      consistent: isConsistent,
      details: isConsistent
        ? 'Subscription and business profile are consistent'
        : `Inconsistency detected: subscription_status=${subscription.subscription_status}, has_active_subscription=${profile.has_active_subscription}`
    };

  } catch (error) {
    return {
      consistent: false,
      details: `Error checking consistency: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Atomically downgrade subscription to free plan with proper column clearing
 * This function handles the terminal state transition in a single atomic operation
 */
export async function downgradeToFreePlanAtomic(
  razorpay_subscription_id: string,
  business_profile_id: string,
  reason: 'cancelled' | 'expired' | 'completed'
): Promise<{ success: boolean; message: string }> {
  const adminClient = await createClient();
  const now = new Date().toISOString();

  try {

    // ENHANCED: Use atomic RPC function for transaction safety
    const { data: atomicResult, error: atomicError } = await adminClient.rpc('update_subscription_atomic', {
      p_subscription_id: razorpay_subscription_id,
      p_new_status: 'active', // Keep as active but downgrade to free plan
      p_business_profile_id: business_profile_id,
      p_has_active_subscription: false, // Free plan users don't have "active subscription"
      p_additional_data: {
        plan_id: "free",
        plan_cycle: "monthly",
        subscription_start_date: now,
        cancelled_at: now,
        // Clear all Razorpay-related fields in one operation
        razorpay_subscription_id: null,
        razorpay_customer_id: null,
        razorpay_plan_id: null, // Added to clear razorpay_plan_id
        last_payment_id: null,
        last_payment_date: null,
        last_payment_method: null,
        subscription_expiry_time: null,
        subscription_charge_time: null,
        cancellation_requested_at: null,
        cancellation_reason: null,
        subscription_paused_at: null,
        // Clear original plan tracking columns as well
        original_plan_id: null,
        original_plan_cycle: null
      },
      p_webhook_timestamp: null
    });

    if (atomicError || !atomicResult?.success) {
      console.error(`[ATOMIC_DOWNGRADE_ERROR] Atomic RPC failed for subscription ${razorpay_subscription_id}. Reason: ${reason}. Error:`, atomicError || atomicResult?.error);
      return {
        success: false,
        message: `Atomic downgrade failed: ${atomicError?.message || atomicResult?.error}`
      };
    }


    return {
      success: true,
      message: `Subscription downgraded to free plan due to ${reason}`
    };

  } catch (error) {
    console.error(`[ATOMIC_DOWNGRADE] Exception during downgrade for subscription ${razorpay_subscription_id}:`, error);
    return {
      success: false,
      message: `Exception during downgrade: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Atomically revert authenticated subscription to trial status with proper column clearing
 * This function handles the authenticated subscription cancellation by preserving the selected plan
 * and reverting to trial status, clearing all other columns except plan_id, plan_cycle, subscription_status, created_at, updated_at
 * NOTE: This function does NOT clear razorpay_subscription_id and razorpay_customer_id - those are cleared only after webhook confirmation
 */
export async function revertToTrialAtomic(
  razorpay_subscription_id: string,
  business_profile_id: string
): Promise<{ success: boolean; message: string }> {
  const adminClient = await createClient();

  try {
    // First, get the current subscription to preserve plan_id and plan_cycle
    const { data: currentSubscription, error: fetchError } = await adminClient
      .from("payment_subscriptions")
      .select("plan_id, plan_cycle")
      .eq("razorpay_subscription_id", razorpay_subscription_id)
      .maybeSingle();

    if (fetchError) {
      console.error(`[REVERT_TO_TRIAL] Error fetching current subscription ${razorpay_subscription_id}:`, fetchError);
      return {
        success: false,
        message: `Error fetching current subscription: ${fetchError.message}`
      };
    }

    if (!currentSubscription) {
      console.error(`[REVERT_TO_TRIAL] Subscription ${razorpay_subscription_id} not found in database`);
      return {
        success: false,
        message: `Subscription not found in database`
      };
    }

    // ENHANCED: Use atomic RPC function for transaction safety
    const { data: atomicResult, error: atomicError } = await adminClient.rpc('update_subscription_atomic', {
      p_subscription_id: razorpay_subscription_id,
      p_new_status: 'trial', // Revert to trial status
      p_business_profile_id: business_profile_id,
      p_has_active_subscription: false, // Trial users don't have "active subscription"
      p_additional_data: {
        // Preserve the selected plan but enforce monthly cycle
        plan_id: currentSubscription.plan_id,
        plan_cycle: 'monthly', // ENFORCED: Always set to monthly for trial users
        // Clear all other columns except the preserved ones and timestamps
        // IMPORTANT: Keep Razorpay IDs until webhook confirms cancellation
        // razorpay_subscription_id: PRESERVED until webhook confirmation
        // razorpay_customer_id: PRESERVED until webhook confirmation
        last_payment_id: null,
        last_payment_date: null,
        last_payment_method: null,
        subscription_start_date: null,
        subscription_expiry_time: null,
        subscription_charge_time: null,
        cancelled_at: null,
        cancellation_requested_at: null,
        cancellation_reason: null,
        subscription_paused_at: null,
        // Clear original plan tracking columns since reverting to trial
        original_plan_id: null,
        original_plan_cycle: null
        // CRITICAL SECURITY FIX: DO NOT null trial_end_date - preserve it to prevent trial exploitation
        // trial_end_date: PRESERVED to prevent users from repeatedly starting and canceling trials
      },
      p_webhook_timestamp: null
    });

    if (atomicError || !atomicResult?.success) {
      console.error(`[REVERT_TO_TRIAL] Atomic RPC failed for subscription ${razorpay_subscription_id}:`, atomicError || atomicResult?.error);
      return {
        success: false,
        message: `Atomic revert to trial failed: ${atomicError?.message || atomicResult?.error}`
      };
    }


    return {
      success: true,
      message: `Subscription reverted to trial status with plan ${currentSubscription.plan_id} and monthly cycle enforced - Razorpay IDs preserved until webhook confirmation`
    };

  } catch (error) {
    console.error(`[REVERT_TO_TRIAL] Exception during revert to trial for subscription ${razorpay_subscription_id}:`, error);
    return {
      success: false,
      message: `Exception during revert to trial: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Clear Razorpay IDs and other columns after successful webhook confirmation
 * This function is called only after receiving successful cancelled webhook from Razorpay
 * Clears all columns except id, business_profile_id, plan_id, plan_cycle, subscription_status, created_at, updated_at
 */
export async function clearRazorpayColumnsAfterCancellation(
  razorpay_subscription_id: string,
  business_profile_id: string
): Promise<{ success: boolean; message: string }> {
  const adminClient = await createClient();
  const now = new Date().toISOString();

  try {
    // Clear all columns except plan_id, plan_cycle, subscription_status, created_at, updated_at
    // Based on payment_subscriptions table schema
    // IMPORTANT: Use business_profile_id to identify the record since we're nulling razorpay_subscription_id
    const { error: updateError } = await adminClient
      .from("payment_subscriptions")
      .update({
        // Clear Razorpay IDs now that cancellation is confirmed
        razorpay_subscription_id: null,
        razorpay_customer_id: null,
        // Clear all other columns (already cleared in revertToTrialAtomic but ensuring consistency)
        subscription_start_date: null,
        subscription_expiry_time: null,
        subscription_charge_time: null,
        last_payment_id: null,
        last_payment_date: null,
        last_payment_method: null,
        cancellation_requested_at: null,
        cancellation_reason: null,
        subscription_paused_at: null,
        cancelled_at: null,
        last_webhook_timestamp: null,
        // Clear original plan tracking columns as well
        original_plan_id: null,
        original_plan_cycle: null,
        updated_at: now
      })
      .eq("business_profile_id", business_profile_id)
      .eq("razorpay_subscription_id", razorpay_subscription_id); // Double check to ensure we're updating the right record

    if (updateError) {
      console.error(`[CLEAR_RAZORPAY_COLUMNS] Error clearing Razorpay columns for subscription ${razorpay_subscription_id}:`, updateError);
      return {
        success: false,
        message: `Error clearing Razorpay columns: ${updateError.message}`
      };
    }


    return {
      success: true,
      message: `Razorpay IDs and other columns cleared after webhook confirmation`
    };

  } catch (error) {
    console.error(`[CLEAR_RAZORPAY_COLUMNS] Exception during column clearing for subscription ${razorpay_subscription_id}:`, error);
    return {
      success: false,
      message: `Exception during column clearing: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}
