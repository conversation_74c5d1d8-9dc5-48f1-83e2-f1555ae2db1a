"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Search,
  MessageCircle,
  Mail,
  Phone,
  ArrowRight,
  CreditCard,
  Package,
  Settings,
  BarChart3,
  Users,
  AlertCircle
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { siteConfig } from "@/lib/site-config";
import Link from "next/link";
import EnhancedSupportFAQ from "./components/EnhancedSupportFAQ";

// Define FAQ categories
const faqCategories = [
  { id: "general", label: "General" },
  { id: "account", label: "Account" },
  { id: "business-card", label: "Business Card" },
  { id: "products", label: "Products" },
  { id: "billing", label: "Billing" },
];

// Define FAQ data
const faqs = [
  {
    id: 1,
    question: "What is Dukancard?",
    answer: "Dukancard is a digital business card platform that helps small businesses, shops, and freelancers create an online presence. It allows you to showcase your products, services, and contact information in a professional digital format that can be shared via QR code or link.",
    category: "general",
  },
  {
    id: 2,
    question: "How do I create a Dukancard account?",
    answer: "To create a Dukancard account, click on the 'Sign Up' button on our homepage. You'll need to provide your email address and create a password. After verifying your email, you can choose your role (business or customer) and start setting up your digital business card.",
    category: "account",
  },
  {
    id: 3,
    question: "Can I customize my digital business card?",
    answer: "Yes, you can customize your digital business card with your business name, logo, contact information, address, bio, and social media links. You can also add products or services with descriptions, images, and pricing.",
    category: "business-card",
  },
  {
    id: 4,
    question: "How many products can I add to my digital card?",
    answer: "The number of products you can add depends on your subscription plan. The Basic plan allows up to 10 active products, while higher-tier plans will offer more product slots when they become available.",
    category: "products",
  },
  {
    id: 5,
    question: "How do I share my digital business card?",
    answer: "You can share your digital business card by sending your unique Dukancard URL (dukancard.in/your-business-name) or by sharing your QR code. The QR code can be displayed at your physical location or included in your marketing materials.",
    category: "business-card",
  },
  {
    id: 6,
    question: "What payment methods do you accept?",
    answer: "We accept all major credit cards, debit cards, UPI, and PayPal. All payments are processed securely through our payment partners.",
    category: "billing",
  },
  {
    id: 7,
    question: "Can I cancel my subscription anytime?",
    answer: "Yes, you can cancel your subscription at any time from your account settings. Your access will continue until the end of your current billing period.",
    category: "billing",
  },
  {
    id: 8,
    question: "Is there a free trial available?",
    answer: "Yes, we offer the first month free for all plans for first-time users.",
    category: "billing",
  },
  
  {
    id: 10,
    question: "Can I track how many people view my digital card?",
    answer: "Yes, our Basic Analytics feature allows you to track storefront views and contact button clicks (WhatsApp/Call). More advanced analytics features will be available in our upcoming premium plans.",
    category: "business-card",
  },
];

// Define support topics
const supportTopics = [
  {
    title: "Account & Billing",
    icon: <Users className="w-8 h-8 text-[var(--brand-gold)]" />,
    description: "Manage your account, subscription, and billing information.",
    url: "/support/account-billing",
  },
  {
    title: "Business Card Setup",
    icon: <CreditCard className="w-8 h-8 text-[var(--brand-gold)]" />,
    description: "Learn how to create and customize your digital business card.",
    url: "/support/business-card-setup",
  },
  {
    title: "Product Management",
    icon: <Package className="w-8 h-8 text-[var(--brand-gold)]" />,
    description: "Add, edit, and manage products in your digital storefront.",
    url: "/support/product-management",
  },
  {
    title: "Analytics & Insights",
    icon: <BarChart3 className="w-8 h-8 text-[var(--brand-gold)]" />,
    description: "Understand your visitor data and engagement metrics.",
    url: "/support/analytics",
  },
  {
    title: "Technical Issues",
    icon: <AlertCircle className="w-8 h-8 text-[var(--brand-gold)]" />,
    description: "Get help with technical problems and troubleshooting.",
    url: "/support/technical-issues",
  },
  {
    title: "Settings & Preferences",
    icon: <Settings className="w-8 h-8 text-[var(--brand-gold)]" />,
    description: "Configure your account settings and preferences.",
    url: "/support/settings",
  },
];

export default function SupportPageClient() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("general");

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="bg-background min-h-screen">
      {/* Hero Section */}
      <section className="relative pt-24 md:pt-32 pb-16 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-3/4 h-3/4 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="text-center max-w-3xl mx-auto">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6"
          >
            How Can We <span className="text-[var(--brand-gold)]">Help You?</span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-lg text-muted-foreground mb-8"
          >
            Find answers to common questions or get in touch with our support team.
          </motion.p>

          {/* Search Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative max-w-xl mx-auto mb-12"
          >
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-5 w-5" />
            <Input
              type="text"
              placeholder="Search for help..."
              className="pl-10 h-12 bg-background border-border"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </motion.div>
        </div>
      </section>

      {/* Support Topics Section */}
      <section className="py-12 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
            Browse Support Topics
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Select a topic to find detailed guides and solutions
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {supportTopics.map((topic, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Link href={topic.url} className="block h-full">
                <Card className="h-full bg-card border border-border hover:border-[var(--brand-gold)]/50 hover:shadow-md transition-all duration-300 cursor-pointer">
                  <CardHeader>
                    <div className="bg-[var(--brand-gold)]/10 p-3 rounded-lg w-fit">
                      {topic.icon}
                    </div>
                    <CardTitle className="text-xl mt-4">{topic.title}</CardTitle>
                    <CardDescription className="text-muted-foreground">
                      {topic.description}
                    </CardDescription>
                  </CardHeader>
                  <CardFooter className="pt-0">
                    <Button variant="ghost" className="p-0 hover:bg-transparent hover:text-[var(--brand-gold)] text-muted-foreground cursor-pointer">
                      Learn more <ArrowRight className="ml-2 w-4 h-4" />
                    </Button>
                  </CardFooter>
                </Card>
              </Link>
            </motion.div>
          ))}
        </motion.div>
      </section>

      {/* Enhanced FAQ Section */}
      <EnhancedSupportFAQ
        faqItems={faqs}
        faqCategories={faqCategories}
        searchQuery={searchQuery}
        activeCategory={activeCategory}
        onCategoryChange={setActiveCategory}
      />

      {/* Contact Support Section */}
      <section className="py-16 px-4 md:px-6 lg:px-8 max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/5 dark:from-[var(--brand-gold)]/20 dark:to-black/50 rounded-2xl p-8 md:p-12"
        >
          <div className="text-center mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
              Still Need Help?
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our support team is ready to assist you with any questions or issues
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-card border border-border hover:border-[var(--brand-gold)]/50 hover:shadow-md transition-all duration-300">
              <CardHeader>
                <div className="bg-[var(--brand-gold)]/10 p-3 rounded-lg w-fit">
                  <Mail className="w-6 h-6 text-[var(--brand-gold)]" />
                </div>
                <CardTitle className="text-xl mt-4">Email Support</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Send us an email and we&apos;ll respond within 24 hours
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button
                  asChild
                  variant="outline"
                  className="w-full border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 cursor-pointer"
                >
                  <Link href={`mailto:${siteConfig.support.email}`}>
                    Email Us
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            <Card className="bg-card border border-border hover:border-[var(--brand-gold)]/50 hover:shadow-md transition-all duration-300">
              <CardHeader>
                <div className="bg-[var(--brand-gold)]/10 p-3 rounded-lg w-fit">
                  <Phone className="w-6 h-6 text-[var(--brand-gold)]" />
                </div>
                <CardTitle className="text-xl mt-4">Phone Support</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Call us during business hours for immediate assistance
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button
                  asChild
                  variant="outline"
                  className="w-full border-[var(--brand-gold)] text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 cursor-pointer"
                >
                  <Link href={`tel:${siteConfig.support.phone}`}>
                    Call Us
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            <Card className="bg-card border border-border hover:border-[var(--brand-gold)]/50 hover:shadow-md transition-all duration-300">
              <CardHeader>
                <div className="bg-[var(--brand-gold)]/10 p-3 rounded-lg w-fit">
                  <MessageCircle className="w-6 h-6 text-[var(--brand-gold)]" />
                </div>
                <CardTitle className="text-xl mt-4">Help Center</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Browse our comprehensive knowledge base
                </CardDescription>
              </CardHeader>
              <CardFooter></CardFooter>
            </Card>
          </div>
        </motion.div>
      </section>
    </div>
  );
}
