import React from 'react';

export const Dialog = ({ children, open, onOpenChange }: any) => {
  if (!open) return null;
  return <div data-testid="dialog">{children}</div>;
};

export const DialogContent = ({ children, className }: any) => (
  <div data-testid="dialog-content" className={className}>
    {children}
  </div>
);

export const DialogHeader = ({ children, className }: any) => (
  <div data-testid="dialog-header" className={className}>
    {children}
  </div>
);

export const DialogTitle = ({ children, className }: any) => (
  <h2 data-testid="dialog-title" className={className}>
    {children}
  </h2>
);
