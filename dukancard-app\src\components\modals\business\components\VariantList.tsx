import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
  ActivityIndicator,
} from "react-native";
import { Edit3, Trash2, Plus, Package } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { useToast } from "@/src/components/ui/Toast";
import { Tables } from "@dukancard-types/supabase";
import { createManageProductsModalStyles } from "@/styles/modals/business/manage-products-modal";
import { ProductVariants, getProductVariants, deleteProductVariant } from "@/backend/supabase/services/business/variantService";

interface VariantListProps {
  productId: string;
  onAddVariant: () => void;
  onEditVariant: (variant: Tables<'product_variants'>) => void;
  refreshTrigger?: number; // To trigger refresh from parent
}

const VariantList: React.FC<VariantListProps> = ({
  productId,
  onAddVariant,
  onEditVariant,
  refreshTrigger,
}) => {
  const theme = useTheme();
  const toast = useToast();
  const styles = createManageProductsModalStyles(theme);
  
  const [variants, setVariants] = useState<Tables<'product_variants'>[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState<string | null>(null);

  // Load variants
  const loadVariants = useCallback(async () => {
    try {
      setLoading(true);
      const result = await getProductVariants(productId);

      if (result.success && result.data) {
        setVariants(result.data);
      } else {
        toast.error("Error", result.error || "Failed to load variants");
      }
    } catch (error) {
      toast.error("Error", "Failed to load variants");
    } finally {
      setLoading(false);
    }
  }, [productId, toast]);

  // Load variants on mount and when refreshTrigger changes
  useEffect(() => {
    loadVariants();
  }, [productId, refreshTrigger, loadVariants]);

  // Handle variant deletion
  const handleDeleteVariant = (variant: ProductVariants) => {
    Alert.alert(
      "Delete Variant",
      `Are you sure you want to delete "${variant.variant_name}"? This action cannot be undone.`,
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              setDeleting(variant.id);
              const result = await deleteProductVariant(variant.id);
              
              if (result.success) {
                toast.success("Success", "Variant deleted successfully");
                await loadVariants(); // Refresh the list
              } else {
                toast.error("Error", result.error || "Failed to delete variant");
              }
            } catch (error) {
              toast.error("Error", "Failed to delete variant");
            } finally {
              setDeleting(null);
            }
          },
        },
      ]
    );
  };

  // Format variant values for display
  const formatVariantValues = (values: Json) => {
    if (typeof values !== 'object' || values === null) {
      return "";
    }
    return Object.entries(values)
      .map(([key, value]) => `${key}: ${value}`)
      .join(", ");
  };

  // Format price for display
  const formatPrice = (price: number | null) => {
    return price ? `₹${price.toLocaleString("en-IN")}` : "Not set";
  };

  // Render variant item
  const renderVariantItem = ({ item }: { item: ProductVariants }) => (
    <View style={styles.variantItem}>
      {/* Variant Image */}
      <View style={styles.variantImageContainer}>
        {item.images && item.images.length > 0 ? (
          <Image
            source={{ uri: item.images[item.featured_image_index || 0] }}
            style={styles.variantImage}
          />
        ) : (
          <View style={styles.variantImagePlaceholder}>
            <Package size={24} color={theme.colors.mutedForeground} />
          </View>
        )}
      </View>

      {/* Variant Info */}
      <View style={styles.variantInfo}>
        <Text style={styles.variantName}>{item.variant_name}</Text>
        <Text style={styles.variantValues}>
          {formatVariantValues(item.variant_values)}
        </Text>
        
        <View style={styles.variantPricing}>
          {item.discounted_price && item.discounted_price < (item.base_price || 0) ? (
            <>
              <Text style={styles.variantDiscountedPrice}>
                {formatPrice(item.discounted_price)}
              </Text>
              <Text style={styles.variantOriginalPrice}>
                {formatPrice(item.base_price)}
              </Text>
            </>
          ) : (
            <Text style={styles.variantPrice}>
              {formatPrice(item.base_price)}
            </Text>
          )}
        </View>

        <View style={styles.variantStatus}>
          <View
            style={[
              styles.statusIndicator,
              {
                backgroundColor: item.is_available
                  ? theme.colors.success
                  : theme.colors.destructive,
              },
            ]}
          />
          <Text style={styles.statusText}>
            {item.is_available ? "Available" : "Unavailable"}
          </Text>
        </View>
      </View>

      {/* Actions */}
      <View style={styles.variantActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => onEditVariant(item)}
          disabled={deleting === item.id}
        >
          <Edit3 size={16} color={theme.colors.primary} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleDeleteVariant(item)}
          disabled={deleting === item.id}
        >
          {deleting === item.id ? (
            <ActivityIndicator size="small" color={theme.colors.destructive} />
          ) : (
            <Trash2 size={16} color={theme.colors.destructive} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Package size={48} color={theme.colors.mutedForeground} />
      <Text style={styles.emptyStateTitle}>No Variants</Text>
      <Text style={styles.emptyStateDescription}>
        Add variants to offer different options for this product
      </Text>
      <TouchableOpacity style={styles.emptyStateButton} onPress={onAddVariant}>
        <Plus size={16} color={theme.colors.primaryForeground} />
        <Text style={styles.emptyStateButtonText}>Add First Variant</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Loading variants...</Text>
      </View>
    );
  }

  return (
    <View style={styles.variantListContainer}>
      {/* Header */}
      <View style={styles.variantListHeader}>
        <Text style={styles.variantListTitle}>Product Variants</Text>
        <TouchableOpacity style={styles.addVariantButton} onPress={onAddVariant}>
          <Plus size={16} color={theme.colors.primaryForeground} />
          <Text style={styles.addVariantButtonText}>Add Variant</Text>
        </TouchableOpacity>
      </View>

      {/* Variants List */}
      {variants.length > 0 ? (
        <FlatList
          data={variants}
          keyExtractor={(item) => item.id}
          renderItem={renderVariantItem}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.variantsList}
        />
      ) : (
        renderEmptyState()
      )}
    </View>
  );
};

export default VariantList;
