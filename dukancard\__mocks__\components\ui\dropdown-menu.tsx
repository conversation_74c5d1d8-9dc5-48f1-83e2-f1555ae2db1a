import React from 'react';

export const DropdownMenu = ({ children }: any) => (
  <div data-testid="dropdown-menu">{children}</div>
);

export const DropdownMenuTrigger = ({ children, asChild }: any) => {
  if (asChild) {
    return React.cloneElement(children);
  }
  return <div data-testid="dropdown-menu-trigger">{children}</div>;
};

export const DropdownMenuContent = ({ children }: any) => (
  <div data-testid="dropdown-menu-content">{children}</div>
);

export const DropdownMenuLabel = ({ children }: any) => (
  <div data-testid="dropdown-menu-label">{children}</div>
);

export const DropdownMenuSeparator = () => (
  <div data-testid="dropdown-menu-separator" />
);

export const DropdownMenuItem = ({ children, onClick }: any) => (
  <div data-testid="dropdown-menu-item" onClick={onClick}>
    {children}
  </div>
);
