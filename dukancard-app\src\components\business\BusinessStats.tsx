import React from "react";
import { View, Text, TouchableOpacity, Linking } from "react-native";
import {
  Heart,
  Users,
  Star,
  Phone,
  MessageCircle,
  Instagram,
  Facebook,
  Navigation,
} from "lucide-react-native";
import { AnimatedLoader } from "../ui/AnimatedLoader";
import { BusinessDiscoveryData } from "@/src/types/business";
import { BusinessInteractionStatus } from "@/backend/supabase/services/business/businessInteractions";
import { formatIndianNumberShort } from "@/lib/utils";
import { createPublicCardViewStyles } from "@/styles/PublicCardViewStyles";
import { useToast } from "@/src/components/ui/Toast";
import WhatsAppIcon from "../icons/WhatsAppIcon";

interface BusinessStatsProps {
  businessData: BusinessDiscoveryData;
  interactionStatus: BusinessInteractionStatus | null;
  isDark: boolean;
  // Interactive button props
  isOwner?: boolean;
  likeLoading?: boolean;
  subscribeLoading?: boolean;
  onLikePress?: () => void;
  onSubscribePress?: () => void;
  onReviewPress?: () => void;
  onDirectionsPress?: () => void;
}

export default function BusinessStats({
  businessData,
  interactionStatus,
  isDark,
  isOwner = false,
  likeLoading = false,
  subscribeLoading = false,
  onLikePress,
  onSubscribePress,
  onReviewPress,
  onDirectionsPress,
}: BusinessStatsProps) {
  const styles = createPublicCardViewStyles(isDark);
  const toast = useToast();

  // Contact action handlers
  const handlePhonePress = () => {
    if (businessData.phone) {
      Linking.openURL(`tel:${businessData.phone}`);
    }
  };

  const handleWhatsAppPress = () => {
    if (businessData.whatsapp_number) {
      const message = `Hi ${businessData.business_name}, I found you through Dukancard!`;
      const url = `whatsapp://send?phone=${
        businessData.whatsapp_number
      }&text=${encodeURIComponent(message)}`;
      Linking.openURL(url).catch(() => {
        toast.error("WhatsApp is not installed");
      });
    }
  };

  const handleInstagramPress = () => {
    if (businessData.instagram_url) {
      Linking.openURL(businessData.instagram_url).catch(() => {
        toast.error("Unable to open Instagram");
      });
    }
  };

  const handleFacebookPress = () => {
    if (businessData.facebook_url) {
      Linking.openURL(businessData.facebook_url).catch(() => {
        toast.error("Unable to open Facebook");
      });
    }
  };

  return (
    <View style={styles.enhancedStatsContainer}>
      {/* Metrics Row */}
      <View style={styles.metricsRow}>
        <View style={styles.statItem}>
          <Heart color={isDark ? "#D4AF37" : "#D4AF37"} size={20} />
          <Text style={styles.statNumber}>
            {formatIndianNumberShort(
              interactionStatus?.likeCount ?? businessData.total_likes ?? 0
            )}
          </Text>
          <Text style={styles.statLabel}>Likes</Text>
        </View>
        <View style={styles.statItem}>
          <Users color={isDark ? "#D4AF37" : "#D4AF37"} size={20} />
          <Text style={styles.statNumber}>
            {formatIndianNumberShort(
              interactionStatus?.subscriptionCount ??
                businessData.total_subscriptions ??
                0
            )}
          </Text>
          <Text style={styles.statLabel}>Followers</Text>
        </View>
        <View style={styles.statItem}>
          <Star color={isDark ? "#D4AF37" : "#D4AF37"} size={20} />
          <Text style={styles.statNumber}>
            {(
              interactionStatus?.averageRating ??
              businessData.average_rating ??
              0
            ).toFixed(1)}
          </Text>
          <Text style={styles.statLabel}>Rating</Text>
        </View>
      </View>

      {/* Interactive Buttons Row */}
      {!isOwner && (onLikePress || onSubscribePress || onReviewPress) && (
        <View style={styles.interactiveButtonsRow}>
          {onLikePress && (
            <TouchableOpacity
              style={[
                styles.compactInteractionButton,
                styles.likeButton,
                interactionStatus?.isLiked && styles.activeInteractionButton,
              ]}
              onPress={onLikePress}
              disabled={likeLoading}
            >
              {likeLoading ? (
                <AnimatedLoader size={14} color="#fff" />
              ) : (
                <Heart
                  size={14}
                  color="#fff"
                  fill={interactionStatus?.isLiked ? "#fff" : "transparent"}
                />
              )}
              <Text style={styles.compactButtonText}>
                {interactionStatus?.isLiked ? "Liked" : "Like"}
              </Text>
            </TouchableOpacity>
          )}

          {onSubscribePress && (
            <TouchableOpacity
              style={[
                styles.compactInteractionButton,
                styles.subscribeButton,
                interactionStatus?.isSubscribed &&
                  styles.activeInteractionButton,
              ]}
              onPress={onSubscribePress}
              disabled={subscribeLoading}
            >
              {subscribeLoading ? (
                <AnimatedLoader size={14} color="#fff" />
              ) : (
                <Users
                  size={14}
                  color="#fff"
                  fill={
                    interactionStatus?.isSubscribed ? "#fff" : "transparent"
                  }
                />
              )}
              <Text style={styles.compactButtonText}>
                {interactionStatus?.isSubscribed ? "Following" : "Follow"}
              </Text>
            </TouchableOpacity>
          )}

          {onReviewPress && (
            <TouchableOpacity
              style={[
                styles.compactInteractionButton,
                styles.reviewButton,
                !!interactionStatus?.userRating &&
                  styles.activeInteractionButton,
              ]}
              onPress={onReviewPress}
            >
              <Star
                size={14}
                color="#fff"
                fill={interactionStatus?.userRating ? "#fff" : "transparent"}
              />
              <Text style={styles.compactButtonText}>
                {interactionStatus?.userRating ? "Reviewed" : "Review"}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Contact & Social Buttons Row */}
      <View style={styles.contactSocialRow}>
        {/* Contact Buttons */}
        {(businessData.phone || businessData.whatsapp_number) && (
          <View style={styles.contactButtonsGroup}>
            {businessData.phone && (
              <TouchableOpacity
                style={styles.compactContactButton}
                onPress={handlePhonePress}
              >
                <Phone color="#fff" size={16} />
                <Text style={styles.compactButtonText}>Call</Text>
              </TouchableOpacity>
            )}
            {businessData.whatsapp_number && (
              <TouchableOpacity
                style={styles.compactWhatsAppButton}
                onPress={handleWhatsAppPress}
              >
                <WhatsAppIcon size={16} color="#fff" />
                <Text style={styles.compactButtonText}>WhatsApp</Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Social Buttons */}
        {(businessData.instagram_url || businessData.facebook_url) && (
          <View style={styles.socialButtonsGroup}>
            {businessData.instagram_url && (
              <TouchableOpacity
                style={styles.compactInstagramButton}
                onPress={handleInstagramPress}
              >
                <Instagram color="#fff" size={16} />
              </TouchableOpacity>
            )}
            {businessData.facebook_url && (
              <TouchableOpacity
                style={styles.compactFacebookButton}
                onPress={handleFacebookPress}
              >
                <Facebook color="#fff" size={16} />
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>

      {/* Get Directions Button */}
      {onDirectionsPress && businessData.latitude && businessData.longitude && (
        <View style={styles.directionsButtonContainer}>
          <TouchableOpacity
            style={styles.directionsButton}
            onPress={onDirectionsPress}
          >
            <Navigation color="#fff" size={18} />
            <Text style={styles.directionsButtonText}>Get Directions</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}
