import { RazorpayWebhookData } from "../../types/api";
import { SupabaseClient } from "@supabase/supabase-js";
import { SupabaseSubscriptionStatus } from "../types";
import { updateSubscription, extractWebhookTimestamp } from "./utils";
import { createClient } from "@/utils/supabase/server";
import { webhookProcessor, type WebhookProcessingContext } from "./webhookProcessor";

/**
 * Handle payment.authorized event
 *
 * This event is triggered when a payment is authorized.
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handlePaymentAuthorized(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  let context: WebhookProcessingContext | null = null;

  try {
    const payment = payload.payload.payment;
    if (!payment) {
      console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload");
      return { success: false, message: "Payment data not found in payload" };
    }

    const paymentId = payment.entity.id;
    console.log(`[RAZORPAY_WEBHOOK] Payment authorized: ${paymentId}`);

    // Check if this is a subscription payment
    const subscriptionId = payment.entity.notes?.subscription_id;
    if (!subscriptionId) {
  
      return { success: true, message: "Not a subscription payment, no update needed" };
    }

    // Extract webhook timestamp from Razorpay payload for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // Use centralized webhook processor for race condition protection
    context = {
      subscriptionId,
      eventType: 'payment.authorized',
      eventId: razorpayEventId || `payment_auth_${paymentId}_${Date.now()}`,
      payload: payload as unknown as Record<string, unknown>,
      webhookTimestamp
    };

    const processingResult = await webhookProcessor.processWebhookEvent(context);
    if (!processingResult.shouldProcess) {
      return { success: processingResult.success, message: processingResult.message };
    }

    // Check if this is a plan switch payment (has old_subscription_id in notes)
    const oldSubscriptionId = payment.entity.notes?.old_subscription_id;
    if (oldSubscriptionId) {
      console.log(`[RAZORPAY_WEBHOOK] Plan switch payment detected: ${oldSubscriptionId} -> ${subscriptionId}`);

      // We'll handle the cancellation of the old subscription in the subscription.authenticated webhook
      // This is to ensure we only cancel after the new subscription is properly authenticated
    }

    // Get subscription details from Razorpay to update all fields
    const { getSubscription } = await import("@/lib/razorpay/services/subscription");
    const subscriptionResult = await getSubscription(subscriptionId);

    if (!subscriptionResult.success || !subscriptionResult.data) {
      console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription details for ${subscriptionId}:`, subscriptionResult.error);
      // Continue anyway with the payment information we have
    }

    const subscription = subscriptionResult.data;

    // Update the subscription notes to include the payment ID
    // This will be used by the subscription.activated webhook to fetch the payment method
    try {
      // Import the updateSubscription function from Razorpay services
      const { updateSubscription: updateRazorpaySubscription } = await import("@/lib/razorpay/services/subscription");

      // Update the subscription notes in Razorpay
      const updateNotesResult = await updateRazorpaySubscription(subscriptionId, {
        notes: {
          ...(subscription?.notes || {}),
          last_payment_id: paymentId
        }
      });

      if (!updateNotesResult.success) {
        console.error(`[RAZORPAY_WEBHOOK] Error updating subscription notes for ${subscriptionId}:`, updateNotesResult.error);
        // Continue anyway - we'll still update our database
      } else {

      }
    } catch (updateError) {
      console.error(`[RAZORPAY_WEBHOOK] Error updating subscription notes:`, updateError);
      // Continue anyway - we'll still update our database
    }

    // Get admin client to bypass RLS
    const adminClient = createClient();

    // Update subscription with payment information
    // Note: Payment authorization doesn't mean the subscription is active yet
    // It's just authenticated, so we use _AUTHENTICATED status instead of _ACTIVE
    const updateResult = await updateSubscription(
      adminClient,
      subscriptionId,
      SupabaseSubscriptionStatus._AUTHENTICATED,
      {
        // Additional data to update
        last_payment_id: paymentId,
        // Removed last_payment_amount as requested
        last_payment_date: new Date(payment.entity.created_at * 1000).toISOString(),
        last_payment_method: payment.entity.method,
        // Clear cancellation_requested_at when payment is authorized
        cancellation_requested_at: null,
        // Update subscription details if we have them
        ...(subscription && {
          razorpay_customer_id: subscription.customer_id || null,
          subscription_start_date: subscription.current_start ? new Date(subscription.current_start * 1000).toISOString() : null,
          subscription_expiry_time: subscription.current_end ? new Date(subscription.current_end * 1000).toISOString() : null,
          subscription_charge_time: subscription.charge_at ? new Date(subscription.charge_at * 1000).toISOString() : null,
        }),
      }
    );

    return updateResult;
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling payment authorized:", error);
    return {
      success: false,
      message: `Error handling payment authorized: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Handle payment.captured event
 *
 * This event is triggered when a payment is captured.
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handlePaymentCaptured(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  _razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  try {
    const payment = payload.payload.payment;
    if (!payment) {
      console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload");
      return { success: false, message: "Payment data not found in payload" };
    }

    const paymentId = payment.entity.id;
    console.log(`[RAZORPAY_WEBHOOK] Payment captured: ${paymentId}`);

    // Check if this is a subscription payment
    const subscriptionId = payment.entity.notes?.subscription_id;
    if (!subscriptionId) {

      return { success: true, message: "Not a subscription payment, no update needed" };
    }

    // Get subscription details from Razorpay to update all fields
    const { getSubscription } = await import("@/lib/razorpay/services/subscription");
    const subscriptionResult = await getSubscription(subscriptionId);

    if (!subscriptionResult.success || !subscriptionResult.data) {
      console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription details for ${subscriptionId}:`, subscriptionResult.error);
      // Continue anyway with the payment information we have
    }

    const subscription = subscriptionResult.data;

    // Update the subscription notes to include the payment ID
    // This will be used by the subscription.activated webhook to fetch the payment method
    try {
      // Import the updateSubscription function from Razorpay services
      const { updateSubscription: updateRazorpaySubscription } = await import("@/lib/razorpay/services/subscription");

      // Update the subscription notes in Razorpay
      const updateNotesResult = await updateRazorpaySubscription(subscriptionId, {
        notes: {
          ...(subscription?.notes || {}),
          last_payment_id: paymentId
        }
      });

      if (!updateNotesResult.success) {
        console.error(`[RAZORPAY_WEBHOOK] Error updating subscription notes for ${subscriptionId}:`, updateNotesResult.error);
        // Continue anyway - we'll still update our database
      } else {

      }
    } catch (updateError) {
      console.error(`[RAZORPAY_WEBHOOK] Error updating subscription notes:`, updateError);
      // Continue anyway - we'll still update our database
    }

    // Get admin client to bypass RLS
    const adminClient = createClient();

    // Update subscription with payment information
    const updateResult = await updateSubscription(
      adminClient,
      subscriptionId,
      SupabaseSubscriptionStatus._ACTIVE, // Payment captured means subscription is active
      {
        // Additional data to update
        last_payment_id: paymentId,
        // Removed last_payment_amount as requested
        last_payment_date: new Date(payment.entity.created_at * 1000).toISOString(),
        last_payment_method: payment.entity.method,
        // Clear cancellation_requested_at when payment is captured
        cancellation_requested_at: null,
        // Update subscription details if we have them
        ...(subscription && {
          razorpay_customer_id: subscription.customer_id || null,
          subscription_start_date: subscription.current_start ? new Date(subscription.current_start * 1000).toISOString() : null,
          subscription_expiry_time: subscription.current_end ? new Date(subscription.current_end * 1000).toISOString() : null,
          subscription_charge_time: subscription.charge_at ? new Date(subscription.charge_at * 1000).toISOString() : null,
        }),
        // has_active_subscription is now handled by updateSubscription based on status
      }
    );

    return updateResult;
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling payment captured:", error);
    return {
      success: false,
      message: `Error handling payment captured: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Handle payment.failed event
 *
 * This event is triggered when a payment fails.
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handlePaymentFailed(
  payload: RazorpayWebhookData,
  context: { eventId: string }
): Promise<{ success: boolean; message: string }> {
  try {
    // Import centralized webhook processor
    const { webhookProcessor } = await import('./webhookProcessor');
    const { extractWebhookTimestamp, SUBSCRIPTION_STATUS } = await import('./utils');

    const payment = payload.payload.payment;
    if (!payment) {
      console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload");
      return { success: false, message: "Payment data not found in payload" };
    }

    const paymentId = payment.entity.id;
    console.log(`[RAZORPAY_WEBHOOK] Payment failed: ${paymentId}`);

    // Check if this is a subscription payment
    const subscriptionId = payment.entity.notes?.subscription_id;
    if (!subscriptionId) {

      return { success: true, message: "Not a subscription payment, no update needed" };
    }

    // Extract webhook timestamp for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // CRITICAL FIX: Use centralized webhookProcessor for all subscription updates
    const updateResult = await webhookProcessor.updateSubscriptionStatus(
      subscriptionId,
      SUBSCRIPTION_STATUS.PAYMENT_FAILED,
      {
        // Additional data to update
        last_payment_id: paymentId,
        last_payment_date: new Date(payment.entity.created_at * 1000).toISOString(),
        last_payment_method: payment.entity.method,
        // CRITICAL: Don't clear cancellation_requested_at for payment failures
        // User may still want to retry or fix payment issues
      },
      webhookTimestamp // Pass webhook timestamp for sequence validation
    );

    // Mark event as processed
    if (updateResult.success) {
      await webhookProcessor.markEventAsSuccess(context.eventId, updateResult.message);
    } else {
      await webhookProcessor.markEventAsFailed(context.eventId, updateResult.message);
    }

    return updateResult;
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling payment failed:", error);

    // Mark event as failed
    const errorMessage = `Error handling payment failed: ${error instanceof Error ? error.message : String(error)}`;
    if (context) {
      const { webhookProcessor } = await import('./webhookProcessor');
      await webhookProcessor.markEventAsFailed(context.eventId, errorMessage);
    }

    return {
      success: false,
      message: errorMessage
    };
  }
}

/**
 * Handle invoice.paid event
 *
 * This event is triggered when an invoice is paid.
 * It updates the payment method in the payment_subscriptions table.
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handleInvoicePaid(
  payload: RazorpayWebhookData,
  context: { eventId: string }
): Promise<{ success: boolean; message: string }> {
  try {
    // Import centralized webhook processor
    const { webhookProcessor } = await import('./webhookProcessor');
    const { extractWebhookTimestamp, SUBSCRIPTION_STATUS } = await import('./utils');

    // Extract invoice, payment, and subscription data from payload
    const invoice = payload.payload.invoice;
    const payment = payload.payload.payment;

    if (!invoice) {
      console.error("[RAZORPAY_WEBHOOK] Invoice data not found in payload");
      return { success: false, message: "Invoice data not found in payload" };
    }

    if (!payment) {
      console.error("[RAZORPAY_WEBHOOK] Payment data not found in payload");
      return { success: false, message: "Payment data not found in payload" };
    }

    const invoiceId = invoice.entity.id;
    const paymentId = payment.entity.id;
    const subscriptionId = invoice.entity.subscription_id;

    console.log(`[RAZORPAY_WEBHOOK] Invoice paid: ${invoiceId}, Payment: ${paymentId}, Subscription: ${subscriptionId}`);

    if (!subscriptionId) {

      return { success: true, message: "Not a subscription invoice, no update needed" };
    }

    // Extract payment method from the payment entity
    const paymentMethod = payment.entity.method;


    // Extract webhook timestamp for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // CRITICAL FIX: Use centralized webhookProcessor for all subscription updates
    // This ensures proper sequence validation and consistent state management
    const updateResult = await webhookProcessor.updateSubscriptionStatus(
      subscriptionId,
      SUBSCRIPTION_STATUS.ACTIVE, // Invoice paid means subscription is active
      {
        // Additional data to update
        last_payment_id: paymentId,
        last_payment_date: new Date(payment.entity.created_at * 1000).toISOString(),
        last_payment_method: paymentMethod,
        // CRITICAL: Clear cancellation_requested_at when invoice is paid
        // This allows trial users to transition to paid even if they previously cancelled
        cancellation_requested_at: null,
      },
      webhookTimestamp // Pass webhook timestamp for sequence validation
    );

    // Mark event as processed
    if (updateResult.success) {
      await webhookProcessor.markEventAsSuccess(context.eventId, updateResult.message);
    } else {
      await webhookProcessor.markEventAsFailed(context.eventId, updateResult.message);
    }

    return updateResult;
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling invoice paid:", error);

    // Mark event as failed
    const errorMessage = `Error handling invoice paid: ${error instanceof Error ? error.message : String(error)}`;
    if (context) {
      const { webhookProcessor } = await import('./webhookProcessor');
      await webhookProcessor.markEventAsFailed(context.eventId, errorMessage);
    }

    return {
      success: false,
      message: errorMessage
    };
  }
}
