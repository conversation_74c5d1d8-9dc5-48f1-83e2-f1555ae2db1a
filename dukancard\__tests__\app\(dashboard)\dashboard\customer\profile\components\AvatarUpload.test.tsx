import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AvatarUpload from '@/app/(dashboard)/dashboard/customer/profile/components/AvatarUpload';
import { useAvatarUpload } from '@/app/(dashboard)/dashboard/customer/profile/hooks/useAvatarUpload';

// Mock the useAvatarUpload hook
jest.mock('@/app/(dashboard)/dashboard/customer/profile/hooks/useAvatarUpload');

// Mock ImageCropDialog
jest.mock('@/app/(dashboard)/dashboard/business/card/components/ImageCropDialog', () => ({
  __esModule: true,
  default: ({ isOpen, imgSrc, onCropComplete, onClose }: any) => (
    <div data-testid="image-crop-dialog">
      {isOpen && (
        <>
          <span>Crop Dialog Open</span>
          <button onClick={() => onCropComplete(new Blob())}>Crop</button>
          <button onClick={onClose}>Close</button>
        </>
      )}
    </div>
  ),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

// Mock @/components/ui/avatar
jest.mock('@/components/ui/avatar', () => ({
  Avatar: ({ children, className }: any) => <div data-testid="mock-avatar" className={className}>{children}</div>,
  AvatarFallback: ({ children, className }: any) => <div data-testid="mock-avatar-fallback" className={className}>{children}</div>,
  AvatarImage: ({ src, alt }: any) => <img data-testid="mock-avatar-image" src={src} alt={alt} />,
}));

describe('AvatarUpload', () => {
  const mockOnUpdateAvatar = jest.fn();
  const mockOnSelectFile = jest.fn();
  const mockHandleCropComplete = jest.fn();
  const mockHandleCropDialogClose = jest.fn();
  const mockHandleAvatarDelete = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useAvatarUpload as jest.Mock).mockReturnValue({
      localPreviewUrl: null,
      isAvatarUploading: false,
      imageToCrop: null,
      onFileSelect: mockOnSelectFile,
      handleCropComplete: mockHandleCropComplete,
      handleCropDialogClose: mockHandleCropDialogClose,
      handleAvatarDelete: mockHandleAvatarDelete,
      avatarErrorDisplay: null,
    });
  });

  it('renders correctly with initial avatar', () => {
    (useAvatarUpload as jest.Mock).mockReturnValueOnce({
      localPreviewUrl: 'http://example.com/initial.jpg',
      isAvatarUploading: false,
      imageToCrop: null,
      onFileSelect: mockOnSelectFile,
      handleCropComplete: mockHandleCropComplete,
      handleCropDialogClose: mockHandleCropDialogClose,
      handleAvatarDelete: mockHandleAvatarDelete,
      avatarErrorDisplay: null,
    });
    render(<AvatarUpload initialAvatarUrl="http://example.com/initial.jpg" userName="Test User" onUpdateAvatar={mockOnUpdateAvatar} />);

    expect(screen.getByAltText('Test User')).toHaveAttribute('src', 'http://example.com/initial.jpg');
    expect(screen.getByRole('button', { name: /Remove avatar/i })).toBeInTheDocument();
  });

  it('renders correctly without initial avatar and shows initials', () => {
    render(<AvatarUpload userName="Test User" onUpdateAvatar={mockOnUpdateAvatar} />);

    expect(screen.getByText('TU')).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /Remove avatar/i })).not.toBeInTheDocument();
  });

  it('calls onFileSelect when file input changes', () => {
    render(<AvatarUpload userName="Test User" onUpdateAvatar={mockOnUpdateAvatar} />);
    const fileInput = screen.getByLabelText(/Upload avatar/i);
    const file = new File(['dummy content'], 'test.png', { type: 'image/png' });

    fireEvent.change(fileInput, { target: { files: [file] } });

    expect(mockOnSelectFile).toHaveBeenCalledWith(file);
  });

  it('calls handleDeleteClick and handleAvatarDelete when delete button is clicked', () => {
    (useAvatarUpload as jest.Mock).mockReturnValueOnce({
      localPreviewUrl: 'http://example.com/initial.jpg',
      isAvatarUploading: false,
      imageToCrop: null,
      onFileSelect: mockOnSelectFile,
      handleCropComplete: mockHandleCropComplete,
      handleCropDialogClose: mockHandleCropDialogClose,
      handleAvatarDelete: mockHandleAvatarDelete,
      avatarErrorDisplay: null,
    });
    render(<AvatarUpload initialAvatarUrl="http://example.com/initial.jpg" userName="Test User" onUpdateAvatar={mockOnUpdateAvatar} />);

    const deleteButton = screen.getByRole('button', { name: /Remove avatar/i });
    fireEvent.click(deleteButton);

    const confirmButton = screen.getByRole('button', { name: /Remove/i });
    fireEvent.click(confirmButton);
    expect(mockHandleAvatarDelete).toHaveBeenCalledWith('http://example.com/initial.jpg');
  });

  it('does not call handleAvatarDelete if user cancels confirmation', () => {
    (useAvatarUpload as jest.Mock).mockReturnValueOnce({
      localPreviewUrl: 'http://example.com/initial.jpg',
      isAvatarUploading: false,
      imageToCrop: null,
      onFileSelect: mockOnSelectFile,
      handleCropComplete: mockHandleCropComplete,
      handleCropDialogClose: mockHandleCropDialogClose,
      handleAvatarDelete: mockHandleAvatarDelete,
      avatarErrorDisplay: null,
    });
    render(<AvatarUpload initialAvatarUrl="http://example.com/initial.jpg" userName="Test User" onUpdateAvatar={mockOnUpdateAvatar} />);

    const deleteButton = screen.getByRole('button', { name: /Remove avatar/i });
    fireEvent.click(deleteButton);

    // Cancel the deletion in the dialog
    const cancelButton = screen.getByRole('button', { name: /Cancel/i });
    fireEvent.click(cancelButton);

    expect(mockHandleAvatarDelete).not.toHaveBeenCalled();
  });

  it('shows loading spinner when isAvatarUploading is true', () => {
    (useAvatarUpload as jest.Mock).mockReturnValueOnce({
      localPreviewUrl: null,
      isAvatarUploading: true,
      imageToCrop: null,
      onFileSelect: mockOnSelectFile,
      handleCropComplete: mockHandleCropComplete,
      handleCropDialogClose: mockHandleCropDialogClose,
      handleAvatarDelete: mockHandleAvatarDelete,
      avatarErrorDisplay: null,
    });
    render(<AvatarUpload userName="Test User" onUpdateAvatar={mockOnUpdateAvatar} />);

    expect(screen.getByText('Uploading...')).toBeInTheDocument();
  });

  it('displays error message when avatarErrorDisplay is present', () => {
    (useAvatarUpload as jest.Mock).mockReturnValueOnce({
      localPreviewUrl: null,
      isAvatarUploading: false,
      imageToCrop: null,
      onFileSelect: mockOnSelectFile,
      handleCropComplete: mockHandleCropComplete,
      handleCropDialogClose: mockHandleCropDialogClose,
      handleAvatarDelete: mockHandleAvatarDelete,
      avatarErrorDisplay: 'Test Error Message',
    });
    render(<AvatarUpload userName="Test User" onUpdateAvatar={mockOnUpdateAvatar} />);

    expect(screen.getByText('Test Error Message')).toBeInTheDocument();
  });

  it('opens ImageCropDialog when imageToCrop is present', () => {
    (useAvatarUpload as jest.Mock).mockReturnValueOnce({
      localPreviewUrl: null,
      isAvatarUploading: false,
      imageToCrop: 'http://example.com/image-to-crop.jpg',
      onFileSelect: mockOnSelectFile,
      handleCropComplete: mockHandleCropComplete,
      handleCropDialogClose: mockHandleCropDialogClose,
      handleAvatarDelete: mockHandleAvatarDelete,
      avatarErrorDisplay: null,
    });
    render(<AvatarUpload userName="Test User" onUpdateAvatar={mockOnUpdateAvatar} />);

    expect(screen.getByTestId('image-crop-dialog')).toHaveTextContent('Crop Dialog Open');
  });

  it('calls handleCropComplete when ImageCropDialog calls onCropComplete', () => {
    (useAvatarUpload as jest.Mock).mockReturnValueOnce({
      localPreviewUrl: null,
      isAvatarUploading: false,
      imageToCrop: 'http://example.com/image-to-crop.jpg',
      onFileSelect: mockOnSelectFile,
      handleCropComplete: mockHandleCropComplete,
      handleCropDialogClose: mockHandleCropDialogClose,
      handleAvatarDelete: mockHandleAvatarDelete,
      avatarErrorDisplay: null,
    });
    render(<AvatarUpload userName="Test User" onUpdateAvatar={mockOnUpdateAvatar} />);

    fireEvent.click(screen.getByText('Crop'));
    expect(mockHandleCropComplete).toHaveBeenCalledWith(expect.any(Blob));
  });

  it('calls handleCropDialogClose when ImageCropDialog calls onClose', () => {
    (useAvatarUpload as jest.Mock).mockReturnValueOnce({
      localPreviewUrl: null,
      isAvatarUploading: false,
      imageToCrop: 'http://example.com/image-to-crop.jpg',
      onFileSelect: mockOnSelectFile,
      handleCropComplete: mockHandleCropComplete,
      handleCropDialogClose: mockHandleCropDialogClose,
      handleAvatarDelete: mockHandleAvatarDelete,
      avatarErrorDisplay: null,
    });
    render(<AvatarUpload userName="Test User" onUpdateAvatar={mockOnUpdateAvatar} />);

    fireEvent.click(screen.getByText('Close'));
    expect(mockHandleCropDialogClose).toHaveBeenCalledTimes(1);
  });
});
