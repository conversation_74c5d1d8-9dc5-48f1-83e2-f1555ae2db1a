import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ActivityItem from '../../../../../../../app/(dashboard)/dashboard/business/activities/components/ActivityItem';
import { BusinessActivity } from '../../../../../../../lib/actions/activities';

// Mock the framer-motion component
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...rest }: any) => <div {...rest}>{children}</div>,
  },
}));

// Mock next/link
jest.mock('next/link', () => {
  const MockLink = ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  );
  MockLink.displayName = 'MockLink';
  return MockLink;
});

// Mock date-fns formatDistanceToNow
jest.mock('date-fns', () => ({
  formatDistanceToNow: jest.fn((date, options) => '2 hours ago'),
}));

// Mock @/components/ui/avatar and @/components/ui/badge
jest.mock('@/components/ui/avatar', () => ({
  Avatar: ({ children }: { children: React.ReactNode }) => <div data-testid="avatar">{children}</div>,
  AvatarFallback: ({ children }: { children: React.ReactNode }) => <div data-testid="avatar-fallback">{children}</div>,
  AvatarImage: ({ src, alt }: { src: string; alt: string }) => <img src={src} alt={alt} data-testid="avatar-image" />,
}));
jest.mock('@/components/ui/badge', () => ({
  Badge: ({ children, className }: { children: React.ReactNode; className?: string }) => <span className={className}>{children}</span>,
}));

// Mock @/lib/utils cn function
jest.mock('@/lib/utils', () => ({
  cn: jest.fn((...args) => args.filter(Boolean).join(' ')),
}));

const mockActivity: BusinessActivity = {
  id: '1',
  activity_type: 'like',
  created_at: '2025-07-19T10:00:00Z',
  is_read: false,
  user_profile: {
    id: 'user1',
    is_business: false,
    name: 'John Doe',
    avatar_url: 'http://example.com/avatar.jpg',
  },
  rating_value: null,
  business_profile_id: 'biz1',
  user_id: 'user1',
  post_id: null,
  post_type: null,
};

const mockBusinessActivity: BusinessActivity = {
  id: '2',
  activity_type: 'subscribe',
  created_at: '2025-07-19T09:00:00Z',
  is_read: true,
  user_profile: {
    id: 'user2',
    is_business: true,
    business_name: 'Acme Corp',
    logo_url: 'http://example.com/logo.jpg',
    business_slug: 'acme-corp',
  },
  rating_value: null,
  business_profile_id: 'biz2',
  user_id: 'user2',
  post_id: null,
  post_type: null,
};

describe('ActivityItem', () => {
  it('renders customer activity correctly', () => {
    render(<ActivityItem activity={mockActivity} />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('liked your business')).toBeInTheDocument();
    expect(screen.getByText('2 hours ago')).toBeInTheDocument();
    expect(screen.getByTestId('avatar-image')).toHaveAttribute('src', 'http://example.com/avatar.jpg');
    expect(screen.getByText('like')).toBeInTheDocument();
    expect(screen.queryByText('Business')).not.toBeInTheDocument();
  });

  it('renders business activity correctly', () => {
    render(<ActivityItem activity={mockBusinessActivity} />);

    expect(screen.getByText('Acme Corp')).toBeInTheDocument();
    expect(screen.getByText('subscribed to your business')).toBeInTheDocument();
    expect(screen.getByText('2 hours ago')).toBeInTheDocument();
    expect(screen.getByTestId('avatar-image')).toHaveAttribute('src', 'http://example.com/logo.jpg');
    expect(screen.getByText('subscribe')).toBeInTheDocument();
    expect(screen.getByText('Business')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Acme Corp' })).toHaveAttribute('href', '/acme-corp');
  });

  it('renders rating activity correctly', () => {
    const ratingActivity: BusinessActivity = {
      ...mockActivity,
      activity_type: 'rating',
      rating_value: 4,
      user_profile: {
        id: 'user3',
        is_business: false,
        name: 'Jane Doe',
        avatar_url: null,
      },
    };
    render(<ActivityItem activity={ratingActivity} />);

    expect(screen.getByText('Jane Doe')).toBeInTheDocument();
    expect(screen.getByText('rated your business 4/5')).toBeInTheDocument();
    expect(screen.getByText('rating')).toBeInTheDocument();
    expect(screen.getByTestId('avatar-fallback')).toHaveTextContent('JD');
  });

  it('renders unread indicator if activity is unread', () => {
    render(<ActivityItem activity={mockActivity} />);
    expect(screen.getByText('liked your business').closest('.relative')?.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('does not render unread indicator if activity is read', () => {
    render(<ActivityItem activity={mockBusinessActivity} />);
    expect(screen.getByText('subscribed to your business').closest('div')).not.toHaveClass('animate-pulse');
  });

  it('calls onView when item comes into view', () => {
    const onViewMock = jest.fn();
    render(<ActivityItem activity={mockActivity} onView={onViewMock} />);

    // Manually trigger intersection observer callback
    const entry = { isIntersecting: true, target: screen.getByText('liked your business').closest('div') };
    // @ts-ignore
    global.IntersectionObserver.mock.calls[0][0]([entry]);

    expect(onViewMock).toHaveBeenCalledWith(mockActivity.id);
  });

  it('handles unknown user profile', () => {
    const unknownUserActivity: BusinessActivity = {
      ...mockActivity,
      user_profile: null as any,
    };
    render(<ActivityItem activity={unknownUserActivity} />);
    expect(screen.getByText('Unknown User')).toBeInTheDocument();
    expect(screen.getByTestId('avatar-fallback')).toHaveTextContent('UU');
  });

  it('handles missing business_name for business user', () => {
    const noNameBusinessActivity: BusinessActivity = {
      ...mockBusinessActivity,
      user_profile: {
        id: 'user2',
        is_business: true,
        logo_url: 'http://example.com/logo.jpg',
        business_slug: 'acme-corp',
      },
    };
    render(<ActivityItem activity={noNameBusinessActivity} />);
    expect(screen.getByText('Business User')).toBeInTheDocument();
  });

  it('handles missing name for customer user', () => {
    const noNameCustomerActivity: BusinessActivity = {
      ...mockActivity,
      user_profile: {
        id: 'user1',
        is_business: false,
        avatar_url: 'http://example.com/avatar.jpg',
      },
    };
    render(<ActivityItem activity={noNameCustomerActivity} />);
    expect(screen.getByText('Customer')).toBeInTheDocument();
  });
});
