import React from 'react';
import { render } from '@testing-library/react-native';
import LinkPhoneSection from '@/app/(dashboard)/customer/settings/components/LinkPhoneSection';
import { useTheme } from '@/src/hooks/useTheme';
import { createLinkPhoneSectionStyles } from '@/styles/dashboard/customer/settings/link-phone';
import { Text, TouchableOpacity } from 'react-native';

// Mock external modules and components
jest.mock('@/src/hooks/useTheme');
jest.mock('lucide-react-native', () => ({
  Phone: 'Phone',
}));
jest.mock('@/styles/dashboard/customer/settings/link-phone', () => ({
  createLinkPhoneSectionStyles: jest.fn(() => ({
    container: {},
    header: {},
    titleContainer: {},
    iconContainer: {},
    title: {},
    description: {},
    content: {},
    section: {},
    label: {},
    readOnlyContainer: {},
    readOnlyText: {},
    helperText: {},
    emptyState: {},
    emptyIconContainer: {},
    emptyTitle: {},
    emptyDescription: {},
  })),
}));

describe('LinkPhoneSection', () => {
  const mockTheme = {
    colors: {
      textSecondary: '#666',
    },
  };

  beforeEach(() => {
    (useTheme as jest.Mock).mockReturnValue(mockTheme);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly when a phone number is linked', () => {
    const { getByText } = render(
      <LinkPhoneSection currentPhone="+************" registrationType="email" />
    );

    expect(getByText('Phone Number')).toBeDefined();
    expect(getByText('Your current phone number linked to this account.')).toBeDefined();
    expect(getByText('+************')).toBeDefined();
    expect(getByText('Phone number changes are not currently supported. Contact support if you need to update your number.')).toBeDefined();
  });

  it('renders correctly when no phone number is linked', () => {
    const { getByText } = render(
      <LinkPhoneSection currentPhone={null} registrationType="email" />
    );

    expect(getByText('Phone Number')).toBeDefined();
    expect(getByText('No phone number is currently linked to your account.')).toBeDefined();
    expect(getByText('No Phone Number')).toBeDefined();
    expect(getByText('No phone number is currently linked to your account. Phone number linking is not available at this time.')).toBeDefined();
  });
});
