import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { CategorySelector } from '@/src/components/discovery/CategorySelector';
import { useTheme } from '@/src/hooks/useTheme';

// Mock the useTheme hook
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      background: '#ffffff',
      cardBackground: '#f0f0f0',
      border: '#cccccc',
      shadow: '#000000',
      primary: '#C29D5B',
      textSecondary: '#888888',
    },
  }),
}));

describe('CategorySelector', () => {
  it('renders with default text when no category is selected', () => {
    const { getByText } = render(
      <CategorySelector selectedCategory={null} onCategoryPress={() => {}} />
    );
    expect(getByText('Select Category')).toBeTruthy();
  });

  it('renders with the selected category name', () => {
    const { getByText } = render(
      <CategorySelector
        selectedCategory="Electronics"
        onCategoryPress={() => {}}
      />
    );
    expect(getByText('Electronics')).toBeTruthy();
  });

  it('calls onCategoryPress when the button is pressed', () => {
    const onCategoryPress = jest.fn();
    const { getByTestId } = render(
      <CategorySelector
        selectedCategory={null}
        onCategoryPress={onCategoryPress}
      />
    );
    fireEvent.press(getByTestId('category-selector-button'));
    expect(onCategoryPress).toHaveBeenCalled();
  });

  it('calls onCategoryClear when the clear button is pressed', () => {
    const onCategoryClear = jest.fn();
    const { getByTestId } = render(
      <CategorySelector
        selectedCategory="Books"
        onCategoryPress={() => {}}
        onCategoryClear={onCategoryClear}
      />
    );
    fireEvent.press(getByTestId('clear-category-button'));
    expect(onCategoryClear).toHaveBeenCalled();
  });

  it('disables the button when the disabled prop is true', () => {
    const onCategoryPress = jest.fn();
    const { getByTestId } = render(
      <CategorySelector
        selectedCategory={null}
        onCategoryPress={onCategoryPress}
        disabled={true}
      />
    );
    fireEvent.press(getByTestId('category-selector-button'));
    expect(onCategoryPress).not.toHaveBeenCalled();
  });
});
