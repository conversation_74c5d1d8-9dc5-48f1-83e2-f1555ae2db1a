import { supabase } from "@/lib/supabase";
import { TABLES, COLUMNS } from "@/src/config/supabase/constants";
import { Database, Tables, TablesInsert, TablesUpdate } from "@dukancard-types/supabase";

export type CustomerProfilesInsert = TablesInsert<"customer_profiles">;
export type CustomerProfilesUpdate = TablesUpdate<"customer_profiles">;

export interface ComprehensiveUserProfile {
  id: string;
  email?: string;
  phone?: string;
  full_name?: string;
  profile?: Tables<'customer_profiles'>;
}

export class CustomerProfileService {
  static async updateCustomerName(userId: string, name: string) {
    return await supabase.auth.updateUser({
      data: { full_name: name },
    });
  }

  static async updateCustomerAddress(userId: string, addressData: any) {
    // Fetch coordinates from pincode table if available
    let latitude: number | null = null;
    let longitude: number | null = null;

    // GPS coordinates should be provided directly - no fallback to pincodes table
    // Coordinates will remain null if not provided via GPS

    return await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .update({
        address: addressData.address || null,
        pincode: addressData.pincode,
        city: addressData.city,
        state: addressData.state,
        locality: addressData.locality,
        latitude: latitude,
        longitude: longitude,
      })
      .eq(COLUMNS.ID, userId);
  }

  static async updateCustomerPhone(userId: string, phone: string) {
    const { error: updateError } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .update({ phone: phone })
      .eq(COLUMNS.ID, userId);

    if (updateError) {
      return { data: null, error: updateError };
    }

    // Update phone in Supabase auth.users table to maintain user ID consistency
    const { error: authUpdateError } = await supabase.auth.updateUser({
      phone: `+91${phone}`,
    });

    return { data: null, error: authUpdateError };
  }

  static async updateCustomerEmail(email: string) {
    return await supabase.auth.updateUser({
      email: email || undefined,
    });
  }
}

export async function getCustomerProfile(userId: string) {
  return await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .select("*")
    .eq(COLUMNS.ID, userId)
    .single();
}

export async function getCustomerProfileForEdit(userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .select("*")
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error("Error fetching customer profile for edit:", error);
      return { data: null, error };
    }

    return { data, error: null };
  } catch (error) {
    console.error(
      "Unexpected error fetching customer profile for edit:",
      error
    );
    return { data: null, error };
  }
}

export async function checkCustomerProfileExists(userId: string) {
  const { data, error } = await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .select(COLUMNS.ID)
    .eq(COLUMNS.ID, userId)
    .single();

  return { exists: !!data && !error, error };
}

export async function createCustomerProfile(
  userId: string,
  profileData: Omit<CustomerProfilesInsert, 'id'>
) {
  return await supabase.from(TABLES.CUSTOMER_PROFILES).insert<TablesInsert<"customer_profiles">>({
    id: userId,
    ...profileData,
  });
}

export async function updateCustomerProfile(
  userId: string,
  profileData: CustomerProfilesUpdate
) {
  return await supabase
    .from(TABLES.CUSTOMER_PROFILES)
    .update(profileData)
    .eq(COLUMNS.ID, userId);
}

export async function updateCustomerProfileWithCoordinates(
  userId: string,
  profileData: {
    name?: string;
    address?: string | null;
    pincode?: string | null;
    city?: string | null;
    state?: string | null;
    locality?: string | null;
    latitude?: number;
    longitude?: number;
    avatar_url?: string | null;
  }
) {
  try {
    const updateData = {
      ...profileData,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .update(updateData)
      .eq(COLUMNS.ID, userId)
      .select();

    if (error) {
      console.error("Error updating customer profile with coordinates:", error);
      return { data: null, error };
    }

    return { data: data?.[0] || null, error: null };
  } catch (error) {
    console.error(
      "Unexpected error updating customer profile with coordinates:",
      error
    );
    return { data: null, error };
  }
}

export async function getCustomerProfileById(userId: string) {
  return await getCustomerProfile(userId);
}

export async function getCustomerProfilesByIds(userIds: string[]) {
  return await supabase.from("customer_profiles").select("*").in("id", userIds);
}

export async function deleteCustomerProfile(userId: string) {
  return await supabase.from("customer_profiles").delete().eq("id", userId);
}

export async function getComprehensiveUserProfile(
  userId: string
): Promise<{ data: ComprehensiveUserProfile | null; error: any }> {
  try {
    // Get user from auth
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { data: null, error: authError || "User not found" };
    }

    // Get customer profile
    const { data: profile, error: profileError } = await getCustomerProfile(
      userId
    );

    const comprehensiveProfile: ComprehensiveUserProfile = {
      id: user.id,
      email: user.email,
      phone: user.phone,
      full_name: user.user_metadata?.full_name,
      profile: profile || undefined,
    };

    return { data: comprehensiveProfile, error: profileError };
  } catch (error) {
    return { data: null, error };
  }
}
