import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import GalleryTab from '@/src/components/business/GalleryTab';
import { createPublicCardViewStyles } from '@/styles/PublicCardViewStyles';
import { BusinessGalleryImage } from '@/src/types/discovery';

// Mock external modules
jest.mock('@/styles/PublicCardViewStyles', () => ({
  createPublicCardViewStyles: jest.fn(() => ({
    section: {},
    galleryItem: {},
    galleryImage: {},
    emptyText: {},
  })),
}));

// Mock Image component to avoid actual image loading issues in tests
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Image: jest.fn((props) => <RN.Image {...props} testID="gallery-image" />),
    FlatList: RN.FlatList,
    View: RN.View,
    Text: RN.Text,
    TouchableOpacity: RN.TouchableOpacity,
  };
});

describe('GalleryTab', () => {
  const mockGallery: BusinessGalleryImage[] = [
    { id: '1', url: 'http://example.com/img1.jpg', caption: 'Image 1' },
    { id: '2', url: 'http://example.com/img2.jpg', caption: 'Image 2' },
    { id: '3', url: 'http://example.com/img3.jpg', caption: 'Image 3' },
  ];
  const mockOnImagePress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (createPublicCardViewStyles as jest.Mock).mockReturnValue({
      section: {},
      galleryItem: {},
      galleryImage: {},
      emptyText: {},
    });
  });

  it('renders gallery images when provided', () => {
    render(
      <GalleryTab
        gallery={mockGallery}
        isDark={false}
        onImagePress={mockOnImagePress}
      />
    );

    expect(screen.getAllByTestId('gallery-image')).toHaveLength(mockGallery.length);
    expect(screen.queryByText('No gallery images available')).toBeNull();
  });

  it('renders empty message when gallery is empty', () => {
    render(
      <GalleryTab
        gallery={[]}
        isDark={false}
        onImagePress={mockOnImagePress}
      />
    );

    expect(screen.queryAllByTestId('gallery-image')).toHaveLength(0);
    expect(screen.getByText('No gallery images available')).toBeOnTheScreen();
  });

  it('calls onImagePress with correct index when an image is pressed', () => {
    render(
      <GalleryTab
        gallery={mockGallery}
        isDark={false}
        onImagePress={mockOnImagePress}
      />
    );

    fireEvent.press(screen.getAllByTestId('gallery-image')[1]); // Press the second image
    expect(mockOnImagePress).toHaveBeenCalledWith(1);
  });

  it('applies dark mode styles', () => {
    render(
      <GalleryTab
        gallery={mockGallery}
        isDark={true}
        onImagePress={mockOnImagePress}
      />
    );
    expect(createPublicCardViewStyles).toHaveBeenCalledWith(true);
  });

  it('applies light mode styles', () => {
    render(
      <GalleryTab
        gallery={mockGallery}
        isDark={false}
        onImagePress={mockOnImagePress}
      />
    );
    expect(createPublicCardViewStyles).toHaveBeenCalledWith(false);
  });

  it('handles null gallery prop gracefully', () => {
    render(
      <GalleryTab
        gallery={null as any} // Pass null to test robustness
        isDark={false}
        onImagePress={mockOnImagePress}
      />
    );
    expect(screen.getByText('No gallery images available')).toBeOnTheScreen();
  });

  it('handles undefined gallery prop gracefully', () => {
    render(
      <GalleryTab
        gallery={undefined as any} // Pass undefined to test robustness
        isDark={false}
        onImagePress={mockOnImagePress}
      />
    );
    expect(screen.getByText('No gallery images available')).toBeOnTheScreen();
  });
});