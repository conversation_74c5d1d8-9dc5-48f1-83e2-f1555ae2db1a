/**
 * Discovery Context for React Native
 * Based on dukancard/app/(main)/discover/context/DiscoverContext.tsx
 */

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { useAuth } from "./AuthContext";
import { supabase } from "../config/supabase";
import {
  loadLocationData,
  saveLocationData,
} from "../services/locationStorageService";
import {
  DiscoverContextType,
  DiscoverSearchResult,
  ProductFilterOption,
  ProductSortOption,
  ViewType,
  BusinessSortBy,
  CombinedSearchFormData,
  LocationData,
} from "../types/discovery";
import { Tables } from "../types/supabase";


import { NearbyProduct } from "../services/discovery";
import {
  discoveryService,
  DiscoverySearchParams,
} from "../services/discovery/DiscoveryService";

// Create the context
const DiscoverContext = createContext<DiscoverContextType | undefined>( // Changed to DiscoverContext
  undefined
);

// Provider component
export function DiscoverProvider({ children }: { children: React.ReactNode }) {
  // Changed to DiscoverProvider
  const { user } = useAuth();

  // State
  const [viewType, setViewType] = useState<ViewType>("products");
  const [sortBy, setSortBy] = useState<BusinessSortBy>("created_desc"); // Changed from businessSortBy, default to created_desc
  const [isSearching, setIsSearching] = useState(true); // Start with loading state

  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isSorting, setIsSorting] = useState(false);
  const [isFilteringByCategory, setIsFilteringByCategory] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [productFilterBy, setProductFilterBy] =
    useState<ProductFilterOption>("all");
  const [productSortBy, setProductSortBy] =
    useState<ProductSortOption>("newest");
  const [searchResult, setSearchResult] = useState<DiscoverSearchResult | null>(
    null
  );
  const [businesses, setBusinesses] = useState<BusinessCardData[]>([]);
  const [products, setProducts] = useState<NearbyProduct[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [hasPerformedInitialSearch, setHasPerformedInitialSearch] =
    useState(false);

  // Location state (kept for React Native app)
  const [userLocation, setUserLocation] = useState<LocationData | null>(null);
  const [isLocationLoading, setIsLocationLoading] = useState(false);

  // Search term (moved here)
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Update authentication state when user changes
  useEffect(() => {
    setIsAuthenticated(!!user);
  }, [user]);

  // Initialize location data on app start - only from profile data
  useEffect(() => {
    const initializeLocation = async () => {
      try {
        setIsLocationLoading(true);

        if (user) {
          // First try to load saved location data
          const loadResult = await loadLocationData();
          if (loadResult.success && loadResult.data) {
            setUserLocation(loadResult.data);
            return;
          }

          // If no saved location, try to get from customer_profiles
          const { data: customerProfile } = await supabase
            .from("customer_profiles")
            .select("latitude, longitude, city, pincode, locality")
            .eq("id", user.id)
            .single();

          if (customerProfile && customerProfile.latitude) {
            const location = {
              latitude: customerProfile.latitude,
              longitude: customerProfile.longitude,
              city: customerProfile.city || undefined,
              pincode: customerProfile.pincode || undefined,
              locality: customerProfile.locality || undefined,
            };
            setUserLocation(location);
            await saveLocationData(location);
            return;
          }

          // Fallback to business_profiles
          const { data: businessProfile } = await supabase
            .from("business_profiles")
            .select("latitude, longitude, city, pincode, locality")
            .eq("user_id", user.id)
            .single();

          if (businessProfile && businessProfile.latitude) {
            const location = {
              latitude: businessProfile.latitude,
              longitude: businessProfile.longitude,
              city: businessProfile.city || undefined,
              pincode: businessProfile.pincode || undefined,
              locality: businessProfile.locality || undefined,
            };
            setUserLocation(location);
            await saveLocationData(location);
            return;
          }

          // No location found in profiles - show global results until user sets location manually
          console.log("No location found in profiles, showing global results");
        }
      } catch (error) {
        console.error("Error initializing location:", error);
        setSearchError("Failed to initialize location data");
        setIsSearching(false);
      } finally {
        setIsLocationLoading(false);
      }
    };

    initializeLocation();
  }, [user]);

  // Functions
  const performSearchWithViewType = useCallback(
    async (
      targetViewType: ViewType,
      data: CombinedSearchFormData,
      newBusinessSort?: BusinessSortBy,
      newProductSort?: ProductSortOption
    ): Promise<void> => {
      try {
        setIsSearching(true);
        setSearchError(null);
        setCurrentPage(1);

        console.log(
          "Performing search with data:",
          data,
          "for viewType:",
          targetViewType
        );
        console.log(
          "Selected category being passed to search:",
          selectedCategory
        );

        // Prepare parameters for the centralized discovery service
        const searchParams: DiscoverySearchParams = {
          viewType: targetViewType,
          searchTerm:
            targetViewType === "cards" ? data.businessName : data.productName,
          category: data.category,
          pincode: data.pincode,
          city: data.city,
          locality: data.locality,
          page: 1,
          limit: 20,
          businessSort:
            targetViewType === "cards"
              ? newBusinessSort || sortBy
              : "created_desc",
          productSort:
            targetViewType === "products"
              ? newProductSort || productSortBy
              : "newest",
          productType:
            targetViewType === "products" && productFilterBy !== "all"
              ? productFilterBy
              : null,
          userLocation:
            userLocation &&
            userLocation.latitude !== undefined &&
            userLocation.longitude !== undefined
              ? {
                  latitude: userLocation.latitude,
                  longitude: userLocation.longitude,
                }
              : undefined,
        };

        // Call the centralized discovery service
        const searchResult = await discoveryService.search(searchParams);

        if (searchResult.data) {
          const result = searchResult.data;

          setSearchResult({
            location: result.location,
            businesses: result.businesses || [],
            products: result.products || [],
            isAuthenticated: result.isAuthenticated,
            totalCount: result.totalCount,
            hasMore: result.hasMore,
            nextPage: result.nextPage,
          });

          console.log(`Search result for ${targetViewType}:`, {
            businesses: result.businesses?.length || 0,
            products: result.products?.length || 0,
            totalCount: result.totalCount,
          });
          setBusinesses(result.businesses || []);
          setProducts(result.products || []);
          setTotalCount(result.totalCount);
          setHasMore(result.hasMore);
        } else {
          throw new Error(searchResult.error || "Search failed");
        }
      } catch (error) {
        console.error("Search error:", error);
        setSearchError("Failed to perform search. Please try again.");

        // Reset to empty state on error
        setSearchResult(null);
        setBusinesses([]);
        setProducts([]);
        setTotalCount(0);
        setHasMore(false);
      } finally {
        setIsSearching(false);
        setIsSorting(false);
        setIsFilteringByCategory(false);
      }
    },
    [userLocation, sortBy, productSortBy, productFilterBy, selectedCategory]
  );

  const performSearch = useCallback(
    async (
      data: CombinedSearchFormData,
      targetViewType?: ViewType,
      newBusinessSort?: BusinessSortBy,
      newProductSort?: ProductSortOption
    ): Promise<void> => {
      return performSearchWithViewType(
        targetViewType || viewType,
        data,
        newBusinessSort,
        newProductSort
      );
    },
    [viewType, performSearchWithViewType]
  );

  // Trigger initial search when location is available (only once)
  useEffect(() => {
    if (userLocation && !isLocationLoading && !hasPerformedInitialSearch) {
      // Perform initial search with current location
      performSearch({
        businessName: null,
        productName: null,
        pincode: userLocation.pincode || null,
        city: userLocation.city || null,
        locality: userLocation.locality || null,
      });
      setHasPerformedInitialSearch(true);
    }
  }, [
    userLocation,
    isLocationLoading,
    hasPerformedInitialSearch,
    performSearch,
  ]);

  // Note: Removed automatic search useEffect - search now only triggers manually

  const handleViewChange = (view: ViewType): void => {
    console.log(`Switching view from ${viewType} to ${view}`);

    setViewType(view);
    // Reset pagination when changing views
    setCurrentPage(1);
    setHasMore(false);

    // Clear current data immediately to show loading state
    setBusinesses([]);
    setProducts([]);
    setTotalCount(0);

    // Trigger a new search for the changed view with explicit viewType parameter
    performSearchWithViewType(view, {
      businessName: view === "cards" ? searchTerm : null,
      productName: view === "products" ? searchTerm : null,
      pincode: userLocation?.pincode || null,
      city: userLocation?.city || null,
      locality: userLocation?.locality || null,
      category: selectedCategory,
    });
  };

  const handleBusinessSortChange = (sortOption: BusinessSortBy): void => {
    // Changed type to BusinessSortBy
    setSortBy(sortOption); // Changed from setBusinessSortBy
    setIsSorting(true);
    // Reset pagination when changing sort
    setCurrentPage(1);
    setHasMore(false);

    // Clear current data immediately to show loading state
    setBusinesses([]);
    setProducts([]);
    setTotalCount(0);

    // Trigger a new search with the updated sort - pass the new sort value directly
    performSearch(
      {
        businessName: viewType === "cards" ? searchTerm || null : null,
        productName: viewType === "products" ? searchTerm || null : null,
        pincode: userLocation?.pincode || null,
        city: userLocation?.city || null,
        locality: userLocation?.locality || null,
        category: selectedCategory,
      },
      undefined, // targetViewType
      sortOption, // Pass the new business sort value directly
      undefined // newProductSort
    );
  };

  const handleSearch = (term: string): void => {
    // Renamed from handleBusinessSearch/handleProductSearch
    setSearchTerm(term);

    // Trigger search immediately (no debouncing - manual search only)
    if (userLocation && !isLocationLoading) {
      performSearch({
        businessName: viewType === "cards" ? term : null,
        productName: viewType === "products" ? term : null,
        pincode: userLocation.pincode || null,
        city: userLocation.city || null,
        locality: userLocation.locality || null,
        category: selectedCategory,
      });
    }
  };

  const handleClearSearch = (): void => {
    setSearchTerm("");
    // Trigger search with empty term to show all results
    if (userLocation && !isLocationLoading) {
      performSearch({
        businessName: null,
        productName: null,
        pincode: userLocation.pincode || null,
        city: userLocation.city || null,
        locality: userLocation.locality || null,
        category: selectedCategory,
      });
    }
  };

  const handleProductSortChange = (sortOption: ProductSortOption): void => {
    setProductSortBy(sortOption);
    setIsSorting(true);
    // Reset pagination when changing sort
    setCurrentPage(1);
    setHasMore(false);

    // Clear current data immediately to show loading state
    setBusinesses([]);
    setProducts([]);
    setTotalCount(0);

    // Trigger a new search with the updated sort - pass the new sort value directly
    performSearch(
      {
        businessName: viewType === "cards" ? searchTerm || null : null,
        productName: viewType === "products" ? searchTerm || null : null,
        pincode: userLocation?.pincode || null,
        city: userLocation?.city || null,
        locality: userLocation?.locality || null,
        category: selectedCategory,
      },
      undefined, // targetViewType
      undefined, // newBusinessSort
      sortOption // Pass the new product sort value directly
    );
  };

  const handleProductFilterChange = (filter: ProductFilterOption): void => {
    setProductFilterBy(filter);
    // Reset pagination when changing filter
    setCurrentPage(1);
    setHasMore(false);
    // Trigger a new search with the updated filter
    performSearch({
      businessName: viewType === "cards" ? searchTerm || null : null,
      productName: viewType === "products" ? searchTerm || null : null,
      pincode: userLocation?.pincode || null,
      city: userLocation?.city || null,
      locality: userLocation?.locality || null,
      category: selectedCategory,
    });
  };

  const handleCategoryChange = (category: string | null): void => {
    setSelectedCategory(category);
    setIsFilteringByCategory(true);
    // Reset pagination when changing category
    setCurrentPage(1);
    setHasMore(false);

    // Clear current data immediately to show loading state
    setBusinesses([]);
    setProducts([]);
    setTotalCount(0);

    // Trigger a new search with the updated category
    performSearch({
      businessName: viewType === "cards" ? searchTerm || null : null,
      productName: viewType === "products" ? searchTerm || null : null,
      pincode: userLocation?.pincode || null,
      city: userLocation?.city || null,
      locality: userLocation?.locality || null,
      category: category, // Use the new category value
    });
  };

  const loadMore = async (): Promise<void> => {
    if (isLoadingMore || !hasMore || !searchResult) return; // Check searchResult.data

    try {
      setIsLoadingMore(true);
      const nextPage = currentPage + 1;

      // Prepare parameters for the centralized discovery service
      const loadMoreParams: DiscoverySearchParams = {
        viewType,
        searchTerm: searchTerm,
        category: selectedCategory,
        pincode: userLocation?.pincode || null,
        city: userLocation?.city || null,
        locality: userLocation?.locality || null,
        page: nextPage,
        limit: 20,
        businessSort: viewType === "cards" ? sortBy : "created_desc",
        productSort: viewType === "products" ? productSortBy : "newest",
        productType:
          viewType === "products" && productFilterBy !== "all"
            ? productFilterBy
            : null,
        userLocation:
          userLocation &&
          userLocation.latitude !== undefined &&
          userLocation.longitude !== undefined
            ? {
                latitude: userLocation.latitude,
                longitude: userLocation.longitude,
              }
            : undefined,
      };

      // Call the centralized discovery service for next page
      const searchMoreResult = await discoveryService.search(loadMoreParams);

      if (searchMoreResult.data) {
        // Removed .success check
        const result = searchMoreResult.data;

        // Append new results to existing ones
        if (viewType === "cards") {
          setBusinesses((prev) => [...prev, ...(result.businesses || [])]);
        } else {
          setProducts((prev) => [...prev, ...(result.products || [])]);
        }

        setCurrentPage(nextPage);
        setHasMore(result.hasMore);
        setTotalCount(result.totalCount);
      } else {
        throw new Error(
          searchMoreResult.error || "Failed to load more results"
        );
      }
    } catch (error) {
      console.error("Load more error:", error);
      setSearchError("Failed to load more results. Please try again.");
    } finally {
      setIsLoadingMore(false);
    }
  };

  const refresh = async (): Promise<void> => {
    // Reset pagination and perform search again
    setCurrentPage(1);
    setHasMore(false);

    // Use current search terms and location for refresh
    await performSearch({
      businessName: viewType === "cards" ? searchTerm : null,
      productName: viewType === "products" ? searchTerm : null,
      pincode: userLocation?.pincode || null,
      city: userLocation?.city || null,
      locality: userLocation?.locality || null,
      category: selectedCategory,
    });
  };

  // Location functions (kept for React Native app)
  const updateLocation = async (location: LocationData): Promise<void> => {
    try {
      setIsLocationLoading(true);
      setUserLocation(location);
      await saveLocationData(location);

      // Trigger a new search with the updated location
      await performSearch({
        businessName: viewType === "cards" ? searchTerm : null,
        productName: viewType === "products" ? searchTerm : null,
        pincode: location.pincode || null,
        city: location.city || null,
        locality: location.locality || null,
        category: selectedCategory,
      });
    } catch (error) {
      console.error("Error updating location:", error);
      setSearchError("Failed to update location. Please try again.");
    } finally {
      setIsLocationLoading(false);
    }
  };

  // Create the context value
  const contextValue: DiscoverContextType = {
    // Changed to DiscoverContextType
    // State
    viewType,
    sortBy, // Changed from businessSortBy
    isSearching,

    isLoadingMore,
    isSorting,
    isFilteringByCategory,
    searchError,
    productFilterBy,
    productSortBy,
    searchResult,
    businesses,
    products,
    currentPage,
    hasMore,
    totalCount,
    isAuthenticated,
    searchTerm, // Added searchTerm
    userLocation, // Added userLocation
    isLocationLoading, // Added isLocationLoading
    selectedCategory, // Added selectedCategory

    // Functions
    performSearch,
    handleViewChange,
    handleBusinessSortChange,
    handleSearch, // Renamed
    handleClearSearch, // Added clear search functionality
    handleProductSortChange,
    handleProductFilterChange,
    handleCategoryChange, // Added
    loadMore,
    refresh,
    updateLocation, // Added updateLocation
  };

  return (
    <DiscoverContext.Provider value={contextValue}>
      {" "}
      {/* Changed to DiscoverContext */}
      {children}
    </DiscoverContext.Provider>
  );
}

// Create a custom hook to use the context
export function useDiscoverContext() {
  // Changed to useDiscoverContext
  const context = useContext(DiscoverContext); // Changed to DiscoverContext
  if (context === undefined) {
    throw new Error(
      "useDiscoverContext must be used within a DiscoverProvider" // Changed message
    );
  }
  return context;
}
