import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { handleRazorpayWebhook, ExtendedRazorpayWebhookData } from "@/lib/razorpay/webhooks/handleWebhook";
import { getPendingWebhookErrors, updateWebhookErrorLog } from "@/lib/razorpay/webhooks/errorTracking";

/**
 * Retry handler for failed Razorpay webhooks
 * This endpoint is meant to be called by a cron job to retry failed webhooks
 * @param req The incoming request
 * @returns The response
 */
export async function POST(req: NextRequest) {
  try {
    // Get the request body
    const body = await req.json();

    // Get the API key from the headers
    const apiKey = req.headers.get("x-api-key") || "";

    // Verify the API key
    const expectedApiKey = process.env.WEBHOOK_RETRY_API_KEY;
    if (!expectedApiKey || apiKey !== expectedApiKey) {
      console.error("[RAZORPAY_WEBHOOK_RETRY] Invalid API key");
      return NextResponse.json(
        { success: false, message: "Invalid API key" },
        { status: 401 }
      );
    }

    // Get the max retry count from the request body
    const maxRetryCount = body.max_retry_count || 3;

    // Get pending webhook errors
    const pendingErrors = await getPendingWebhookErrors(maxRetryCount);

    if (pendingErrors.length === 0) {
      return NextResponse.json(
        { success: true, message: "No pending webhook errors to retry" },
        { status: 200 }
      );
    }

    console.log(`[RAZORPAY_WEBHOOK_RETRY] Found ${pendingErrors.length} pending webhook errors to retry`);

    // Process each pending error
    const results = [];
    for (const error of pendingErrors) {
      try {
        // Update retry count
        await updateWebhookErrorLog(
          error.id,
          'retrying',
          error.retry_count + 1
        );

        // Retry the webhook
        const result = await handleRazorpayWebhook(
          error.payload as unknown as ExtendedRazorpayWebhookData,
          "", // Empty signature since we're retrying
          error.id,
          undefined // No raw body needed for retries
        );

        results.push({
          error_id: error.id,
          event_type: error.event_type,
          subscription_id: error.subscription_id,
          success: result.success,
          message: result.message
        });
      } catch (retryError) {
        console.error(`[RAZORPAY_WEBHOOK_RETRY] Error retrying webhook ${error.id}:`, retryError);

        // Update error status
        await updateWebhookErrorLog(
          error.id,
          'failed',
          error.retry_count + 1,
          retryError instanceof Error ? retryError.message : String(retryError)
        );

        results.push({
          error_id: error.id,
          event_type: error.event_type,
          subscription_id: error.subscription_id,
          success: false,
          message: retryError instanceof Error ? retryError.message : String(retryError)
        });
      }
    }

    return NextResponse.json(
      {
        success: true,
        message: `Processed ${pendingErrors.length} pending webhook errors`,
        results
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK_RETRY] Error processing retry request:", error);
    return NextResponse.json(
      { success: false, message: "Error processing retry request" },
      { status: 500 }
    );
  }
}

/**
 * Get handler for webhook retry status
 * @param req The incoming request
 * @returns The response with retry statistics
 */
export async function GET(req: NextRequest) {
  try {
    // Get the API key from the headers
    const apiKey = req.headers.get("x-api-key") || "";

    // Verify the API key
    const expectedApiKey = process.env.WEBHOOK_RETRY_API_KEY;
    if (!expectedApiKey || apiKey !== expectedApiKey) {
      console.error("[RAZORPAY_WEBHOOK_RETRY] Invalid API key");
      return NextResponse.json(
        { success: false, message: "Invalid API key" },
        { status: 401 }
      );
    }

    // Get admin Supabase client for webhook operations
    const supabase = createClient();

    // Get retry statistics using raw SQL query
    const { data: stats, error } = await supabase
      .rpc('get_webhook_error_stats');

    if (error) {
      console.error("[RAZORPAY_WEBHOOK_RETRY] Error getting webhook error stats:", error);
      return NextResponse.json(
        { success: false, message: "Error getting webhook error stats" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: "Webhook error stats retrieved",
        stats
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK_RETRY] Error getting webhook error stats:", error);
    return NextResponse.json(
      { success: false, message: "Error getting webhook error stats" },
      { status: 500 }
    );
  }
}
