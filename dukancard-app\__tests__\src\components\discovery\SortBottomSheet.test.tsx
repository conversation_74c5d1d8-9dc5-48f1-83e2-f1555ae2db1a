import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import SortBottomSheet, {
  SortBottomSheetRef,
} from '@/src/components/discovery/SortBottomSheet';
import { useTheme } from '@/src/hooks/useTheme';

// Mock dependencies
jest.mock('@gorhom/bottom-sheet', () => {
  const RN = require('react-native');
  return {
    __esModule: true,
    default: RN.View,
    BottomSheetView: RN.View,
    BottomSheetFlatList: RN.FlatList,
  };
});
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      textPrimary: '#000',
      textSecondary: '#888',
      border: '#ccc',
      cardBackground: '#f0f0f0',
    },
    isDark: false,
  }),
}));

describe('SortBottomSheet', () => {
  const ref = React.createRef<SortBottomSheetRef>();

  it('renders correctly for products', () => {
    const { getByText } = render(
      <SortBottomSheet
        ref={ref}
        viewType="products"
        productSortBy="newest"
        businessSortBy="created_desc"
        onProductSortSelect={() => {}}
        onBusinessSortSelect={() => {}}
      />
    );

    expect(getByText('Sort Products')).toBeTruthy();
    expect(getByText('Newest First')).toBeTruthy();
  });

  it('renders correctly for business cards', () => {
    const { getByText } = render(
      <SortBottomSheet
        ref={ref}
        viewType="cards"
        productSortBy="newest"
        businessSortBy="created_desc"
        onProductSortSelect={() => {}}
        onBusinessSortSelect={() => {}}
      />
    );

    expect(getByText('Sort Business Cards')).toBeTruthy();
    expect(getByText('Most Liked')).toBeTruthy();
  });

  it('calls onProductSortSelect when a product sort option is selected', () => {
    const onProductSortSelect = jest.fn();
    const { getByText } = render(
      <SortBottomSheet
        ref={ref}
        viewType="products"
        productSortBy="newest"
        businessSortBy="created_desc"
        onProductSortSelect={onProductSortSelect}
        onBusinessSortSelect={() => {}}
      />
    );

    fireEvent.press(getByText('Name (A-Z)'));
    expect(onProductSortSelect).toHaveBeenCalledWith('name_asc');
  });

  it('calls onBusinessSortSelect when a business sort option is selected', () => {
    const onBusinessSortSelect = jest.fn();
    const { getByText } = render(
      <SortBottomSheet
        ref={ref}
        viewType="cards"
        productSortBy="newest"
        businessSortBy="created_desc"
        onProductSortSelect={() => {}}
        onBusinessSortSelect={onBusinessSortSelect}
      />
    );

    fireEvent.press(getByText('Highest Rated'));
    expect(onBusinessSortSelect).toHaveBeenCalledWith('rating_desc');
  });
});
