import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { EmailOTPForm } from '@/app/(main)/login/components/EmailOTPForm';

describe('EmailOTPForm', () => {
  const onEmailSubmit = jest.fn();
  const onOTPSubmit = jest.fn();
  const onResendOTP = jest.fn();
  const onBackToEmail = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders email form by default', () => {
    const { container } = render(
      <EmailOTPForm
        step="email"
        email=""
        countdown={0}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    expect(container).toBeInTheDocument();
  });

});