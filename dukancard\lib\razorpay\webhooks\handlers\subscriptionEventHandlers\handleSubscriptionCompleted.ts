import { RazorpayWebhookData, RazorpaySubscription } from "../../../types/api";
import { SupabaseClient } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/server";
import {
  extractWebhookTimestamp
} from "../utils";
import { webhookProcessor, type WebhookProcessingContext } from "../webhookProcessor";
import { updateSubscriptionWithBusinessProfile } from "../subscription-db-updater";

/**
 * Handle subscription.completed event
 *
 * This event is triggered when a subscription completes all billing cycles.
 * The subscription is downgraded to free plan.
 *
 * @param payload The webhook payload
 * @param supabase The Supabase client
 * @returns The result of handling the event
 */
export async function handleSubscriptionCompleted(
  payload: RazorpayWebhookData,
  _supabase: SupabaseClient,
  razorpayEventId?: string
): Promise<{ success: boolean; message: string }> {
  let context: WebhookProcessingContext | null = null;

  try {
    // Extract subscription data from payload
    const subscriptionData = payload.payload.subscription;

    if (!subscriptionData || !subscriptionData.entity) {
      console.error("[RAZORPAY_WEBHOOK] Subscription data not found in payload");
      return { success: false, message: "Subscription data not found in payload" };
    }

    // Cast to proper type to access properties
    const subscription = subscriptionData.entity as unknown as RazorpaySubscription;
    const subscriptionId = subscription.id;
    console.log(`[RAZORPAY_WEBHOOK] Subscription completed: ${subscriptionId}`);

    // Extract webhook timestamp from Razorpay payload for sequence validation
    const webhookTimestamp = extractWebhookTimestamp(payload);

    // Use centralized webhook processor for race condition protection
    context = {
      subscriptionId,
      eventType: 'subscription.completed',
      eventId: razorpayEventId || `completed_${subscriptionId}_${Date.now()}`,
      payload: payload as unknown as Record<string, unknown>,
      webhookTimestamp
    };

    const processingResult = await webhookProcessor.processWebhookEvent(context);
    if (!processingResult.shouldProcess) {
      return { success: processingResult.success, message: processingResult.message };
    }

    // Get admin client to bypass RLS
    const adminClient = createClient();

    // Check current subscription status to determine proper handling
    const { data: currentSubscription, error: fetchError } = await adminClient
      .from('payment_subscriptions')
      .select('subscription_status, plan_id, business_profile_id')
      .eq('razorpay_subscription_id', subscriptionId)
      .maybeSingle();

    if (fetchError) {
      console.error(`[RAZORPAY_WEBHOOK] Error fetching subscription ${subscriptionId}:`, fetchError);
      return { success: false, message: `Error fetching subscription: ${fetchError.message}` };
    }

    if (!currentSubscription) {
      console.log(`[RAZORPAY_WEBHOOK] No subscription found for ${subscriptionId}, skipping completion processing`);
      return { success: true, message: "No subscription found to complete" };
    }

    // Complete subscription: downgrade to free plan
    console.log(`[RAZORPAY_WEBHOOK] Completing subscription ${subscriptionId}, downgrading to free plan`);

    const now = new Date().toISOString();
    const updateResult = await updateSubscriptionWithBusinessProfile({
      subscription_id: subscriptionId,
      business_profile_id: currentSubscription.business_profile_id,
      subscription_status: 'active', // Set to active but with free plan
      has_active_subscription: false, // Free plan users don't have "active subscription"
      additional_data: {
        plan_id: 'free',
        plan_cycle: 'monthly',
        subscription_start_date: now,
        cancelled_at: now, // Mark as cancelled due to completion
        // Clear Razorpay-related fields
        razorpay_subscription_id: null,
        razorpay_customer_id: null,
        razorpay_plan_id: null,
        last_payment_id: null,
        last_payment_date: null,
        last_payment_method: null,
        updated_at: now
      }
    });

    // Mark event as processed
    if (updateResult.success) {
      await webhookProcessor.markEventAsSuccess(context.eventId, "Subscription completed and downgraded to free plan");
      console.log(`[RAZORPAY_WEBHOOK] Successfully completed subscription ${subscriptionId} and downgraded to free plan`);
      return { success: true, message: "Subscription completed and downgraded to free plan" };
    } else {
      await webhookProcessor.markEventAsFailed(context.eventId, updateResult.message);
      console.error(`[RAZORPAY_WEBHOOK] Failed to complete subscription ${subscriptionId}:`, updateResult.message);
      return updateResult;
    }
  } catch (error) {
    console.error("[RAZORPAY_WEBHOOK] Error handling subscription completed:", error);
    return {
      success: false,
      message: `Error handling subscription completed: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}