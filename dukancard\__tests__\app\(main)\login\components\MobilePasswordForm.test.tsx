import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MobilePasswordForm } from '@/app/(main)/login/components/MobilePasswordForm';

describe('MobilePasswordForm', () => {
  const mockOnSubmit = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders mobile and password input fields', () => {
    render(
      <MobilePasswordForm
        isPending={false}
        onSubmit={mockOnSubmit}
      />
    );
    expect(screen.getByText('Mobile Number')).toBeInTheDocument();
    expect(screen.getByText('Password')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('9876543210')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('••••••••')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('calls onSubmit with correct values', async () => {
    render(
      <MobilePasswordForm
        isPending={false}
        onSubmit={mockOnSubmit}
      />
    );
    const mobileInput = screen.getByLabelText(/mobile number/i);
    const passwordInput = screen.getByLabelText(/password/i);

    fireEvent.change(mobileInput, { target: { value: '9876543210' } });
    fireEvent.change(passwordInput, { target: { value: 'testpassword' } });
    fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        mobile: '9876543210',
        password: 'testpassword',
      }, expect.anything());
    });
  });

  it('disables submit button when isPending is true', () => {
    render(
      <MobilePasswordForm
        isPending={true}
        onSubmit={mockOnSubmit}
      />
    );
    expect(screen.getByRole('button', { name: /signing in.../i })).toBeDisabled();
  });
});
