import { DashboardLayout } from "@/src/components/shared/layout/DashboardLayout";
import React, { useEffect, useState, useCallback } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Image,
  ActivityIndicator,
  Alert,
  Linking,
} from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import { useAuth } from "@/src/contexts/AuthContext";
import { useRouter } from "expo-router";
import {
  User,
  ChevronRight,
  BarChart3,
  Package,
  Star,
  Users,
  Heart,
  Palette,
  LogOut,
  CreditCard,
  ImageIcon,
  Crown,
  QrCode, // Added QrCode icon
} from "lucide-react-native";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { ProfileSkeleton } from "@/src/components/ui/SkeletonLoader";
import { ErrorState } from "@/src/components/ui/ErrorState";
import { handleNetworkError, logError } from "@/src/utils/errorHandling";
import { createBusinessProfileStyles } from "@/styles/dashboard/business/business-profile-styles";
import { ThemeToggleButton } from "@/src/components/ui/ThemeToggleButton";

import ComingSoonModal from "@/src/components/ui/ComingSoonModal";

import ManageCardModal from "@/src/components/modals/business/ManageCardModal";
import ManageProductsModal from "@/src/components/modals/business/ManageProductsModal";
import { ProfileHeader } from "@/src/components/shared/ProfileHeader"; // Imported ProfileHeader
import ShareBusinessCardModal from "@/src/components/modals/business/ShareBusinessCardModal"; // Imported ShareBusinessCardModal
import { getBusinessProfile } from "@/backend/supabase/services/common/profileService";
import { Tables } from "@dukancard-types/supabase";

export default function BusinessProfileScreen() {
  const { user, profileStatus, signOut } = useAuth();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const [businessProfile, setBusinessProfile] =
    useState<Tables<'business_profiles'> | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Coming soon modal state
  const [comingSoonModal, setComingSoonModal] = useState({
    visible: false,
    featureName: "",
    description: "",
  });



  // Manage Card modal state
  const [isManageCardModalVisible, setManageCardModalVisible] = useState(false);

  // Manage Products Modal state
  const [isManageProductsModalVisible, setManageProductsModalVisible] = useState(false);

  // Share Business Card modal state
  const [isShareBusinessCardModalVisible, setShareBusinessCardModalVisible] = useState(false); // New state for modal

  // Use profile data from context
  const businessName = businessProfile?.business_name || "Business";

  // Fetch business profile if not available
  useEffect(() => {
    if (!businessProfile && user?.id) {
      setIsLoading(true);
      getBusinessProfile()
        .then((result) => {
          if (result.success && result.data) {
            setBusinessProfile(result.data);
          } else {
            // No business profile found, check if user is a customer and redirect
            if (profileStatus.roleStatus?.role === "customer") {
              router.replace("/(dashboard)/customer");
              return;
            }
            setError("No business profile found");
          }
          setIsLoading(false);
        })
        .catch((err) => {
          setError("Failed to load profile");
          setIsLoading(false);
        });
    }
  }, [businessProfile, user?.id, profileStatus.roleStatus?.role, router]);

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      const result = await getBusinessProfile();
      if (result.success && result.data) {
        setBusinessProfile(result.data);
      }
    } catch (error) {
      console.error("Error during refresh:", error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Helper function to show coming soon modal
  const showComingSoonModal = (featureName: string, description?: string) => {
    setComingSoonModal({
      visible: true,
      featureName,
      description:
        description ||
        `${featureName} is available on our website with full functionality and advanced management tools.`,
    });
  };

  // Helper function to close coming soon modal
  const closeComingSoonModal = () => {
    setComingSoonModal({
      visible: false,
      featureName: "",
      description: "",
    });
  };

  const isDark = colorScheme === "dark";
  const styles = createBusinessProfileStyles(colorScheme);

  return (
    <DashboardLayout
      userName={businessName}
      showNotifications={true}
      hideHeader={true}
    >
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={["#D4AF37"]}
            tintColor="#D4AF37"
          />
        }
      >
        {/* Profile Content - Matching Skeleton Layout */}
        {isLoading && !businessProfile ? (
          <ProfileSkeleton />
        ) : error ? (
          <ErrorState
            type="generic"
            title="Unable to load data"
            message={error}
            onRetry={() => handleRefresh()}
            showRetry={true}
            style={styles.errorContainer}
          />
        ) : (
          <View
            style={[
              styles.profileContent,
              { backgroundColor: isDark ? "#000000" : "#FFFFFF" },
            ]}
          >
            {/* Profile Header - Avatar, Name, and Stats */}
            {businessProfile && user && (
              <ProfileHeader
                avatarUrl={businessProfile.logo_url}
                profileName={businessName}
                likesCount={businessProfile.total_likes || 0}
                followersCount={businessProfile.total_subscriptions || 0}
                reviewsOrRatingsCount={businessProfile.average_rating || 0}
                isBusinessProfile={true}
              />
            )}

            {/* Show My Digital Card Button */}
            {businessProfile?.business_slug && (
              <TouchableOpacity
                style={[
                  styles.profileMenuItem,
                  { marginTop: 10, justifyContent: 'flex-start' },
                ]}
                onPress={() => setShareBusinessCardModalVisible(true)}
              >
                <QrCode size={20} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  Show My Digital Card
                </Text>
                <ChevronRight size={20} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>
            )}

            {/* Separator */}
            <View
              style={[
                styles.separator,
                { backgroundColor: isDark ? "#333" : "#e0e0e0" },
              ]}
            />
            
            {/* Business Menu Options */}
            <View style={styles.profileMenu}>
              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() => setManageCardModalVisible(true)}
              >
                <CreditCard size={20} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  Manage Card
                </Text>
                <ChevronRight size={20} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() => setManageProductsModalVisible(true)}
              >
                <Package size={20} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  Manage Products/Services
                </Text>
                <ChevronRight size={20} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() =>
                  showComingSoonModal(
                    "Gallery",
                    "Gallery management with drag-and-drop reordering, bulk uploads, image optimization, and advanced organization features are available on our website."
                  )
                }
              >
                <ImageIcon size={20} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  Gallery
                </Text>
                <ChevronRight size={20} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() =>
                  showComingSoonModal(
                    "Analytics",
                    "Comprehensive analytics including visitor insights, engagement metrics, conversion tracking, and detailed reports are available on our website."
                  )
                }
              >
                <BarChart3 size={20} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  Analytics
                </Text>
                <ChevronRight size={20} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() =>
                  showComingSoonModal(
                    "Manage Plan",
                    "Plan management including subscription upgrades, billing history, payment methods, and plan comparisons are available on our website."
                  )
                }
              >
                <Crown size={20} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.menuItemText,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  Manage Plan
                </Text>
                <ChevronRight size={20} color={isDark ? "#999" : "#666"} />
              </TouchableOpacity>

              
            </View>

            {/* Theme Toggle Section */}
            <View
              style={[
                styles.themeSection,
                { backgroundColor: isDark ? "#1a1a1a" : "#f8f9fa" },
              ]}
            >
              <View style={styles.themeSectionHeader}>
                <Palette size={20} color={isDark ? "#D4AF37" : "#D4AF37"} />
                <Text
                  style={[
                    styles.themeSectionTitle,
                    { color: isDark ? "#fff" : "#000" },
                  ]}
                >
                  App Theme
                </Text>
              </View>
              <ThemeToggleButton variant="profile" showLabel />
            </View>

            {/* Logout Button */}
            <TouchableOpacity
              style={[
                styles.logoutButton,
                { backgroundColor: isDark ? "#2a1a1a" : "#fef2f2" },
              ]}
              onPress={async () => {
                if (isLoggingOut) return; // Prevent multiple clicks

                setIsLoggingOut(true);
                try {
                  const { error } = await signOut();
                  if (error) {
                    console.error("Logout error:", error);
                    setIsLoggingOut(false); // Reset loading state on error
                  }
                  // On success, the auth context will handle navigation
                } catch (error) {
                  console.error("Logout error:", error);
                  setIsLoggingOut(false); // Reset loading state on error
                }
              }}
              disabled={isLoggingOut}
            >
              {isLoggingOut ? (
                <ActivityIndicator size="small" color="#ef4444" />
              ) : (
                <LogOut size={20} color="#ef4444" />
              )}
              <Text style={[styles.logoutButtonText, { color: "#ef4444" }]}>
                {isLoggingOut ? "Logging out..." : "Logout"}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Coming Soon Modal */}
      <ComingSoonModal
        visible={comingSoonModal.visible}
        onClose={closeComingSoonModal}
        featureName={comingSoonModal.featureName}
        description={comingSoonModal.description}
      />



      {/* Manage Card Modal */}
      <ManageCardModal
        visible={isManageCardModalVisible}
        onClose={() => setManageCardModalVisible(false)}
      />

      {/* Manage Products Modal */}
      <ManageProductsModal
        visible={isManageProductsModalVisible}
        onClose={() => setManageProductsModalVisible(false)}
      />

      {/* Share Business Card Modal */}
      {businessProfile?.business_slug && (
        <ShareBusinessCardModal
          visible={isShareBusinessCardModalVisible}
          onClose={() => setShareBusinessCardModalVisible(false)}
          businessSlug={businessProfile.business_slug}
          businessName={businessProfile.business_name || ""}
          businessLogo={businessProfile.logo_url}
          isDark={isDark}
        />
      )}
    </DashboardLayout>
  );
}
