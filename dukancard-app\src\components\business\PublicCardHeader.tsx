import React, { useState } from "react";
import { View, Text, Image, TouchableOpacity } from "react-native";
import { ArrowLeft } from "lucide-react-native";
import QRCode from "react-qr-code";
import { BusinessDiscoveryData } from "@/src/types/business";
import { createPublicCardViewStyles } from "@/styles/PublicCardViewStyles";
import { generateDukancardUrl } from "@/src/utils/qrCodeUtils";

interface PublicCardHeaderProps {
  businessData: BusinessDiscoveryData;
  isDark: boolean;
  onClose?: () => void;
  distanceKm: string | null; // Keep the prop name for backward compatibility
}

export default function PublicCardHeader({
  businessData,
  isDark,
  onClose,
  distanceKm,
}: PublicCardHeaderProps) {
  const [imageError, setImageError] = useState(false);
  const styles = createPublicCardViewStyles(isDark);
  const themeColor = businessData.data?.theme_color || "#D4AF37";
  const qrValue = businessData.data?.business_slug
    ? generateDukancardUrl(businessData.data.business_slug)
    : "";
  const displayUrl = businessData.data?.business_slug
    ? `dukancard.in/${businessData.data.business_slug}`
    : "";

  return (
    <View
      style={[
        styles.header,
        {
          backgroundColor: themeColor, // Use proper gold theme color
          paddingBottom: 20,
          alignItems: "stretch", // Override center alignment
          borderBottomLeftRadius: 0, // Remove rounded corners
          borderBottomRightRadius: 0, // Remove rounded corners
        },
      ]}
    >
      {/* Back Button */}
      <TouchableOpacity
        style={styles.backButtonContainer}
        onPress={onClose}
        activeOpacity={0.7}
      >
        <ArrowLeft color="#000" size={24} />
      </TouchableOpacity>

      {/* Top Row - Logo and QR Code in Two Columns */}
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
          paddingHorizontal: 20,
          paddingTop: 20,
          marginBottom: 16,
        }}
      >
        {/* Left Column - Business Logo */}
        <View
          style={{
            marginBottom: 0,
            width: 104, // Match QR container size (80 + 12*2 padding)
            height: 104,
            justifyContent: "center",
            alignItems: "center",
            // Remove shadow and container styling
          }}
        >
          {businessData.data?.logo_url && !imageError ? (
            <Image
              source={{ uri: businessData.data.logo_url }}
              style={{
                width: 104, // Match QR container size including padding
                height: 104,
                borderRadius: 52, // Keep rounded (half of 104)
                borderWidth: 3,
                borderColor: isDark
                  ? "rgba(255,255,255,0.3)"
                  : "rgba(0,0,0,0.1)",
              }}
              onError={() => setImageError(true)}
            />
          ) : (
            <View
              style={{
                width: 104, // Match QR container size including padding
                height: 104,
                borderRadius: 52, // Keep rounded (half of 104)
                justifyContent: "center",
                alignItems: "center",
                borderWidth: 3,
                borderColor: isDark
                  ? "rgba(255,255,255,0.3)"
                  : "rgba(0,0,0,0.1)",
                backgroundColor: isDark
                  ? "rgba(255,255,255,0.1)"
                  : "rgba(0,0,0,0.05)",
              }}
            >
              <Text
                style={{
                  fontSize: 36, // Increased to match larger logo
                  fontWeight: "800",
                  color: "#fff",
                  textShadowColor: "rgba(0, 0, 0, 0.3)",
                  textShadowOffset: { width: 0, height: 2 },
                  textShadowRadius: 4,
                }}
              >
                {businessData.data?.business_name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
        </View>

        {/* Right Column - QR Code */}
        {businessData.data?.business_slug && (
          <View
            style={{
              backgroundColor: "#fff",
              padding: 12,
              borderRadius: 12,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.15,
              shadowRadius: 8,
              elevation: 5,
              width: 104, // 80 + 12*2 padding
              height: 104,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <QRCode
              value={qrValue}
              size={80}
              style={{ height: "auto", maxWidth: "100%", width: "100%" }}
              viewBox="0 0 80 80"
              fgColor="#000000"
              bgColor="#FFFFFF"
            />
          </View>
        )}
      </View>

      {/* Bottom Row - Business Name and URL in Single Column with Background */}
      <View
        style={{
          marginHorizontal: 20,
          backgroundColor: isDark ? "rgba(0,0,0,0.3)" : "rgba(255,255,255,0.9)",
          borderRadius: 12,
          paddingVertical: 12,
          paddingHorizontal: 16,
          marginBottom: 10,
        }}
      >
        {/* Business Name */}
        <Text
          style={[
            styles.businessName,
            {
              marginBottom: 6,
              textAlign: "center",
              fontSize: 20,
              lineHeight: 24,
              color: isDark ? "#fff" : "#000",
              textShadowColor: "transparent", // Remove shadow
              textShadowOffset: { width: 0, height: 0 },
              textShadowRadius: 0,
            },
          ]}
          numberOfLines={2}
          ellipsizeMode="tail"
        >
          {businessData.data?.business_name}
        </Text>

        {/* URL below business name */}
        {displayUrl && (
          <Text
            style={{
              fontSize: 12,
              fontFamily: "monospace",
              color: isDark ? "rgba(255,255,255,0.8)" : "rgba(0,0,0,0.7)",
              fontWeight: "600",
              textAlign: "center",
            }}
            numberOfLines={1}
            ellipsizeMode="middle"
          >
            {displayUrl}
          </Text>
        )}

        {distanceKm && (
          <Text
            style={{
              fontSize: 14,
              fontWeight: "bold",
              color: isDark ? "#FFF" : "#000",
              textAlign: "center",
              marginTop: 8,
            }}
          >
            {distanceKm} away
          </Text>
        )}
      </View>
    </View>
  );
}
