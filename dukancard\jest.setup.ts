import '@testing-library/jest-dom';

// Polyfill for TextEncoder
global.TextEncoder = require('util').TextEncoder;

// Polyfill for Request, Response, Headers
// These are minimal polyfills to satisfy TypeScript and basic usage in tests.
// For more complex scenarios, consider a dedicated polyfill library or more detailed mocks.

// Mock the global Headers object
class MockHeaders {
  private headers: Map<string, string>;

  constructor(init?: HeadersInit) {
    this.headers = new Map();
    if (init) {
      if (Array.isArray(init)) {
        init.forEach(([name, value]) => this.headers.set(name.toLowerCase(), value));
      } else if (init instanceof Headers) {
        init.forEach((value, name) => this.headers.set(name.toLowerCase(), value));
      } else {
        Object.entries(init).forEach(([name, value]) => this.headers.set(name.toLowerCase(), value));
      }
    }
  }

  append(name: string, value: string) {
    this.headers.set(name.toLowerCase(), value);
  }

  delete(name: string) {
    this.headers.delete(name.toLowerCase());
  }

  get(name: string) {
    return this.headers.get(name.toLowerCase()) || null;
  }

  has(name: string) {
    return this.headers.has(name.toLowerCase());
  }

  set(name: string, value: string) {
    this.headers.set(name.toLowerCase(), value);
  }

  forEach(callbackfn: (value: string, key: string, parent: Headers) => void, thisArg?: any) {
    this.headers.forEach((value, key) => callbackfn.call(thisArg, value, key, this as any));
  }

  // Mock implementations for missing Headers methods
  getSetCookie(): string[] {
    return [];
  }

  *entries(): IterableIterator<[string, string]> {
    for (const entry of this.headers.entries()) {
      yield entry;
    }
  }

  *keys(): IterableIterator<string> {
    for (const key of this.headers.keys()) {
      yield key;
    }
  }

  *values(): IterableIterator<string> {
    for (const value of this.headers.values()) {
      yield value;
    }
  }

  [Symbol.iterator](): IterableIterator<[string, string]> {
    return this.entries();
  }
}
global.Headers = MockHeaders as any;

// Mock the global Request object
global.Request = class MockRequest {
  url: string;
  method: string;
  headers: Headers;
  constructor(input: RequestInfo | URL, init?: RequestInit) {
    this.url = typeof input === 'string' ? input : (input as Request).url || (input as URL).href;
    this.method = init?.method || 'GET';
    this.headers = new MockHeaders(init?.headers) as any;
  }
} as any;

// Mock the global Response object
global.Response = class MockResponse {
  status: number;
  ok: boolean;
  json: () => Promise<any>;
  text: () => Promise<string>;
  headers: Headers;
  constructor(body?: any, init?: ResponseInit) {
    this.status = init?.status || 200;
    this.ok = this.status >= 200 && this.status < 300;
    this.json = async () => JSON.parse(body);
    this.text = async () => String(body);
    this.headers = new MockHeaders(init?.headers) as any;
  }
} as any;

// Polyfill for IntersectionObserver
global.IntersectionObserver = class MockIntersectionObserver implements IntersectionObserver {
  readonly root: Element | null = null;
  readonly rootMargin: string = '';
  readonly thresholds: ReadonlyArray<number> = [];
  takeRecords = () => [];
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();

  constructor(callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {
    // Mock constructor, no actual functionality needed for most tests
  }
} as typeof IntersectionObserver;

jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      on: jest.fn(() => ({
        subscribe: jest.fn(),
        unsubscribe: jest.fn(),
      })),
    })),
    channel: jest.fn(() => ({
      on: jest.fn(() => ({
        subscribe: jest.fn(),
        unsubscribe: jest.fn(),
      })),
    })),
  })),
}));