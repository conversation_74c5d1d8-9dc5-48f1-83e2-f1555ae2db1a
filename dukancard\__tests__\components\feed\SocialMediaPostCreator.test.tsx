import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { toast } from 'sonner';
import SocialMediaPostCreator from '@/components/feed/shared/SocialMediaPostCreator';
import { createCustomerPost, updateCustomerPost, deleteCustomerPost } from '@/lib/actions/customerPosts';
import { uploadCustomerPostImage } from '@/lib/actions/shared/upload-customer-post-media';
import { compressImageUltraAggressiveClient } from '@/lib/utils/client-image-compression';

// Mock dependencies
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

jest.mock('@/lib/actions/customerPosts', () => ({
  createCustomerPost: jest.fn(),
  updateCustomerPost: jest.fn(),
  deleteCustomerPost: jest.fn()
}));

// Mock dynamic imports
jest.mock('@/lib/utils/client-image-compression', () => ({
  compressImageUltraAggressiveClient: jest.fn()
}));

jest.mock('@/lib/actions/shared/upload-customer-post-media', () => ({
  uploadCustomerPostImage: jest.fn()
}));



jest.mock('@/utils/supabase/client', () => ({
  createClient: () => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })
    },
    from: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: {
              name: 'Test User',
              avatar_url: null,
              pincode: '123456',
              city: 'Test City',
              state: 'Test State',
              locality: 'Test Locality'
            },
            error: null
          })
        })
      })
    })
  })
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    refresh: jest.fn()
  })
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, layout, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, layout, ...props }: any) => <button {...props}>{children}</button>,
    textarea: ({ children, layout, ...props }: any) => <textarea {...props}>{children}</textarea>
  },
  AnimatePresence: ({ children }: any) => children
}));

jest.mock('lucide-react', () => ({
  Image: () => <div>Image Icon</div>,
  MapPin: () => <div>MapPin Icon</div>,
  X: () => <div>X Icon</div>,
  Send: () => <div>Send Icon</div>,
  Loader2: () => <div>Loader Icon</div>
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, asChild, ...props }: any) => {
    if (asChild) {
      return children;
    }
    return <button {...props}>{children}</button>;
  }
}));

jest.mock('@/components/ui/avatar', () => ({
  Avatar: ({ children }: any) => <div>{children}</div>,
  AvatarFallback: ({ children }: any) => <div>{children}</div>,
  AvatarImage: ({ children }: any) => <div>{children}</div>
}));

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ alt, ...props }: any) => <img alt={alt} {...props} />
}));

jest.mock('@/components/feed/shared/forms/LocationDisplay', () => {
  return function LocationDisplay() {
    return <div>Location Display</div>;
  };
});

const mockCreateCustomerPost = createCustomerPost as jest.MockedFunction<typeof createCustomerPost>;
const mockUpdateCustomerPost = updateCustomerPost as jest.MockedFunction<typeof updateCustomerPost>;
const mockDeleteCustomerPost = deleteCustomerPost as jest.MockedFunction<typeof deleteCustomerPost>;
const mockUploadCustomerPostImage = uploadCustomerPostImage as jest.MockedFunction<typeof uploadCustomerPostImage>;
const mockCompressImage = compressImageUltraAggressiveClient as jest.MockedFunction<typeof compressImageUltraAggressiveClient>;
const mockToast = toast as jest.Mocked<typeof toast>;

// Mock URL.createObjectURL and URL.revokeObjectURL
Object.defineProperty(global, 'URL', {
  value: {
    createObjectURL: jest.fn(() => 'mock-url'),
    revokeObjectURL: jest.fn(),
  },
  writable: true,
});

describe('SocialMediaPostCreator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockToast.success = jest.fn();
    mockToast.error = jest.fn();

    // Mock the return values for the mocked functions
    mockCreateCustomerPost.mockClear();
    mockUploadCustomerPostImage.mockClear();
    mockDeleteCustomerPost.mockClear();
    mockCompressImage.mockClear();
  });

  it('should render and allow text input', async () => {
    render(<SocialMediaPostCreator />);

    // Click to expand the post creator
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Type content
    const textArea = screen.getByPlaceholderText(/What's on your mind/);
    fireEvent.change(textArea, { target: { value: 'Test post content' } });

    // Wait for the content to be processed
    await waitFor(() => {
      expect(textArea).toHaveValue('Test post content');
    });

    // Check that submit button is enabled when there's content
    const submitButton = screen.getByText('Post');
    expect(submitButton).not.toBeDisabled();

    // Check character count (look for the container with character count)
    const characterCountContainer = screen.getByText((content, element) => {
      return element?.textContent === '17/2000';
    });
    expect(characterCountContainer).toBeInTheDocument();
  });

  it('should create a post with image successfully', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockCompressedBlob = new Blob(['compressed'], { type: 'image/webp' });

    mockCreateCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    mockCompressImage.mockResolvedValue({
      blob: mockCompressedBlob,
      finalSizeKB: 50,
      compressionRatio: 0.5,
      dimensions: { width: 800, height: 600 }
    });

    mockUploadCustomerPostImage.mockResolvedValue({
      success: true,
      url: 'https://example.com/image.webp'
    });

    mockUpdateCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post updated'
    });

    render(<SocialMediaPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Add content
    const textArea = screen.getByPlaceholderText(/What's on your mind/);
    fireEvent.change(textArea, { target: { value: 'Post with image' } });

    // Add image
    const fileInput = document.getElementById('image-upload') as HTMLInputElement;
    fireEvent.change(fileInput, { target: { files: [mockFile] } });

    // Check that image preview appears
    await waitFor(() => {
      expect(screen.getByAltText('Post image preview')).toBeInTheDocument();
      expect(screen.getByAltText('Post image preview')).toHaveAttribute('src', 'mock-url');
    });

    // Check that submit button is enabled when there's content and image
    const submitButton = screen.getByText('Post');
    expect(submitButton).not.toBeDisabled();
  });

  it('should rollback post creation when image upload fails', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockCompressedBlob = new Blob(['compressed'], { type: 'image/webp' });

    mockCreateCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    mockCompressImage.mockResolvedValue({
      blob: mockCompressedBlob,
      finalSizeKB: 50,
      compressionRatio: 0.5,
      dimensions: { width: 800, height: 600 }
    });

    mockUploadCustomerPostImage.mockResolvedValue({
      success: false,
      error: 'Upload failed'
    });

    mockDeleteCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post deleted'
    });

    render(<SocialMediaPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Add content and image
    const textArea = screen.getByPlaceholderText(/What's on your mind/);
    fireEvent.change(textArea, { target: { value: 'Post with image' } });

    const fileInput = document.getElementById('image-upload') as HTMLInputElement;
    fireEvent.change(fileInput, { target: { files: [mockFile] } });

    // Check that image preview appears
    await waitFor(() => {
      expect(screen.getByAltText('Post image preview')).toBeInTheDocument();
    });

    // Check that submit button is enabled
    const submitButton = screen.getByText('Post');
    expect(submitButton).not.toBeDisabled();
  });

  it('should show character count', async () => {
    render(<SocialMediaPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Initially should show 0 characters (character count is not shown when empty)
    // Just check that the component is expanded and ready for input
    expect(screen.getByPlaceholderText(/What's on your mind/)).toBeInTheDocument();

    // Add content and check character count updates
    const textArea = screen.getByPlaceholderText(/What's on your mind/);
    fireEvent.change(textArea, { target: { value: 'Hello world!' } });

    await waitFor(() => {
      const characterCountContainer = screen.getByText((content, element) => {
        return element?.textContent === '12/2000';
      });
      expect(characterCountContainer).toBeInTheDocument();
    });
  });

  it('should validate required content', async () => {
    render(<SocialMediaPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Check that submit button is disabled when there's no content
    const submitButton = screen.getByText('Post');
    expect(submitButton).toBeDisabled();

    // Verify that createCustomerPost is not called
    expect(mockCreateCustomerPost).not.toHaveBeenCalled();
  });
});
