# Dukancard Brownfield Architecture Document

## Introduction

This document captures the CURRENT STATE of the Dukancard codebase, including technical debt, workarounds, and real-world patterns. It serves as a reference for AI agents working on enhancements and for creating a comprehensive test suite.

### Document Scope

Comprehensive documentation of the entire Dukancard Next.js application.

### Change Log

| Date       | Version | Description                 | Author  |
| ---------- | ------- | --------------------------- | ------- |
| 2025-07-19 | 1.0     | Initial brownfield analysis | Mary (Analyst) |

## High Level Architecture

### Technical Summary

Dukancard is a web application that allows users to create and manage digital cards. These cards can display products, and the application has a subscription-based model for its services, using Razorpay for payments. It includes user authentication, a dashboard for managing cards and products, and an onboarding process for new users. It also features a community feed system for businesses and customers to connect.

### Actual Tech Stack (from package.json)

| Category | Technology | Version | Notes |
| --- | --- | --- | --- |
| Runtime | Node.js | ^20 | |
| Framework | Next.js | ^15.2.4 | Using Turbopack for development |
| UI Library | React | ^19.0.0 | |
| Styling | Tailwind CSS | ^4 | |
| UI Components | Radix UI | Various | Used for accessible, unstyled components |
| Animations | Framer Motion | ^12.9.2 | |
| Backend | Supabase | ^2.50.0 | Database, Auth, and other backend services |
| Forms | React Hook Form | ^7.54.2 | |
| Validation | Zod | ^3.24.2 | |
| Testing | Jest, Playwright | ^29.7.0, ^1.54.0 | |
| Payments | Razorpay | (Implied) | Integration via custom library |
| Rate Limiting | Upstash Ratelimit | ^2.0.5 | |

### Repository Structure Reality Check

- Type: Monorepo (contains `dukancard` and `dukancard-app`)
- Package Manager: npm
- Notable: The project is split into a Next.js web app (`dukancard`) and a React Native mobile app (`dukancard-app`). This document focuses solely on the `dukancard` web application.

## Source Tree and Module Organization

### Project Structure (Actual)

```text
dukancard/
├── __tests__/         # Jest tests
├── app/               # Next.js App Router
│   ├── (auth)/        # Auth-related pages (login, signup)
│   ├── (dashboard)/   # Main application dashboard
│   │   └── dashboard/
│   │       ├── business/ # Dashboard for business users
│   │       └── customer/ # Dashboard for customer users
│   ├── (main)/        # Main marketing/landing pages
│   ├── (onboarding)/  # User onboarding flow
│   ├── [cardSlug]/    # Dynamic routes for viewing digital cards
│   ├── api/           # API routes
│   └── ...
├── components/        # Shared React components
├── hooks/             # Custom React hooks
├── lib/               # Core logic, services, and utilities
│   ├── actions/       # Server-side actions
│   ├── client/        # Client-side Supabase logic
│   ├── razorpay/      # Razorpay integration
│   ├── schemas/       # Zod validation schemas
│   ├── services/      # Business logic
│   └── subscription/  # Subscription management logic
├── public/            # Static assets
└── ...
```

### Key Modules and Their Purpose

- **`lib/actions`**: This is the core of the application's backend logic. It contains server-side actions organized by feature. This is where most of the business logic, data manipulation, and interactions with Supabase occur.
  - **Key Sub-directories:** `businessProfiles`, `products`, `customerPosts`, `subscription`, `user`.
- **`lib/services`**: Contains more specialized, cross-cutting services.
  - `realtimeService.ts`: Likely handles real-time updates using Supabase Realtime.
  - `subscription.ts`: Contains subscription-related logic.
- **`lib/schemas`**: Defines data validation schemas using Zod.
  - `authSchemas.ts`: For authentication-related data (e.g., login, signup).
  - `locationSchemas.ts`: For location-related data.
- **`lib/client`**: Handles client-side interactions with Supabase.
- **`lib/razorpay`**: Contains the logic for integrating with the Razorpay payment gateway.
- **`app`**: The heart of the frontend, using Next.js App Router. It's organized by route and feature.
- **`components`**: Contains shared, reusable React components.

## Data Models and APIs

### Data Models (Inferred from `lib/actions` and `lib/schemas`)

- **User:** Represents a user of the application.
- **Business Profile:** Represents a business entity on the platform.
- **Customer Profile:** Represents a customer entity on the platform.
- **Product:** Represents a product or service offered by a business.
- **Post:** Represents a post in the community feed.
- **Subscription:** Represents a user's subscription plan.
- **Location:** Represents a geographical location.
- **Category:** Represents a category for products or businesses.
- **Review:** Represents a review of a business.
- **Like:** Represents a "like" on a post or product.
- **Gallery:** Represents a gallery of images for a business.
- **Activity:** Represents a user activity.

### APIs

- **Internal API:** The application uses Next.js API routes in the `app/api` directory for its internal backend functionality.
- **External APIs:**
  - **Supabase:** Used for database, authentication, and other backend services.
  - **Razorpay:** Used for payment processing.
  - **Upstash:** Used for rate limiting.

## Technical Debt and Known Issues

1.  **Lack of Test Coverage:** The primary motivation for this documentation effort. Many parts of the application lack automated tests, making it difficult to refactor or add new features safely.
2.  **Potential for Inconsistent Patterns:** As with any rapidly developed project, there may be inconsistencies in coding patterns across different features.

## Integration Points and External Dependencies

| Service | Purpose | Integration Type | Key Files |
| --- | --- | --- | --- |
| Supabase | Backend Platform | SDK | `lib/client/`, `utils/supabase/` |
| Razorpay | Payments | Custom | `lib/razorpay/` |
| Upstash | Rate Limiting | SDK | `middleware.ts` |
| Google Analytics | Analytics | Component | `app/components/GoogleAnalytics.tsx` |
| Meta Pixel | Analytics | Component | `app/components/MetaPixel.tsx` |

## Development and Deployment

### Local Development Setup

1.  Run `npm install` to install dependencies.
2.  Run `npm run dev` to start the development server.

### Build and Deployment Process

- **Build Command**: `npm run build`
- **Deployment**: Likely automated via a CI/CD pipeline (details to be confirmed).

## Testing Reality

### Current Test Coverage

- Unit/Integration Tests exist for some parts of the application.
- The primary testing framework is **Jest**.
- Tests are located in the `__tests__` directory, mirroring the project structure.
- End-to-end tests may exist using **Playwright** (in the `e2e` directory).

### Testing Conventions (from `middleware.test.ts`)

- **Mocking:** Dependencies are heavily mocked using `jest.mock`. This is the established pattern.
- **Structure:** Tests are organized with `describe` and `it` blocks.
- **Setup/Teardown:** `beforeEach` and `afterAll` are used for setting up test conditions and cleaning up.
- **Environment:** Test-specific environment variables are set within the test files.
- **Spies:** `jest.spyOn` is used to monitor function calls (e.g., `console.warn`).

---
