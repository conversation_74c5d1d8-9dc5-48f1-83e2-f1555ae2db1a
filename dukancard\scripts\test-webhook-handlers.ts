#!/usr/bin/env tsx

/**
 * WEBHOOK HANDLERS TEST
 * 
 * Tests actual webhook handlers with proper environment loading
 * Uses the same patterns as production code
 */

import { config } from 'dotenv';
import { resolve } from 'path';
import type { RazorpayWebhookData } from '../lib/razorpay/types/api';

// Load environment variables the same way as production
config({ path: resolve(process.cwd(), '.env.local') });

const BUSINESS_ID = 'd4fe2395-3872-4522-9b67-0d280633f318';

interface TestResult {
  success: boolean;
  testName: string;
  message: string;
  duration: number;
  details?: Record<string, unknown>;
}

async function testFreePlanScenario(): Promise<TestResult> {
  const testName = 'Free Plan Scenario Test';
  const startTime = Date.now();

  try {
    // Dynamic import like production code
    const { createClient } = await import('../utils/supabase/server');
    const { handleSubscriptionCancelled } = await import('../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionCancelled');
    
    const supabase = createClient();
    const subscriptionId = `sub_test_free_${Date.now()}`;

    console.log('📋 Setting up active subscription...');
    
    // Setup active subscription
    await supabase
      .from('payment_subscriptions')
      .update({
        razorpay_subscription_id: subscriptionId,
        plan_id: 'growth',
        plan_cycle: 'monthly',
        subscription_status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('business_profile_id', BUSINESS_ID);

    await supabase
      .from('business_profiles')
      .update({
        has_active_subscription: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', BUSINESS_ID);

    // Get initial state
    const { data: initialSub } = await supabase
      .from('payment_subscriptions')
      .select('subscription_status, plan_id')
      .eq('business_profile_id', BUSINESS_ID)
      .single();

    const { data: initialProfile } = await supabase
      .from('business_profiles')
      .select('has_active_subscription')
      .eq('id', BUSINESS_ID)
      .single();

    console.log(`   Initial: ${initialSub?.subscription_status}/${initialSub?.plan_id}/${initialProfile?.has_active_subscription}`);

    // Create realistic webhook payload
    const payload = {
      entity: 'event',
      account_id: 'acc_test',
      event: 'subscription.cancelled',
      contains: ['subscription'],
      payload: {
        subscription: {
          entity: {
            id: subscriptionId,
            status: 'cancelled',
            notes: {
              business_profile_id: BUSINESS_ID,
              plan_type: 'growth',
              plan_cycle: 'monthly'
            }
          }
        }
      },
      created_at: Math.floor(Date.now() / 1000)
    };

    console.log('🔄 Processing cancellation webhook...');
    
    // Use actual webhook handler
    const result = await handleSubscriptionCancelled(payload as unknown as RazorpayWebhookData, supabase, `test_free_${Date.now()}`);

    console.log(`   Webhook result: ${result.success ? '✅' : '❌'} ${result.message}`);

    // Get final state
    const { data: finalSub } = await supabase
      .from('payment_subscriptions')
      .select('subscription_status, plan_id')
      .eq('business_profile_id', BUSINESS_ID)
      .single();

    const { data: finalProfile } = await supabase
      .from('business_profiles')
      .select('has_active_subscription')
      .eq('id', BUSINESS_ID)
      .single();

    console.log(`   Final: ${finalSub?.subscription_status}/${finalSub?.plan_id}/${finalProfile?.has_active_subscription}`);

    // Verify free plan requirements
    const isCorrect = 
      result.success &&
      finalSub?.plan_id === 'free' &&
      finalProfile?.has_active_subscription === false &&
      finalSub?.subscription_status === 'active';

    return {
      success: isCorrect,
      testName,
      message: isCorrect 
        ? 'Free plan scenario working correctly - atomic downgrade successful'
        : 'Free plan scenario failed - incorrect final state',
      duration: (Date.now() - startTime) / 1000,
      details: {
        subscriptionId,
        webhookResult: result,
        initialState: { ...initialSub, ...initialProfile },
        finalState: { ...finalSub, ...finalProfile },
        expectedState: {
          planId: 'free',
          hasActiveSubscription: false,
          subscriptionStatus: 'active'
        }
      }
    };

  } catch (error) {
    return {
      success: false,
      testName,
      message: `Free plan test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration: (Date.now() - startTime) / 1000
    };
  }
}

async function testIdempotency(): Promise<TestResult> {
  const testName = 'Webhook Idempotency Test';
  const startTime = Date.now();

  try {
    // Dynamic import like production code
    const { createClient } = await import('../utils/supabase/server');
    const { handleSubscriptionCancelled } = await import('../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionCancelled');
    
    const supabase = createClient();
    const subscriptionId = `sub_test_idem_${Date.now()}`;
    const eventId = `test_idem_${Date.now()}`;

    console.log('📋 Setting up active subscription...');
    
    // Setup active subscription
    await supabase
      .from('payment_subscriptions')
      .update({
        razorpay_subscription_id: subscriptionId,
        plan_id: 'growth',
        plan_cycle: 'monthly',
        subscription_status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('business_profile_id', BUSINESS_ID);

    // Create webhook payload
    const payload = {
      entity: 'event',
      account_id: 'acc_test',
      event: 'subscription.cancelled',
      contains: ['subscription'],
      payload: {
        subscription: {
          entity: {
            id: subscriptionId,
            status: 'cancelled',
            notes: {
              business_profile_id: BUSINESS_ID,
              plan_type: 'growth',
              plan_cycle: 'monthly'
            }
          }
        }
      },
      created_at: Math.floor(Date.now() / 1000)
    };

    console.log('🔄 Processing webhook (1st time)...');
    const firstResult = await handleSubscriptionCancelled(payload as unknown as RazorpayWebhookData, supabase, eventId);
    console.log(`   First: ${firstResult.success ? '✅' : '❌'} ${firstResult.message}`);

    // Get state after first processing
    const { data: firstState } = await supabase
      .from('payment_subscriptions')
      .select('subscription_status, plan_id')
      .eq('business_profile_id', BUSINESS_ID)
      .single();

    console.log('🔄 Processing same webhook (2nd time)...');
    const secondResult = await handleSubscriptionCancelled(payload as unknown as RazorpayWebhookData, supabase, eventId);
    console.log(`   Second: ${secondResult.success ? '✅' : '❌'} ${secondResult.message}`);

    // Get state after second processing
    const { data: secondState } = await supabase
      .from('payment_subscriptions')
      .select('subscription_status, plan_id')
      .eq('business_profile_id', BUSINESS_ID)
      .single();

    // Check if idempotent
    const stateUnchanged = JSON.stringify(firstState) === JSON.stringify(secondState);
    const wasIdempotent = firstResult.success && (!secondResult.success || stateUnchanged);

    return {
      success: wasIdempotent,
      testName,
      message: wasIdempotent 
        ? 'Idempotency working correctly - duplicate webhook handled properly'
        : 'Idempotency failed - duplicate processing detected',
      duration: (Date.now() - startTime) / 1000,
      details: {
        subscriptionId,
        eventId,
        firstResult,
        secondResult,
        stateUnchanged,
        wasIdempotent
      }
    };

  } catch (error) {
    return {
      success: false,
      testName,
      message: `Idempotency test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration: (Date.now() - startTime) / 1000
    };
  }
}

async function testRaceCondition(): Promise<TestResult> {
  const testName = 'Race Condition Test';
  const startTime = Date.now();

  try {
    // Dynamic import like production code
    const { createClient } = await import('../utils/supabase/server');
    const { handleSubscriptionCancelled } = await import('../lib/razorpay/webhooks/handlers/subscriptionEventHandlers/handleSubscriptionCancelled');
    
    const supabase = createClient();
    const subscriptionId = `sub_test_race_${Date.now()}`;
    const simultaneousRequests = 3;

    console.log('📋 Setting up active subscription...');
    
    // Setup active subscription
    await supabase
      .from('payment_subscriptions')
      .update({
        razorpay_subscription_id: subscriptionId,
        plan_id: 'growth',
        plan_cycle: 'monthly',
        subscription_status: 'active',
        updated_at: new Date().toISOString()
      })
      .eq('business_profile_id', BUSINESS_ID);

    // Create webhook payload
    const payload = {
      entity: 'event',
      account_id: 'acc_test',
      event: 'subscription.cancelled',
      contains: ['subscription'],
      payload: {
        subscription: {
          entity: {
            id: subscriptionId,
            status: 'cancelled',
            notes: {
              business_profile_id: BUSINESS_ID,
              plan_type: 'growth',
              plan_cycle: 'monthly'
            }
          }
        }
      },
      created_at: Math.floor(Date.now() / 1000)
    };

    console.log(`🏁 Processing ${simultaneousRequests} simultaneous webhooks...`);

    // Process webhooks simultaneously with different event IDs
    const promises = Array.from({ length: simultaneousRequests }, (_, index) =>
      handleSubscriptionCancelled(payload as unknown as RazorpayWebhookData, supabase, `test_race_${Date.now()}_${index}`)
        .catch(error => ({ success: false, message: error.message, isError: true }))
    );

    const results = await Promise.all(promises);

    // Get final state
    const { data: finalState } = await supabase
      .from('payment_subscriptions')
      .select('subscription_status, plan_id')
      .eq('business_profile_id', BUSINESS_ID)
      .single();

    // Analyze results
    const successfulProcessing = results.filter(r => r.success && !(r as unknown as { isError?: boolean }).isError).length;
    const errors = results.filter(r => (r as unknown as { isError?: boolean }).isError).length;

    // For cancellation, we expect the subscription to be downgraded to free plan
    const correctFinalState = finalState?.plan_id === 'free';

    const success = errors === 0 && correctFinalState && successfulProcessing >= 1;

    return {
      success,
      testName,
      message: `Race condition test: ${successfulProcessing} successful, ${errors} errors. Final state: ${finalState?.plan_id}`,
      duration: (Date.now() - startTime) / 1000,
      details: {
        subscriptionId,
        simultaneousRequests,
        successfulProcessing,
        errors,
        results,
        finalState,
        correctFinalState
      }
    };

  } catch (error) {
    return {
      success: false,
      testName,
      message: `Race condition test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      duration: (Date.now() - startTime) / 1000
    };
  }
}

async function main() {
  console.log('🚀 Webhook Handlers Test - Production Code Testing\n');
  console.log(`📋 Business ID: ${BUSINESS_ID}\n`);
  
  const tests = [
    { name: 'Free Plan Scenario', fn: testFreePlanScenario },
    { name: 'Webhook Idempotency', fn: testIdempotency },
    { name: 'Race Condition Handling', fn: testRaceCondition }
  ];

  const results: TestResult[] = [];

  for (const test of tests) {
    console.log(`\n🧪 Running: ${test.name}`);
    console.log('-'.repeat(50));
    
    const result = await test.fn();
    results.push(result);
    
    if (result.success) {
      console.log(`✅ ${test.name}: PASSED (${result.duration.toFixed(2)}s)`);
      console.log(`   ${result.message}`);
    } else {
      console.log(`❌ ${test.name}: FAILED (${result.duration.toFixed(2)}s)`);
      console.log(`   ${result.message}`);
    }

    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Generate report
  const totalTests = results.length;
  const passedTests = results.filter(r => r.success).length;
  const failedTests = totalTests - passedTests;
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 WEBHOOK HANDLERS TEST REPORT');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${failedTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  console.log('\n📋 Test Results:');
  results.forEach(result => {
    console.log(`  ${result.success ? '✅' : '❌'} ${result.testName}`);
  });
  
  if (failedTests > 0) {
    console.log('\n❌ Failed Tests Details:');
    results.filter(r => !r.success).forEach(result => {
      console.log(`   • ${result.testName}: ${result.message}`);
    });
  }
  
  console.log('\n🔍 Key Findings:');
  const freePlanTest = results.find(r => r.testName.includes('Free Plan'));
  if (freePlanTest) {
    console.log(`   • Free Plan Logic: ${freePlanTest.success ? '✅ Working' : '❌ Failed'}`);
  }
  
  const idempotencyTest = results.find(r => r.testName.includes('Idempotency'));
  if (idempotencyTest) {
    console.log(`   • Idempotency Protection: ${idempotencyTest.success ? '✅ Working' : '❌ Failed'}`);
  }
  
  const raceTest = results.find(r => r.testName.includes('Race'));
  if (raceTest) {
    console.log(`   • Race Condition Handling: ${raceTest.success ? '✅ Working' : '❌ Failed'}`);
  }
  
  console.log('='.repeat(60));
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL WEBHOOK TESTS PASSED!');
    console.log('🛡️  Webhook handlers are production-ready!');
    console.log('✅ Free plan logic verified');
    console.log('✅ Database operations atomic');
    console.log('✅ Real webhook handlers tested');
  } else {
    console.log('⚠️  Some tests failed. Review the details above.');
  }
  
  console.log('='.repeat(60));
  
  process.exit(passedTests === totalTests ? 0 : 1);
}

main().catch(console.error);
