import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useForm, FormProvider } from 'react-hook-form';
import { AddressStep } from '@/app/(onboarding)/onboarding/components/steps/AddressStep';

// Mock the lucide-react icons if they cause issues in tests
jest.mock('lucide-react', () => ({
  Home: ({ className }: { className: string }) => <svg data-testid="home-icon" className={className} />, 
  MapPin: ({ className }: { className: string }) => <svg data-testid="map-pin-icon" className={className} />,
  Building2: ({ className }: { className: string }) => <svg data-testid="building-icon" className={className} />,
  Loader2: ({ className }: { className: string }) => <svg data-testid="loader-icon" className={className} />,
}));

// Explicitly mock react-hook-form
jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: jest.fn(),
    register: jest.fn(),
    setValue: jest.fn(),
    formState: { errors: {} },
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

// Explicitly mock the UI components
jest.mock('@/components/ui/form', () => ({
  FormControl: ({ children, ...props }: { children: React.ReactNode }) => {
    // Pass through any props to children that might need them (like disabled state)
    return React.Children.map(children, child => {
      if (React.isValidElement(child)) {
        const childProps = child.props as Record<string, any>;
        return React.cloneElement(child as any, { ...props, ...childProps });
      }
      return child;
    });
  },
  FormField: ({ render, name }: { render: ({ field }: any) => React.ReactNode, name: string }) => {
    // Set default values for business status to match component behavior
    const defaultValue = name === 'businessStatus' ? 'online' : '';
    const [fieldValue, setFieldValue] = React.useState(defaultValue);

    const mockField = {
      name: name,
      value: fieldValue,
      onChange: (value: any) => {
        setFieldValue(value);
      },
      onBlur: jest.fn(),
    };
    return <div data-testid="form-field">{render({ field: mockField })}</div>;
  },
  FormItem: ({ children }: { children: React.ReactNode }) => <div data-testid="form-item">{children}</div>,
  FormLabel: ({ children, htmlFor }: { children: React.ReactNode, htmlFor?: string }) => {
    // Extract the field name from children to create proper label association
    const labelText = React.Children.toArray(children)
      .filter(child => typeof child === 'string')
      .join(' ');

    // Map label text to field names
    let fieldName = '';
    if (labelText.includes('Address Line')) fieldName = 'addressLine';
    else if (labelText.includes('Pincode')) fieldName = 'pincode';
    else if (labelText.includes('City')) fieldName = 'city';
    else if (labelText.includes('State')) fieldName = 'state';
    else if (labelText.includes('Locality')) fieldName = 'locality';
    else if (labelText.includes('Business Status')) fieldName = 'businessStatus';

    return <label data-testid="form-label" htmlFor={fieldName}>{children}</label>;
  },
  FormMessage: ({ children }: { children: React.ReactNode }) => <div data-testid="form-message">{children}</div>,
}));

jest.mock('@/components/ui/select', () => ({
  Select: ({ children, onValueChange, value, disabled }: any) => {
    // Recursively pass disabled prop to all children
    const passPropsToChildren = (children: any): any => {
      return React.Children.map(children, child => {
        if (React.isValidElement(child)) {
          const newProps: Record<string, any> = {
            onValueChange,
            value,
            disabled,
            ...(child.props as Record<string, any>)
          };

          // If the child has children, recursively pass props
          const childProps = child.props as Record<string, any>;
          if (childProps && typeof childProps === 'object' && 'children' in childProps) {
            newProps.children = passPropsToChildren(childProps.children);
          }

          return React.cloneElement(child as any, newProps);
        }
        return child;
      });
    };

    return (
      <div data-testid="select-mock-wrapper">
        {passPropsToChildren(children)}
      </div>
    );
  },
  SelectContent: ({ children }: any) => <div data-testid="select-content-mock">{children}</div>,
  SelectItem: ({ children, value }: any) => <option value={value} data-testid="select-item-mock">{children}</option>,
  SelectTrigger: ({ children, onValueChange, value, disabled }: any) => (
    <button
      data-testid="select-trigger-mock"
      role="combobox"
      aria-expanded="false"
      aria-haspopup="listbox"
      onClick={() => { /* Simulate opening dropdown */ }}
      disabled={disabled}
      aria-label="Locality/Area"
      id="locality"
    >
      {children}
    </button>
  ),
  SelectValue: ({ placeholder }: any) => <span data-testid="select-value-mock">{placeholder}</span>,
}));

jest.mock('@/components/ui/input', () => ({
  Input: ({ className, placeholder, type, pattern, inputMode, onChange, onKeyDown, disabled, name, ...props }: any) => {
    // Create a proper id based on the field name for label association
    const fieldId = name || placeholder?.toLowerCase().replace(/[^a-z]/g, '') || 'input';

    const handleChange = (e: any) => {
      let value = e.target.value;

      // Apply pincode validation for pincode field
      if (name === 'pincode') {
        // Only allow numeric characters
        value = value.replace(/[^0-9]/g, '');
        // Update the input value
        e.target.value = value;
      }

      if (onChange) {
        onChange(e);
      }
    };

    return (
      <input
        className={className}
        placeholder={placeholder}
        type={type}
        pattern={pattern}
        inputMode={inputMode}
        onChange={handleChange}
        onKeyDown={onKeyDown}
        disabled={disabled}
        name={name}
        id={fieldId}
        {...props}
      />
    );
  },
}));



describe('AddressStep', () => {
  const mockHandlePincodeChange = jest.fn();
  const mockForm = {
    control: {}, // Mock control object
    handleSubmit: jest.fn(),
    register: jest.fn(),
    setValue: jest.fn(),
    formState: { errors: {} },
    trigger: jest.fn(),
    setError: jest.fn(),
    clearErrors: jest.fn(),
    getValues: jest.fn(),
    watch: jest.fn(),
    getFieldState: jest.fn(),
    resetField: jest.fn(),
    reset: jest.fn(),
    setFocus: jest.fn(),
    unregister: jest.fn(),
    getFieldStates: jest.fn(),
  } as any;

  const defaultProps = {
    form: mockForm, // Pass the mock form directly
    isSubmitting: false,
    user: null,
    existingData: null,
    availableLocalities: [],
    isPincodeLoading: false,
    handlePincodeChange: mockHandlePincodeChange,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all form fields correctly', () => {
    render(<AddressStep {...defaultProps} />);

    expect(screen.getByLabelText(/Address Line/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Pincode/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/City/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/State/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Locality\/Area/i)).toBeInTheDocument();
    // For Business Status, check for the label text and buttons instead of label association
    expect(screen.getByText(/Business Status/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Online/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Offline/i })).toBeInTheDocument();
  });

  it('disables inputs when isSubmitting is true', () => {
    render(<AddressStep {...defaultProps} isSubmitting={true} />);

    expect(screen.getByLabelText(/Address Line/i)).toBeDisabled();
    expect(screen.getByLabelText(/Pincode/i)).toBeDisabled();
    expect(screen.getByLabelText(/City/i)).toBeDisabled();
    expect(screen.getByLabelText(/State/i)).toBeDisabled();
    expect(screen.getByRole('combobox', { name: /Locality\/Area/i })).toBeDisabled();
    expect(screen.getByRole('button', { name: /Online/i })).toBeDisabled();
    expect(screen.getByRole('button', { name: /Offline/i })).toBeDisabled();
  });

  it('Pincode input only allows numeric values', () => {
    render(<AddressStep {...defaultProps} />);

    const pincodeInput = screen.getByLabelText(/Pincode/i);
    fireEvent.change(pincodeInput, { target: { value: '123abc456' } });
    expect(pincodeInput).toHaveValue('123456');

    fireEvent.change(pincodeInput, { target: { value: '!@#$%' } });
    expect(pincodeInput).toHaveValue('');
  });

  it('calls handlePincodeChange when 6 digits are entered in pincode', () => {
    render(<AddressStep {...defaultProps} />);

    const pincodeInput = screen.getByLabelText(/Pincode/i);
    fireEvent.change(pincodeInput, { target: { value: '12345' } });
    expect(mockHandlePincodeChange).not.toHaveBeenCalled();

    fireEvent.change(pincodeInput, { target: { value: '123456' } });
    expect(mockHandlePincodeChange).toHaveBeenCalledTimes(1);
    expect(mockHandlePincodeChange).toHaveBeenCalledWith('123456');
  });

  it('shows loader icon when isPincodeLoading is true', () => {
    render(<AddressStep {...defaultProps} isPincodeLoading={true} />);

    expect(screen.getByTestId('loader-icon')).toBeInTheDocument();
  });

  it('City and State inputs are disabled and readOnly', () => {
    render(<AddressStep {...defaultProps} />);

    const cityInput = screen.getByLabelText(/City/i);
    const stateInput = screen.getByLabelText(/State/i);

    expect(cityInput).toBeDisabled();
    expect(cityInput).toHaveAttribute('readOnly');
    expect(stateInput).toBeDisabled();
    expect(stateInput).toHaveAttribute('readOnly');
  });

  it('Locality/Area select is disabled when no availableLocalities', () => {
    render(<AddressStep {...defaultProps} availableLocalities={[]} />);

    expect(screen.getByRole('combobox', { name: /Locality\/Area/i })).toBeDisabled();
    expect(screen.getByText('Enter Pincode first')).toBeInTheDocument();
  });

  it('Locality/Area select is enabled and shows options when availableLocalities are provided', async () => {
    const localities = ['Locality A', 'Locality B'];
    render(<AddressStep {...defaultProps} availableLocalities={localities} />);

    const localitySelect = screen.getByRole('combobox', { name: /Locality\/Area/i });
    expect(localitySelect).toBeEnabled();
    expect(screen.queryByText('Enter Pincode first')).not.toBeInTheDocument();

    fireEvent.mouseDown(localitySelect); // Open the select dropdown
    await waitFor(() => {
      expect(screen.getByText('Locality A')).toBeInTheDocument();
      expect(screen.getByText('Locality B')).toBeInTheDocument();
    });
  });

  it('Business Status buttons change value on click', () => {
    render(<AddressStep {...defaultProps} />);

    const onlineButton = screen.getByRole('button', { name: /Online/i });
    const offlineButton = screen.getByRole('button', { name: /Offline/i });

    // Initial state
    expect(onlineButton).toHaveClass('bg-primary/10');
    expect(offlineButton).not.toHaveClass('bg-primary/10');

    fireEvent.click(offlineButton);
    expect(offlineButton).toHaveClass('bg-primary/10');
    expect(onlineButton).not.toHaveClass('bg-primary/10');

    fireEvent.click(onlineButton);
    expect(onlineButton).toHaveClass('bg-primary/10');
    expect(offlineButton).not.toHaveClass('bg-primary/10');
  });
});