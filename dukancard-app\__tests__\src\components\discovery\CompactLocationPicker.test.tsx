import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { CompactLocationPicker } from '@/src/components/discovery/CompactLocationPicker';

const mockColors = {
  background: '#ffffff',
  border: '#cccccc',
  textPrimary: '#000000',
  textSecondary: '#888888',
  primary: '#C29D5B',
};

describe('CompactLocationPicker', () => {
  it('renders with default text when no location is provided', () => {
    const { getByText } = render(
      <CompactLocationPicker onLocationChange={() => {}} colors={mockColors} />
    );
    expect(getByText('Select Location')).toBeTruthy();
    expect(getByText('Tap to choose your location')).toBeTruthy();
  });

  it('renders with the provided location data', () => {
    const location = {
      locality: 'Koramangala',
      pincode: '560034',
      city: 'Bengaluru',
      latitude: 12.9352,
      longitude: 77.6245,
    };
    const { getByText } = render(
      <CompactLocationPicker
        location={location}
        onLocationChange={() => {}}
        colors={mockColors}
      />
    );
    expect(getByText('Koramangala, 560034, Bengaluru')).toBeTruthy();
    expect(getByText('Tap to change location')).toBeTruthy();
  });

  it('opens the full-screen selector on press', () => {
    const { getByTestId, queryByTestId } = render(
      <CompactLocationPicker onLocationChange={() => {}} colors={mockColors} />
    );

    expect(queryByTestId('full-screen-location-selector')).toBeNull();

    fireEvent.press(getByTestId('compact-location-picker'));

    expect(queryByTestId('full-screen-location-selector')).toBeTruthy();
  });

  it('shows an activity indicator when loading', () => {
    const { getByTestId } = render(
      <CompactLocationPicker
        onLocationChange={() => {}}
        isLoading={true}
        colors={mockColors}
      />
    );
    expect(getByTestId('activity-indicator')).toBeTruthy();
  });

  it('is disabled when the disabled prop is true', () => {
    const onLocationChange = jest.fn();
    const { getByTestId } = render(
      <CompactLocationPicker
        onLocationChange={onLocationChange}
        disabled={true}
        colors={mockColors}
      />
    );

    fireEvent.press(getByTestId('compact-location-picker'));
    expect(onLocationChange).not.toHaveBeenCalled();
  });
});
