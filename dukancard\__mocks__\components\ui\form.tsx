import React from 'react';

export const Form = ({ children, ...props }: any) => (
  <form data-testid="form-mock" {...props}>
    {children}
  </form>
);

export const FormControl = ({ children }: { children: React.ReactNode }) => (
  <div data-testid="form-control">{children}</div>
);

export const FormField = ({ control, name, render }: any) => {
  const field = {
    value: '',
    onChange: jest.fn(),
    onBlur: jest.fn(),
    name: name || 'test-field'
  };
  return <div data-testid="form-field">{render({ field })}</div>;
};

export const FormItem = ({ children }: { children: React.ReactNode }) => (
  <div data-testid="form-item">{children}</div>
);

export const FormLabel = ({ children, className }: any) => (
  <label data-testid="form-label" className={className}>{children}</label>
);

export const FormMessage = ({ children }: { children: React.ReactNode }) => (
  <div data-testid="form-message">{children}</div>
);
