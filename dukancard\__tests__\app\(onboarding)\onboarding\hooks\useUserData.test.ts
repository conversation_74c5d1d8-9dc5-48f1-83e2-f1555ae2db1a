import { renderHook, waitFor } from '@testing-library/react';
import { useUserData } from '@/app/(onboarding)/onboarding/hooks/useUserData';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { toast } from 'sonner';

// Mock external modules
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getSession: jest.fn(),
    },
  })),
}));
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
  },
}));

describe('useUserData', () => {
  const mockPush = jest.fn();
  const mockGetSession = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });
    (createClient as jest.Mock).mockImplementation(() => ({
      auth: {
        getSession: mockGetSession,
      },
    }));
  });

  it('sets a mock user when in a test environment', async () => {
    // Simulate test environment
    const originalNodeEnv = process.env.NODE_ENV;
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: 'test',
      configurable: true,
    });

    const { result } = renderHook(() => useUserData());

    await waitFor(() => {
      expect(result.current.user).toBeDefined();
      expect(result.current.user?.id).toBe('test-user-id');
      expect(result.current.user?.email).toBe('<EMAIL>');
      expect(mockGetSession).not.toHaveBeenCalled(); // Should not call Supabase in test env
    });

    // Clean up test environment variable
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: originalNodeEnv,
      configurable: true,
    });
  });

  it('sets the user when a successful session is returned', async () => {
    const originalNodeEnv = process.env.NODE_ENV;
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: 'development',
      configurable: true,
    });
    const mockUser = {
      id: 'real-user-id',
      email: '<EMAIL>',
      user_metadata: { name: 'Real User' },
      created_at: '2024-01-01T00:00:00Z'
    };
    mockGetSession.mockResolvedValueOnce({
      data: { session: { user: mockUser } },
      error: null,
    });

    const { result } = renderHook(() => useUserData());

    await waitFor(() => {
      expect(result.current.user).toEqual(mockUser);
      expect(mockGetSession).toHaveBeenCalledTimes(1);
      expect(toast.error).not.toHaveBeenCalled();
      expect(mockPush).not.toHaveBeenCalled();
    });

    // Clean up
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: originalNodeEnv,
      configurable: true,
    });
  });

  it('shows an error toast and redirects to login when session fails', async () => {
    const originalNodeEnv = process.env.NODE_ENV;
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: 'development',
      configurable: true,
    });
    mockGetSession.mockResolvedValueOnce({
      data: { session: null },
      error: { message: 'Auth error' },
    });

    const { result } = renderHook(() => useUserData());

    await waitFor(() => {
      expect(result.current.user).toBeNull();
      expect(mockGetSession).toHaveBeenCalledTimes(1);
      expect(toast.error).toHaveBeenCalledWith("Authentication error. Redirecting to login.");
      expect(mockPush).toHaveBeenCalledWith("/login");
    });

    // Clean up
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: originalNodeEnv,
      configurable: true,
    });
  });

  it('shows an error toast and redirects to login when session has no user', async () => {
    const originalNodeEnv = process.env.NODE_ENV;
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: 'development',
      configurable: true,
    });
    mockGetSession.mockResolvedValueOnce({
      data: { session: { user: null } },
      error: null,
    });

    const { result } = renderHook(() => useUserData());

    await waitFor(() => {
      expect(result.current.user).toBeNull();
      expect(mockGetSession).toHaveBeenCalledTimes(1);
      expect(toast.error).toHaveBeenCalledWith("Authentication error. Redirecting to login.");
      expect(mockPush).toHaveBeenCalledWith("/login");
    });

    // Clean up
    Object.defineProperty(process.env, 'NODE_ENV', {
      value: originalNodeEnv,
      configurable: true,
    });
  });
});
