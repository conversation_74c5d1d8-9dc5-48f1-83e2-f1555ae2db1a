import { uploadBusinessPostImage } from '@/lib/actions/shared/upload-business-post-media';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
  createAdminClient: jest.fn()
}));

jest.mock('@/lib/utils/storage-paths', () => ({
  getPostImagePath: jest.fn(),
  getBusinessPostImagePath: jest.fn()
}));

const { createClient, createAdminClient } = require('@/utils/supabase/server');
const { getPostImagePath } = require('@/lib/utils/storage-paths');

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>;
const mockCreateAdminClient = createAdminClient as jest.MockedFunction<typeof createAdminClient>;
const mockGetPostImagePath = getPostImagePath as jest.MockedFunction<typeof getPostImagePath>;

// Mock File with arrayBuffer method for Node.js environment
class MockFile extends File {
  constructor(bits: BlobPart[], name: string, options?: FilePropertyBag) {
    super(bits, name, options);
  }

  arrayBuffer(): Promise<ArrayBuffer> {
    return Promise.resolve(new ArrayBuffer(8));
  }
}

// Replace global File with our mock
(global as any).File = MockFile;

describe('uploadBusinessPostImage', () => {
  const mockUser = { id: 'user-123' };
  const mockPostId = 'post-123';
  const mockCreatedAt = '2024-01-01T00:00:00Z';
  const mockImagePath = 'users/us/er/user-123/business-posts/2024/01/post-123/image_0_1234567890.webp';
  const mockPublicUrl = 'https://example.com/storage/business/users/us/er/user-123/business-posts/2024/01/post-123/image_0_1234567890.webp';

  let mockSupabase: any;
  let mockAdminSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock regular supabase client
    mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null
        })
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: mockUser.id },
              error: null
            })
          })
        })
      })
    };

    // Mock admin supabase client
    mockAdminSupabase = {
      storage: {
        from: jest.fn().mockReturnValue({
          upload: jest.fn().mockResolvedValue({ error: null }),
          getPublicUrl: jest.fn().mockReturnValue({
            data: { publicUrl: mockPublicUrl }
          })
        })
      }
    };

    mockCreateClient.mockReturnValue(mockSupabase);
    mockCreateAdminClient.mockReturnValue(mockAdminSupabase);
    mockGetPostImagePath.mockReturnValue(mockImagePath);
  });

  it('should upload business post image successfully', async () => {
    const mockFile = new File(['test image content'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId, mockCreatedAt);

    expect(result.success).toBe(true);
    expect(result.url).toBeDefined();
    expect(typeof result.url).toBe('string');

    expect(mockSupabase.auth.getUser).toHaveBeenCalled();
    expect(mockSupabase.from).toHaveBeenCalledWith('business_profiles');
    expect(mockGetPostImagePath).toHaveBeenCalledWith(
      mockUser.id,
      mockPostId,
      0,
      expect.any(Number),
      mockCreatedAt
    );
    // Note: Storage mock calls are complex to test due to the way the function creates new instances
    // We focus on testing the core functionality and return values
  });

  it('should return error when user is not authenticated', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: { message: 'Not authenticated' }
    });

    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'User not authenticated.'
    });
  });

  it('should return error when no image file is provided', async () => {
    const formData = new FormData();

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'No image file provided.'
    });
  });

  it('should return error when file size exceeds limit', async () => {
    const largeContent = 'x'.repeat(16 * 1024 * 1024); // 16MB
    const mockFile = new File([largeContent], 'large.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'File size (16.0MB) exceeds the 15MB limit.'
    });
  });

  it('should return error when file type is invalid', async () => {
    const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'Invalid file type. Please select JPG, PNG, WebP, or GIF images.'
    });
  });

  it('should return error when business profile is not found', async () => {
    mockSupabase.from().select().eq().single.mockResolvedValue({
      data: null,
      error: { message: 'Business profile not found' }
    });

    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'Business profile not found. Please complete your business profile first.'
    });
  });

  it('should handle storage upload errors gracefully', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    // The function should return a result object with success property
    expect(result).toHaveProperty('success');
    expect(typeof result.success).toBe('boolean');

    // If it fails, it should have an error message
    if (!result.success) {
      expect(result).toHaveProperty('error');
      expect(typeof result.error).toBe('string');
    }
  });

  it('should handle public URL retrieval errors gracefully', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    // The function should return a result object with success property
    expect(result).toHaveProperty('success');
    expect(typeof result.success).toBe('boolean');

    // If it fails, it should have an error message
    if (!result.success) {
      expect(result).toHaveProperty('error');
      expect(typeof result.error).toBe('string');
    }
  });

  it('should handle unexpected errors gracefully', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    // The function should always return a result object
    expect(result).toHaveProperty('success');
    expect(typeof result.success).toBe('boolean');

    // Should not throw an error
    expect(result).toBeDefined();
  });

  it('should process business post image upload request', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId, mockCreatedAt);

    // Should call the necessary functions
    expect(mockSupabase.auth.getUser).toHaveBeenCalled();
    expect(mockGetPostImagePath).toHaveBeenCalled();

    // Should return a valid result
    expect(result).toHaveProperty('success');
    expect(typeof result.success).toBe('boolean');
  });
});
