import { Tables } from "@dukancard-types/supabase";

export interface BusinessActivitiesResponse {
  success: boolean;
  data?: Tables<'business_activities'>[];
  count?: number;
  error?: string;
}

export type ActivityFilterType =
  | "all"
  | "like"
  | "subscribe"
  | "rating"
  | "unread";
export type ActivitySortType = "newest" | "oldest" | "unread_first";

export interface GetBusinessActivitiesParams {
  businessProfileId: string;
  page?: number;
  limit?: number;
  filter?: ActivityFilterType;
  sort?: ActivitySortType;
  autoMarkAsRead?: boolean;
}

export interface MarkActivitiesAsReadParams {
  businessProfileId: string;
  activityIds?: string[] | "all";
}

export interface ActivityServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
