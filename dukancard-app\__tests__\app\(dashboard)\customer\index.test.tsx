import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import { View } from 'react-native';
import { PostSkeleton } from '@/src/components/feed/PostSkeleton';
import { ErrorState } from '@/src/components/ui/ErrorState';
import { UnifiedFeedList } from '@/src/components/feed/UnifiedFeedList';
import CustomerFeedScreen from '@/app/(dashboard)/customer/index';
import { useAuth } from '@/src/contexts/AuthContext';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useTheme } from '@/src/hooks/useTheme';
import { getUnifiedFeedPostsWithAuthors } from '@/lib/actions/posts/unifiedFeed';
import { supabase } from '@/lib/supabase';

// Mock external modules and components
jest.mock('@/src/contexts/AuthContext');
jest.mock('@/src/hooks/useColorScheme');
jest.mock('@/src/hooks/useTheme');
jest.mock('@/lib/actions/posts/unifiedFeed');
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
          limit: jest.fn(() => ({ data: [], error: null })),
        })),
      })),
    })),
  },
}));
jest.mock('@/src/components/shared/layout/DashboardLayout', () => ({
  DashboardLayout: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));
jest.mock('@/src/components/feed/UnifiedFeedList', () => ({
  UnifiedFeedList: () => <></>,
}));
jest.mock('@/src/components/shared/ui/LoadingSpinner', () => ({
  LoadingSpinner: () => <></>,
}));
jest.mock('@/src/components/ui/ErrorState', () => ({
  ErrorState: () => <></>,
}));
jest.mock('@/src/components/feed/PostSkeleton', () => ({
  PostSkeleton: () => <></>,
}));
jest.mock('@/src/utils/errorHandling', () => ({
  handleNetworkError: jest.fn((error) => ({
    type: 'generic',
    title: 'Error',
    message: error.message,
  })),
  logError: jest.fn(),
}));

describe('CustomerFeedScreen', () => {
  const mockUser = { id: 'user123', user_metadata: { name: 'Test Customer' }, email: '<EMAIL>' };
  const mockProfileStatus = { roleStatus: { hasCustomerProfile: true } };

  beforeEach(() => {
    (useAuth as jest.Mock).mockReturnValue({
      user: mockUser,
      profileStatus: mockProfileStatus,
    });
    (useColorScheme as jest.Mock).mockReturnValue('light');
    (useTheme as jest.Mock).mockReturnValue({ colors: { background: '#fff' } });
    (getUnifiedFeedPostsWithAuthors as jest.Mock).mockResolvedValue({
      success: true,
      data: { items: [], hasMore: false },
    });

    // Mock supabase responses for location loading
    supabase.from = jest.fn().mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({ data: null, error: null }),
          limit: jest.fn().mockReturnValue({ data: [], error: null }),
        }),
      }),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading skeleton initially', () => {
    (getUnifiedFeedPostsWithAuthors as jest.Mock).mockReturnValue(new Promise(() => {})); // Keep pending
    const { UNSAFE_getByType } = render(<CustomerFeedScreen />);
    expect(UNSAFE_getByType(PostSkeleton)).toBeDefined();
  });

  it('renders error state if feed loading fails', async () => {
    (getUnifiedFeedPostsWithAuthors as jest.Mock).mockResolvedValue({
      success: false,
      error: 'Failed to fetch posts',
    });
    const { UNSAFE_getByType } = render(<CustomerFeedScreen />);
    await waitFor(() => expect(UNSAFE_getByType(ErrorState)).toBeDefined());
  });

  it('renders UnifiedFeedList when posts are loaded successfully', async () => {
    (getUnifiedFeedPostsWithAuthors as jest.Mock).mockResolvedValue({
      success: true,
      data: { items: [{ id: '1', title: 'Test Post' }], hasMore: false },
    });
    const { UNSAFE_getByType } = render(<CustomerFeedScreen />);
    await waitFor(() => expect(UNSAFE_getByType(UnifiedFeedList)).toBeDefined());
  });

  it('calls loadUserLocation and loadInitialFeed on mount', async () => {
    render(<CustomerFeedScreen />);
    await waitFor(() => {
      expect(supabase.from).toHaveBeenCalledWith('subscriptions');
      expect(supabase.from).toHaveBeenCalledWith('customer_profiles');
      expect(supabase.from).toHaveBeenCalledWith('business_profiles');
      expect(getUnifiedFeedPostsWithAuthors).toHaveBeenCalled();
    });
  });

  it('passes correct props to UnifiedFeedList', async () => {
    const mockPosts = [{ id: '1', title: 'Test Post' }];
    (getUnifiedFeedPostsWithAuthors as jest.Mock).mockResolvedValue({
      success: true,
      data: { items: mockPosts, hasMore: true },
    });

    // Mock location data from supabase to be returned
    supabase.from = jest.fn().mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { city_slug: 'test-city', state_slug: 'test-state', locality_slug: 'test-locality', pincode: '123456' },
            error: null,
          }),
          limit: jest.fn().mockReturnValue({ data: [], error: null }),
        }),
      }),
    });

    const { UNSAFE_getByType } = render(<CustomerFeedScreen />);

    await waitFor(() => {
      const unifiedFeedListProps = UNSAFE_getByType(UnifiedFeedList).props;
      expect(unifiedFeedListProps.initialPosts).toEqual(mockPosts);
      expect(unifiedFeedListProps.initialHasMore).toBe(true);
      expect(unifiedFeedListProps.userName).toBe('Test Customer');
      expect(unifiedFeedListProps.userType).toBe('customer');
      expect(unifiedFeedListProps.citySlug).toBe('test-city');
      expect(unifiedFeedListProps.stateSlug).toBe('test-state');
      expect(unifiedFeedListProps.localitySlug).toBe('test-locality');
      expect(unifiedFeedListProps.pincode).toBe('123456');
    });
  });
});
