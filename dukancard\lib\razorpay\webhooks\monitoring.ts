/**
 * Webhook monitoring and alerting utilities
 * Provides comprehensive monitoring for webhook processing
 */

import { createClient } from "@/utils/supabase/server";

export interface WebhookMetrics {
  total_events: number;
  successful_events: number;
  failed_events: number;
  retrying_events: number;
  success_rate: number;
  average_processing_time?: number;
}

export interface CriticalAlert {
  type: 'SUBSCRIPTION_INCONSISTENCY' | 'HIGH_FAILURE_RATE' | 'PAYMENT_FAILURE' | 'TERMINAL_STATE_VIOLATION';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  message: string;
  entity_id?: string;
  subscription_id?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Get webhook processing metrics using Supabase function
 */
export async function getWebhookMetrics(hours: number = 24): Promise<WebhookMetrics> {
  const supabase = createClient();

  try {
    // Use the Supabase function for comprehensive metrics
    const { data: statsData, error: statsError } = await supabase
      .rpc('get_webhook_error_stats');

    if (statsError || !statsData || statsData.length === 0) {
      console.error('[MONITORING] Error fetching webhook stats:', statsError);
      return {
        total_events: 0,
        successful_events: 0,
        failed_events: 0,
        retrying_events: 0,
        success_rate: 0
      };
    }

    const stats = statsData[0];

    // For average processing time, we still need to calculate manually for the specified hours
    let average_processing_time: number | undefined;

    if (hours !== 24) {
      // If not 24 hours, calculate manually for the specific timeframe
      const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000).toISOString();

      const { data: timeData, error: timeError } = await supabase
        .from('processed_webhook_events')
        .select('status, processed_at, created_at')
        .gte('created_at', cutoffTime)
        .eq('status', 'processed');

      if (!timeError && timeData && timeData.length > 0) {
        const successfulWithTimes = timeData.filter(event =>
          event.processed_at && event.created_at
        );

        if (successfulWithTimes.length > 0) {
          const totalProcessingTime = successfulWithTimes.reduce((sum, event) => {
            const created = new Date(event.created_at).getTime();
            const processed = new Date(event.processed_at).getTime();
            return sum + (processed - created);
          }, 0);
          average_processing_time = totalProcessingTime / successfulWithTimes.length;
        }
      }
    }

    return {
      total_events: Number(stats.total_events),
      successful_events: Number(stats.successful_events),
      failed_events: Number(stats.failed_events),
      retrying_events: Number(stats.retrying_events),
      success_rate: Number(stats.success_rate),
      average_processing_time
    };

  } catch (error) {
    console.error('[MONITORING] Exception getting webhook metrics:', error);
    return {
      total_events: 0,
      successful_events: 0,
      failed_events: 0,
      retrying_events: 0,
      success_rate: 0
    };
  }
}

/**
 * Check for subscription inconsistencies using Supabase function
 */
export async function checkSubscriptionConsistencies(): Promise<CriticalAlert[]> {
  const supabase = createClient();
  const alerts: CriticalAlert[] = [];

  try {
    // Find subscriptions where payment_subscriptions and business_profiles are inconsistent
    const { data: inconsistencies, error } = await supabase
      .rpc('find_subscription_inconsistencies');

    if (error) {
      console.error('[MONITORING] Error checking subscription consistencies:', error);
      return alerts;
    }

    if (inconsistencies && inconsistencies.length > 0) {
      for (const inconsistency of inconsistencies) {
        alerts.push({
          type: 'SUBSCRIPTION_INCONSISTENCY',
          severity: 'HIGH',
          message: `Subscription inconsistency detected for business ${inconsistency.business_profile_id}: ${inconsistency.inconsistency_type}`,
          entity_id: inconsistency.business_profile_id,
          subscription_id: inconsistency.razorpay_subscription_id,
          metadata: {
            subscription_status: inconsistency.subscription_status,
            has_active_subscription: inconsistency.has_active_subscription,
            plan_id: inconsistency.plan_id,
            inconsistency_type: inconsistency.inconsistency_type
          }
        });
      }
    }

  } catch (error) {
    console.error('[MONITORING] Exception checking subscription consistencies:', error);
  }

  return alerts;
}

/**
 * Check webhook failure rate and generate alerts
 */
export async function checkWebhookHealth(): Promise<CriticalAlert[]> {
  const alerts: CriticalAlert[] = [];
  
  try {
    const metrics = await getWebhookMetrics(1); // Last 1 hour
    
    // Alert if success rate is below 95%
    if (metrics.total_events > 10 && metrics.success_rate < 95) {
      alerts.push({
        type: 'HIGH_FAILURE_RATE',
        severity: metrics.success_rate < 80 ? 'CRITICAL' : 'HIGH',
        message: `Webhook success rate is ${metrics.success_rate.toFixed(2)}% (${metrics.successful_events}/${metrics.total_events})`,
        metadata: { ...metrics }
      });
    }
    
    // Alert if average processing time is too high
    if (metrics.average_processing_time && metrics.average_processing_time > 30000) { // 30 seconds
      alerts.push({
        type: 'HIGH_FAILURE_RATE',
        severity: 'MEDIUM',
        message: `High webhook processing time: ${(metrics.average_processing_time / 1000).toFixed(2)} seconds`,
        metadata: { average_processing_time: metrics.average_processing_time }
      });
    }
    
  } catch (error) {
    console.error('[MONITORING] Exception checking webhook health:', error);
  }
  
  return alerts;
}

/**
 * Log critical alert
 */
export async function logCriticalAlert(alert: CriticalAlert): Promise<void> {
  try {
    console.error(`[CRITICAL_ALERT] ${alert.type}: ${alert.message}`, {
      severity: alert.severity,
      entity_id: alert.entity_id,
      subscription_id: alert.subscription_id,
      metadata: alert.metadata
    });
    
    // In a production environment, you might want to:
    // 1. Send alerts to monitoring services (e.g., Sentry, DataDog)
    // 2. Send notifications to Slack/Discord
    // 3. Create tickets in your issue tracking system
    // 4. Store alerts in a dedicated alerts table
    
    // Example: Store in alerts table (if you have one)
    const supabase = createClient();
    await supabase
      .from('system_alerts')
      .insert({
        alert_type: alert.type,
        severity: alert.severity,
        message: alert.message,
        entity_id: alert.entity_id,
        subscription_id: alert.subscription_id,
        metadata: alert.metadata,
        created_at: new Date().toISOString()
      });
    
  } catch (error) {
    console.error('[MONITORING] Error logging critical alert:', error);
  }
}

/**
 * Run comprehensive health check
 */
export async function runHealthCheck(): Promise<{
  healthy: boolean;
  alerts: CriticalAlert[];
  metrics: WebhookMetrics;
}> {
  const alerts: CriticalAlert[] = [];
  
  try {
    // Get metrics
    const metrics = await getWebhookMetrics(24);
    
    // Check webhook health
    const webhookAlerts = await checkWebhookHealth();
    alerts.push(...webhookAlerts);
    
    // Check subscription consistencies
    const consistencyAlerts = await checkSubscriptionConsistencies();
    alerts.push(...consistencyAlerts);
    
    // Log critical alerts
    for (const alert of alerts) {
      if (alert.severity === 'CRITICAL' || alert.severity === 'HIGH') {
        await logCriticalAlert(alert);
      }
    }
    
    const healthy = alerts.filter(a => a.severity === 'CRITICAL' || a.severity === 'HIGH').length === 0;
    
    return {
      healthy,
      alerts,
      metrics
    };
    
  } catch (error) {
    console.error('[MONITORING] Exception during health check:', error);
    
    const criticalAlert: CriticalAlert = {
      type: 'HIGH_FAILURE_RATE',
      severity: 'CRITICAL',
      message: `Health check failed: ${error instanceof Error ? error.message : String(error)}`
    };
    
    return {
      healthy: false,
      alerts: [criticalAlert],
      metrics: {
        total_events: 0,
        successful_events: 0,
        failed_events: 0,
        retrying_events: 0,
        success_rate: 0
      }
    };
  }
}
