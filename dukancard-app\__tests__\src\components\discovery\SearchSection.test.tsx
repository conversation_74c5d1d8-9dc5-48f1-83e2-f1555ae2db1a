import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { SearchSection } from '@/src/components/discovery/SearchSection';
import { useTheme } from '@/src/hooks/useTheme';

// Mock the useTheme hook
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      background: '#ffffff',
      cardBackground: '#f0f0f0',
      border: '#cccccc',
      shadow: '#000000',
      textPrimary: '#000000',
      textSecondary: '#888888',
      primary: '#C29D5B',
    },
  }),
}));

describe('SearchSection', () => {
  it('renders correctly for "cards" viewType', () => {
    const { getByPlaceholderText } = render(
      <SearchSection
        searchTerm=""
        onSearch={() => {}}
        viewType="cards"
        onSortPress={() => {}}
      />
    );
    expect(getByPlaceholderText('Search businesses...')).toBeTruthy();
  });

  it('renders correctly for "products" viewType', () => {
    const { getByPlaceholderText } = render(
      <SearchSection
        searchTerm=""
        onSearch={() => {}}
        viewType="products"
        onSortPress={() => {}}
      />
    );
    expect(getByPlaceholderText('Search products or services...')).toBeTruthy();
  });

  it('calls onSearch when the search button is pressed', () => {
    const onSearch = jest.fn();
    const { getByTestId, getByPlaceholderText } = render(
      <SearchSection
        searchTerm=""
        onSearch={onSearch}
        viewType="cards"
        onSortPress={() => {}}
      />
    );

    fireEvent.changeText(getByPlaceholderText('Search businesses...'), 'test');
    fireEvent.press(getByTestId('search-button'));

    expect(onSearch).toHaveBeenCalledWith('test');
  });

  it('calls onSortPress when the sort button is pressed', () => {
    const onSortPress = jest.fn();
    const { getByTestId } = render(
      <SearchSection
        searchTerm=""
        onSearch={() => {}}
        viewType="cards"
        onSortPress={onSortPress}
      />
    );

    fireEvent.press(getByTestId('sort-button'));
    expect(onSortPress).toHaveBeenCalled();
  });
});
