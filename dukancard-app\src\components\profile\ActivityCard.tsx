/**
 * ActivityCard Component for React Native
 * Reusable component for displaying business information in activity screens
 * Supports different action buttons (unlike, unsubscribe, view profile)
 */

import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { Tables } from "@dukancard-types/supabase";

export type BusinessProfiles = Tables<"business_profiles">;

// Types
export type ActivityType = "like" | "subscription" | "review";

export interface ActivityCardData {
  id: string;
  type: ActivityType;
  business_profiles: BusinessProfiles | null;
  // Additional fields for different activity types
  rating?: number; // For reviews
  review_text?: string | null; // For reviews
  created_at?: string; // For all types
  updated_at?: string; // For reviews
}

interface ActivityCardProps {
  activity: ActivityCardData;
  onAction?: (
    activityId: string,
    actionType: "unlike" | "unsubscribe" | "delete" | "edit"
  ) => Promise<void>;
  onVisitBusiness?: (businessSlug: string) => void;
  showActionButton?: boolean;
  actionButtonText?: string;
  actionButtonIcon?: keyof typeof Ionicons.glyphMap;
  actionButtonColor?: string;
}

export const ActivityCard: React.FC<ActivityCardProps> = ({
  activity,
  onAction,
  onVisitBusiness,
  showActionButton = true,
  actionButtonText,
  actionButtonIcon,
  actionButtonColor = "#ff4444",
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const colorScheme = useColorScheme();
  const styles = createActivityCardStyles(colorScheme || "light");

  const business = activity.business_profiles;

  if (!business) {
    return null;
  }

  // Get default action based on activity type
  const getDefaultAction = (): {
    text: string;
    icon: keyof typeof Ionicons.glyphMap;
    action: "unlike" | "unsubscribe" | "delete";
  } => {
    switch (activity.type) {
      case "like":
        return { text: "Unlike", icon: "heart-dislike", action: "unlike" };
      case "subscription":
        return {
          text: "Unsubscribe",
          icon: "notifications-off",
          action: "unsubscribe",
        };
      case "review":
        return { text: "Delete", icon: "trash", action: "delete" };
      default:
        return { text: "Remove", icon: "close", action: "delete" };
    }
  };

  const defaultAction = getDefaultAction();
  const finalActionText = actionButtonText || defaultAction.text;
  const finalActionIcon = actionButtonIcon || defaultAction.icon;

  const handleAction = () => {
    if (!onAction) return;

    const actionType =
      activity.type === "like"
        ? "unlike"
        : activity.type === "subscription"
        ? "unsubscribe"
        : "delete";

    Alert.alert(
      `${finalActionText} ${
        activity.type === "review" ? "Review" : "Business"
      }`,
      `Are you sure you want to ${finalActionText.toLowerCase()} ${
        activity.type === "review" ? "this review" : business.business_name
      }?`,
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: finalActionText,
          style: "destructive",
          onPress: async () => {
            setIsLoading(true);
            try {
              await onAction(activity.id, actionType);
            } catch (error) {
              console.error(`Error performing ${actionType}:`, error);
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleVisitBusiness = () => {
    if (onVisitBusiness && business.business_slug) {
      onVisitBusiness(business.business_slug);
    } else {
    }
  };

  const getBusinessInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const formatAddress = () => {
    const parts = [];
    if (business.city) parts.push(business.city);
    if (business.state) parts.push(business.state);
    if (business.pincode) parts.push(business.pincode);
    return parts.join(", ");
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    } catch {
      return "Recently";
    }
  };

  const renderActivitySpecificInfo = () => {
    switch (activity.type) {
      case "review":
        return (
          <View style={styles.activityInfo}>
            <View style={styles.ratingContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Ionicons
                  key={star}
                  name={
                    star <= (activity.rating || 0) ? "star" : "star-outline"
                  }
                  size={14}
                  color="#D4AF37"
                />
              ))}
              <Text style={styles.ratingText}>({activity.rating}/5)</Text>
            </View>
            {activity.review_text && (
              <Text style={styles.reviewText} numberOfLines={2}>
                {activity.review_text}
              </Text>
            )}
            <Text style={styles.dateText}>
              Reviewed on {formatDate(activity.created_at || "")}
              {activity.updated_at !== activity.created_at && " (edited)"}
            </Text>
          </View>
        );
      case "like":
        return (
          <View style={styles.activityInfo}>
            <View style={styles.statusContainer}>
              <Ionicons name="heart" size={12} color="#ff4444" />
              <Text style={styles.statusText}>Liked</Text>
            </View>
            {activity.created_at && (
              <Text style={styles.dateText}>
                Liked on {formatDate(activity.created_at)}
              </Text>
            )}
          </View>
        );
      case "subscription":
        return (
          <View style={styles.activityInfo}>
            <View style={styles.statusContainer}>
              <Ionicons name="notifications" size={12} color="#4CAF50" />
              <Text style={[styles.statusText, { color: "#4CAF50" }]}>
                Following
              </Text>
            </View>
            {activity.created_at && (
              <Text style={styles.dateText}>
                Subscribed on {formatDate(activity.created_at || "")}
              </Text>
            )}
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.card}>
      <TouchableOpacity
        style={styles.cardContent}
        onPress={handleVisitBusiness}
        activeOpacity={0.7}
      >
        {/* Business Logo/Avatar */}
        <View style={styles.logoContainer}>
          {business.logo_url ? (
            <Image source={{ uri: business.logo_url }} style={styles.logo} />
          ) : (
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>
                {getBusinessInitials(business.business_name || "B")}
              </Text>
            </View>
          )}
        </View>

        {/* Business Info */}
        <View style={styles.businessInfo}>
          <Text style={styles.businessName} numberOfLines={1}>
            {business.business_name}
          </Text>
          {formatAddress() && (
            <Text style={styles.businessAddress} numberOfLines={1}>
              {formatAddress()}
            </Text>
          )}
          {renderActivitySpecificInfo()}
        </View>

        {/* Visit Button */}
        <TouchableOpacity
          style={styles.visitButton}
          onPress={handleVisitBusiness}
        >
          <Ionicons name="arrow-forward" size={16} color="#D4AF37" />
        </TouchableOpacity>
      </TouchableOpacity>

      {/* Action Button */}
      {showActionButton && onAction && (
        <TouchableOpacity
          style={[
            styles.actionButton,
            isLoading && styles.actionButtonDisabled,
            { backgroundColor: actionButtonColor },
          ]}
          onPress={handleAction}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Ionicons name={finalActionIcon} size={16} color="#fff" />
              <Text style={styles.actionText}>{finalActionText}</Text>
            </>
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

const createActivityCardStyles = (colorScheme: "light" | "dark" | null) => {
  const isDark = colorScheme === "dark";

  return StyleSheet.create({
    card: {
      backgroundColor: isDark ? "#1a1a1a" : "#fff",
      borderRadius: 12,
      marginBottom: 12,
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
      overflow: "hidden",
    },
    cardContent: {
      flexDirection: "row",
      alignItems: "center",
      padding: 16,
    },
    logoContainer: {
      marginRight: 12,
    },
    logo: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    logoPlaceholder: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: "#D4AF37",
      justifyContent: "center",
      alignItems: "center",
    },
    logoText: {
      color: "#fff",
      fontSize: 16,
      fontWeight: "bold",
    },
    businessInfo: {
      flex: 1,
      marginRight: 12,
    },
    businessName: {
      fontSize: 16,
      fontWeight: "600",
      color: isDark ? "#ffffff" : "#1a1a1a",
      marginBottom: 4,
    },
    businessAddress: {
      fontSize: 14,
      color: isDark ? "#cccccc" : "#666",
      marginBottom: 6,
    },
    activityInfo: {
      marginTop: 4,
    },
    statusContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 4,
    },
    statusText: {
      fontSize: 12,
      fontWeight: "500",
      marginLeft: 4,
    },
    ratingContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 4,
    },
    ratingText: {
      fontSize: 12,
      color: isDark ? "#cccccc" : "#666",
      marginLeft: 4,
    },
    reviewText: {
      fontSize: 14,
      color: isDark ? "#cccccc" : "#666",
      marginBottom: 4,
      lineHeight: 18,
    },
    dateText: {
      fontSize: 12,
      color: isDark ? "#999999" : "#999",
    },
    visitButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: "rgba(212, 175, 55, 0.1)",
      justifyContent: "center",
      alignItems: "center",
    },
    actionButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 12,
      paddingHorizontal: 16,
    },
    actionButtonDisabled: {
      backgroundColor: "#ccc",
    },
    actionText: {
      color: "#fff",
      fontSize: 14,
      fontWeight: "500",
      marginLeft: 6,
    },
  });
};
