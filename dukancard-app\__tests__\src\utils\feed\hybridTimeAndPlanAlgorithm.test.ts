import {
  processHybridTimeAndPlan,
  processCustomerPostsForHybrid,
  processBusinessPostsHybrid,
  groupPostsByPlan,
  getGuaranteedPostsPerPlan,
  getRemainingLatestPosts,
  mergeHybridPosts,
  getHybridAlgorithmStats,
  validateNoPostLoss,
} from '@/src/utils/feed/hybridTimeAndPlanAlgorithm';
import { applyDiversityRules } from '@/src/utils/feed/diversityEngine';
import { createMockUnifiedPost } from '../../../utils/mockData';

// Mock applyDiversityRules
jest.mock('@/src/utils/feed/diversityEngine', () => ({
  applyDiversityRules: jest.fn((posts) => posts), // Default: return posts as is
}));

describe('hybridTimeAndPlanAlgorithm', () => {
  const mockCustomerPosts = [
    createMockUnifiedPost({ id: 'c1', author_id: 'cust1', created_at: '2023-01-01T12:00:00Z', post_source: 'customer' }),
    createMockUnifiedPost({ id: 'c2', author_id: 'cust2', created_at: '2023-01-01T12:05:00Z', post_source: 'customer' }),
    createMockUnifiedPost({ id: 'c3', author_id: 'cust1', created_at: '2023-01-01T12:10:00Z', post_source: 'customer' }),
  ];

  const mockBusinessPosts = [
    createMockUnifiedPost({ id: 'b1', author_id: 'biz1', created_at: '2023-01-01T12:02:00Z', post_source: 'business', business_plan: 'enterprise' }),
    createMockUnifiedPost({ id: 'b2', author_id: 'biz2', created_at: '2023-01-01T12:07:00Z', post_source: 'business', business_plan: 'pro' }),
    createMockUnifiedPost({ id: 'b3', author_id: 'biz1', created_at: '2023-01-01T12:12:00Z', post_source: 'business', business_plan: 'enterprise' }),
    createMockUnifiedPost({ id: 'b4', author_id: 'biz3', created_at: '2023-01-01T11:00:00Z', post_source: 'business', business_plan: 'free' }),
  ];

  const allMockPosts = [...mockCustomerPosts, ...mockBusinessPosts];

  beforeEach(() => {
    jest.clearAllMocks();
    (applyDiversityRules as jest.Mock).mockImplementation((posts) => posts); // Reset mock to default behavior
  });

  describe('processHybridTimeAndPlan', () => {
    it('should return empty array if no posts', () => {
      expect(processHybridTimeAndPlan([])).toEqual([]);
    });

    it('should process and merge posts with diversity enabled', () => {
      const result = processHybridTimeAndPlan(allMockPosts);
      expect(applyDiversityRules).toHaveBeenCalledTimes(1);
      expect(result.length).toBe(allMockPosts.length);
    });

    it('should process and merge posts with diversity disabled', () => {
      const result = processHybridTimeAndPlan(allMockPosts, 1, { enableDiversity: false });
      expect(applyDiversityRules).not.toHaveBeenCalled();
      expect(result.length).toBe(allMockPosts.length);
    });

    it('should handle only customer posts', () => {
      const result = processHybridTimeAndPlan(mockCustomerPosts);
      expect(result.length).toBe(mockCustomerPosts.length);
      expect(result[0].id).toBe('c3'); // Latest customer post
    });

    it('should handle only business posts', () => {
      const result = processHybridTimeAndPlan(mockBusinessPosts);
      expect(result.length).toBe(mockBusinessPosts.length);
      expect(result[0].id).toBe('b3'); // Latest business post
    });
  });

  describe('processCustomerPostsForHybrid', () => {
    it('should sort customer posts chronologically (latest first)', () => {
      const result = processCustomerPostsForHybrid(mockCustomerPosts, false);
      expect(result[0].id).toBe('c3');
      expect(result[1].id).toBe('c2');
      expect(result[2].id).toBe('c1');
    });

    it('should apply diversity rules if enabled', () => {
      processCustomerPostsForHybrid(mockCustomerPosts, true);
      expect(applyDiversityRules).toHaveBeenCalledWith(expect.any(Array), { prioritizeRecency: true });
    });
  });

  describe('processBusinessPostsHybrid', () => {
    it('should prioritize guaranteed slots and then remaining latest posts', () => {
      const result = processBusinessPostsHybrid(mockBusinessPosts, 1, 10, 1);
      // Expect b3 (enterprise), b2 (pro), b4 (free) as guaranteed, then remaining sorted by time
      expect(result.length).toBe(mockBusinessPosts.length);
      expect(result[0].id).toBe('b3');
      expect(result[1].id).toBe('b2');
      expect(result[2].id).toBe('b1');
      expect(result[3].id).toBe('b4');
    });

    it('should handle pagination for guaranteed slots', () => {
      const resultPage2 = processBusinessPostsHybrid(mockBusinessPosts, 2, 10, 1);
      // For page 2, it should try to get the second latest from each plan
      // In this mock data, only enterprise has a second post (b1)
      expect(resultPage2.length).toBe(mockBusinessPosts.length);
    });
  });

  describe('groupPostsByPlan', () => {
    it('should group business posts by their plan', () => {
      const grouped = groupPostsByPlan(mockBusinessPosts);
      expect(grouped.get('enterprise')).toEqual([
        { id: 'b1', author_id: 'biz1', created_at: '2023-01-01T12:02:00Z', post_source: 'business', business_plan: 'enterprise' },
        { id: 'b3', author_id: 'biz1', created_at: '2023-01-01T12:12:00Z', post_source: 'business', business_plan: 'enterprise' },
      ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()));
      expect(grouped.get('pro')).toEqual([
        { id: 'b2', author_id: 'biz2', created_at: '2023-01-01T12:07:00Z', post_source: 'business', business_plan: 'pro' },
      ]);
      expect(grouped.get('free')).toEqual([
        { id: 'b4', author_id: 'biz3', created_at: '2023-01-01T11:00:00Z', post_source: 'business', business_plan: 'free' },
      ]);
    });
  });

  describe('getGuaranteedPostsPerPlan', () => {
    it('should select the latest post from each plan for guaranteed slots', () => {
      const postsByPlan = groupPostsByPlan(mockBusinessPosts);
      const guaranteed = getGuaranteedPostsPerPlan(postsByPlan, 1, 0);
      expect(guaranteed.length).toBe(3); // enterprise, pro, free
      expect(guaranteed).toContainEqual(expect.objectContaining({ id: 'b3' }));
      expect(guaranteed).toContainEqual(expect.objectContaining({ id: 'b2' }));
      expect(guaranteed).toContainEqual(expect.objectContaining({ id: 'b4' }));
    });

    it('should select posts based on pageOffset', () => {
      const postsByPlan = groupPostsByPlan(mockBusinessPosts);
      const guaranteed = getGuaranteedPostsPerPlan(postsByPlan, 1, 10); // Simulate page 2 (offset 10)
      expect(guaranteed.length).toBe(1); // Only enterprise has a second post
      expect(guaranteed).toContainEqual(expect.objectContaining({ id: 'b1' }));
    });
  });

  describe('getRemainingLatestPosts', () => {
    it('should return remaining latest posts excluding already selected ones', () => {
      const exclude = [
        createMockUnifiedPost({ id: 'b3', author_id: 'biz1', created_at: '2023-01-01T12:12:00Z', post_source: 'business', business_plan: 'enterprise' }),
        createMockUnifiedPost({ id: 'b2', author_id: 'biz2', created_at: '2023-01-01T12:07:00Z', post_source: 'business', business_plan: 'pro' }),
      ];
      const remaining = getRemainingLatestPosts(mockBusinessPosts, exclude, 2, 0);
      expect(remaining.length).toBe(2);
      expect(remaining).toContainEqual(expect.objectContaining({ id: 'b1' }));
      expect(remaining).toContainEqual(expect.objectContaining({ id: 'b4' }));
    });

    it('should return empty array if no remaining slots', () => {
      const remaining = getRemainingLatestPosts(mockBusinessPosts, [], 0, 0);
      expect(remaining).toEqual([]);
    });
  });

  describe('mergeHybridPosts', () => {
    it('should merge customer and business posts by timestamp', () => {
      const merged = mergeHybridPosts(mockCustomerPosts, mockBusinessPosts, 10, true);
      expect(merged.length).toBe(mockCustomerPosts.length + mockBusinessPosts.length);
      // Verify chronological order (latest first)
      expect(new Date(merged[0].created_at).getTime()).toBeGreaterThanOrEqual(new Date(merged[1].created_at).getTime());
    });

    it('should slice to targetCount', () => {
      const merged = mergeHybridPosts(mockCustomerPosts, mockBusinessPosts, 3, true);
      expect(merged.length).toBe(3);
    });
  });

  describe('getHybridAlgorithmStats', () => {
    it('should return correct statistics', () => {
      const processedPosts = [
        createMockUnifiedPost({ id: 'c1', author_id: 'cust1', created_at: '2023-01-01T12:00:00Z', post_source: 'customer' }),
        createMockUnifiedPost({ id: 'b1', author_id: 'biz1', created_at: '2023-01-01T12:02:00Z', post_source: 'business', business_plan: 'enterprise' }),
        createMockUnifiedPost({ id: 'b2', author_id: 'biz2', created_at: '2023-01-01T12:07:00Z', post_source: 'business', business_plan: 'pro' }),
      ];
      const stats = getHybridAlgorithmStats(allMockPosts, processedPosts);
      expect(stats.originalCount).toBe(allMockPosts.length);
      expect(stats.processedCount).toBe(processedPosts.length);
      expect(stats.customerPosts).toBe(1);
      expect(stats.businessPosts).toBe(2);
      expect(stats.planDistribution).toEqual({ enterprise: 1, pro: 1 });
    });
  });

  describe('validateNoPostLoss', () => {
    it('should return isValid true if no posts are lost', () => {
      const original = [createMockUnifiedPost({ id: '1' }), createMockUnifiedPost({ id: '2' })];
      const processed = [createMockUnifiedPost({ id: '1' }), createMockUnifiedPost({ id: '2' })];
      const result = validateNoPostLoss(original, processed, 2);
      expect(result.isValid).toBe(true);
      expect(result.issues).toEqual([]);
      expect(result.lostPosts).toEqual([]);
    });

    it('should return isValid false and lost posts if posts are missing', () => {
      const original = [createMockUnifiedPost({ id: '1' }), createMockUnifiedPost({ id: '2' }), createMockUnifiedPost({ id: '3' })];
      const processed = [createMockUnifiedPost({ id: '1' }), createMockUnifiedPost({ id: '3' })];
      const result = validateNoPostLoss(original, processed, 3);
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Lost 1 posts during processing');
      expect(result.lostPosts).toEqual(['2']);
    });

    it('should return isValid false if processed count exceeds expected count', () => {
      const original = [createMockUnifiedPost({ id: '1' })];
      const processed = [createMockUnifiedPost({ id: '1' }), createMockUnifiedPost({ id: '2' })];
      const result = validateNoPostLoss(original, processed, 1);
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Returned 2 posts, expected 1');
    });
  });
});