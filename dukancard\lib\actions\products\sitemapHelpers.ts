"use server";

import { createClient } from "@/utils/supabase/server";

// Define types for sitemap data
export type SitemapProductData = {
  product_slug: string;
  business_slug: string;
  updated_at: string | null;
};

/**
 * Securely fetch products for sitemap using the service role key
 */
export async function getSecureProductsForSitemap(): Promise<{
  data?: SitemapProductData[];
  error?: string;
}> {
  try {
    // Use the admin client with service role key to bypass RLS
    const supabase = await createClient();

    // Get all valid business profiles (online AND (has active subscription OR trial not expired))
    const nowISO = new Date().toISOString();
    const { data: businessProfiles, error: businessError } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("status", "online")
      .or(`has_active_subscription.eq.true,trial_end_date.gt.${nowISO}`);

    if (businessError) {
      return { error: "Database error fetching businesses." };
    }

    // If no businesses, return empty array
    if (!businessProfiles || businessProfiles.length === 0) {
      return { data: [] };
    }

    // Extract business IDs
    interface BusinessProfileId { id: string; }
    const businessIds = businessProfiles.map((profile: BusinessProfileId) => profile.id);

    // Fetch all products from online businesses
    const { data: products, error: productsError } = await supabase
      .from("products_services")
      .select(`
        id,
        slug,
        updated_at,
        business_id,
        business_profiles!business_id(business_slug)
      `)
      .in("business_id", businessIds)
      .eq("is_available", true)
      .not("slug", "is", null);

    if (productsError) {
      return { error: "Database error fetching products." };
    }

    if (!products || products.length === 0) {
      return { data: [] };
    }

    // Create a map to deduplicate by product URL (business_slug + product_slug)
    const uniqueProducts = new Map<string, SitemapProductData>();

    interface ProductServiceSitemapData {
      id: string;
      slug: string;
      updated_at: string;
      business_id: string;
      business_profiles: Array<{ business_slug: string }> | { business_slug: string } | null;
    }

    // Process products and deduplicate
    products.forEach((product: ProductServiceSitemapData) => {
      // Handle different possible shapes of business_profiles
      let businessSlug: string | undefined;

      if (product.business_profiles) {
        if (Array.isArray(product.business_profiles) && product.business_profiles.length > 0) {
          businessSlug = product.business_profiles[0].business_slug;
        } else if (typeof product.business_profiles === 'object') {
          businessSlug = (product.business_profiles as { business_slug?: string }).business_slug;
        }
      }

      const productSlug = product.slug;

      if (businessSlug && productSlug) {
        // Create a unique key for each product URL
        const key = `${businessSlug}/product/${productSlug}`;

        uniqueProducts.set(key, {
          product_slug: productSlug,
          business_slug: businessSlug,
          updated_at: product.updated_at
        });
      }
    });

    // Convert map values to array
    const sitemapData = Array.from(uniqueProducts.values());

    return { data: sitemapData };
  } catch (_e) {
    return { error: "An unexpected error occurred." };
  }
}
