import { Tables } from "@/types/supabase";

type BusinessProfiles = Tables<'business_profiles'>;

// Define sorting options for business profiles
export type BusinessSortBy =
  | "name_asc"
  | "name_desc"
  | "created_asc"
  | "created_desc"
  | "likes_asc"
  | "likes_desc"
  | "subscriptions_asc"
  | "subscriptions_desc"
  | "rating_asc"
  | "rating_desc";

// Define types for business profile with products
export type BusinessProfileWithProducts = BusinessProfiles & {
  products_services?: Record<string, unknown>[];
};

// Define types for sitemap data
export type SitemapProfileData = {
  business_slug: string;
  updated_at: string | null;
};

export type BusinessProfilePublicData = Omit<
  BusinessProfiles,
  "user_id" | "payment_subscriptions"
> & {
  subscription_status: string | null;
  plan_id: string | null;
};
