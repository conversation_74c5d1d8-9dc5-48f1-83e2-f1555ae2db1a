import { getProductServices } from '@/app/(dashboard)/dashboard/business/products/actions/getProducts';
import { createClient } from '@/utils/supabase/server';

// Mock the Supabase client
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe('getProductServices', () => {
  let mockQueryChain: any; // To hold the chainable query object

  beforeEach(() => {
    jest.clearAllMocks();

    mockQueryChain = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      or: jest.fn().mockReturnThis(),
      not: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn(), // This will be set in individual tests
    };

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn(),
      },
      from: jest.fn(() => mockQueryChain),
    });
  });

  it('should return an error if the user is not authenticated', async () => {
    // Arrange
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
      },
    });

    // Act
    const result = await getProductServices();

    // Assert
    expect(result.error).toBe('User not authenticated.');
  });

  it('should fetch products for an authenticated user', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProducts = [{
      id: 'prod-1',
      name: 'Product 1',
      product_type: 'product',
      description: 'Test product',
      base_price: 100,
      discounted_price: 90,
      is_available: true,
      image_url: null,
      images: [],
      featured_image_index: 0,
      slug: 'product-1',
      created_at: '2023-01-01T00:00:00.000Z',
      updated_at: '2023-01-01T00:00:00.000Z',
      product_variants: []
    }];

    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: mockProducts, error: null, count: 1 });

    // Act
    const result = await getProductServices();

    // Assert
    expect(mockSupabase().from).toHaveBeenCalledWith('products_services');
    expect(mockQueryChain.eq).toHaveBeenCalledWith('business_id', mockUser.id);
    expect(result.data).toHaveLength(1);
    expect(result.count).toBe(1);
  });

  it('should apply search term filter', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: [], error: null, count: 0 });

    // Act
    await getProductServices(1, 10, { searchTerm: 'test' });

    // Assert
    expect(mockQueryChain.or).toHaveBeenCalledWith('name.ilike.%test%,description.ilike.%test%');
  });

  it('should apply product type filter', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: [], error: null, count: 0 });

    // Act
    await getProductServices(1, 10, { productType: 'service' });

    // Assert
    expect(mockQueryChain.eq).toHaveBeenCalledWith('product_type', 'service');
  });

  it('should apply sorting for price and name', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: [], error: null, count: 0 });

    // Act
    await getProductServices(1, 10, {}, 'price_asc');
    await getProductServices(1, 10, {}, 'name_desc');

    // Assert
    expect(mockQueryChain.order).toHaveBeenCalledWith('discounted_price', { ascending: true, nullsFirst: false });
    expect(mockQueryChain.order).toHaveBeenCalledWith('base_price', { ascending: true, nullsFirst: false });
    expect(mockQueryChain.order).toHaveBeenCalledWith('name', { ascending: false });
  });

  it('should apply hasVariants filter (true)', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: [], error: null, count: 0 });

    // Act
    await getProductServices(1, 10, { hasVariants: true });

    // Assert
    expect(mockQueryChain.not).toHaveBeenCalledWith('product_variants', 'is', null);
  });

  it('should apply hasVariants filter (false)', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: [], error: null, count: 0 });

    // Act
    await getProductServices(1, 10, { hasVariants: false });

    // Assert
    expect(mockQueryChain.is).toHaveBeenCalledWith('product_variants', null);
  });

  it('should apply price_desc sorting', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: [], error: null, count: 0 });

    // Act
    await getProductServices(1, 10, {}, 'price_desc');

    // Assert
    expect(mockQueryChain.order).toHaveBeenCalledWith('discounted_price', { ascending: false, nullsFirst: false });
    expect(mockQueryChain.order).toHaveBeenCalledWith('base_price', { ascending: false, nullsFirst: false });
  });

  it('should apply name_asc sorting', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: [], error: null, count: 0 });

    // Act
    await getProductServices(1, 10, {}, 'name_asc');

    // Assert
    expect(mockQueryChain.order).toHaveBeenCalledWith('name', { ascending: true });
  });

  it('should apply available_first sorting', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: [], error: null, count: 0 });

    // Act
    await getProductServices(1, 10, {}, 'available_first');

    // Assert
    expect(mockQueryChain.order).toHaveBeenCalledWith('is_available', { ascending: false });
    expect(mockQueryChain.order).toHaveBeenCalledWith('created_at', { ascending: false });
  });

  it('should apply unavailable_first sorting', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: [], error: null, count: 0 });

    // Act
    await getProductServices(1, 10, {}, 'unavailable_first');

    // Assert
    expect(mockQueryChain.order).toHaveBeenCalledWith('is_available', { ascending: true });
    expect(mockQueryChain.order).toHaveBeenCalledWith('created_at', { ascending: false });
  });

  it('should apply variant_count_asc sorting', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProducts = [
      { id: 'prod-1', product_variants: [{ id: 'v1' }], created_at: '2023-01-01T00:00:00.000Z', updated_at: '2023-01-01T00:00:00.000Z' },
      { id: 'prod-2', product_variants: [{ id: 'v2' }, { id: 'v3' }], created_at: '2023-01-01T00:00:00.000Z', updated_at: '2023-01-01T00:00:00.000Z' },
      { id: 'prod-3', product_variants: [], created_at: '2023-01-01T00:00:00.000Z', updated_at: '2023-01-01T00:00:00.000Z' },
    ];
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: mockProducts, error: null, count: 3 });

    // Act
    const result = await getProductServices(1, 10, {}, 'variant_count_asc');

    // Assert
    expect(result.data?.map(p => p.id)).toEqual(['prod-3', 'prod-1', 'prod-2']);
  });

  it('should apply variant_count_desc sorting', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProducts = [
      { id: 'prod-1', product_variants: [{ id: 'v1' }], created_at: '2023-01-01T00:00:00.000Z', updated_at: '2023-01-01T00:00:00.000Z' },
      { id: 'prod-2', product_variants: [{ id: 'v2' }, { id: 'v3' }], created_at: '2023-01-01T00:00:00.000Z', updated_at: '2023-01-01T00:00:00.000Z' },
      { id: 'prod-3', product_variants: [], created_at: '2023-01-01T00:00:00.000Z', updated_at: '2023-01-01T00:00:00.000Z' },
    ];
    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: mockProducts, error: null, count: 3 });

    // Act
    const result = await getProductServices(1, 10, {}, 'variant_count_desc');

    // Assert
    expect(result.data?.map(p => p.id)).toEqual(['prod-2', 'prod-1', 'prod-3']);
  });

  it('should handle Supabase query error', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockError = new Error('Supabase query failed');

    mockSupabase().auth.getUser.mockResolvedValue({ data: { user: mockUser }, error: null });
    mockQueryChain.range.mockResolvedValue({ data: null, error: mockError, count: null });

    // Act
    const result = await getProductServices();

    // Assert
    expect(result.error).toBe('Failed to fetch products/services.');
    expect(result.data).toBeUndefined();
    expect(result.count).toBeUndefined();
  });
});