import { SupabaseClient } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/server";
import { SubscriptionState, WebhookProcessingContext } from "./types";
import { SubscriptionStateManager, SubscriptionStatus } from "../utils";
import { RazorpaySubscription } from "../../../types/api";

export class SubscriptionManager {
  private supabase: SupabaseClient;

  private constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient;
  }

  static async build(): Promise<SubscriptionManager> {
    const supabaseClient = await createClient();
    return new SubscriptionManager(supabaseClient);
  }

  async getCurrentSubscriptionState(subscriptionId: string): Promise<SubscriptionState | null> {
    const { data, error } = await this.supabase
      .from("payment_subscriptions")
      .select("id, business_profile_id, subscription_status, plan_id, cancelled_at, razorpay_subscription_id, updated_at, last_webhook_timestamp")
      .eq("razorpay_subscription_id", subscriptionId)
      .maybeSingle();

    if (error) {
      console.error(`[SubscriptionManager] Error fetching subscription state:`, error);
      return null;
    }

    return data;
  }

  async createMissingSubscriptionRecord(context: WebhookProcessingContext): Promise<SubscriptionState | null> {
    try {
      const payload = context.payload as { payload?: { subscription?: { entity: RazorpaySubscription } }, subscription?: { entity: RazorpaySubscription } };
      const subscription = payload?.payload?.subscription?.entity || payload?.subscription?.entity;

      if (!subscription) {
        console.error(`[SubscriptionManager] No subscription data in webhook payload for ${context.subscriptionId}`);
        return null;
      }

      const businessProfileId = subscription.notes?.business_profile_id || subscription.notes?.user_id;
      if (!businessProfileId) {
        console.error(`[SubscriptionManager] No business_profile_id in subscription notes for ${context.subscriptionId}`);
        return null;
      }

      const { data: businessProfile, error: profileError } = await this.supabase
        .from('business_profiles')
        .select('id')
        .eq('id', businessProfileId)
        .single();

      if (profileError || !businessProfile) {
        console.error(`[SubscriptionManager] Business profile ${businessProfileId} not found for subscription ${context.subscriptionId}`);
        return null;
      }

      const planType = subscription.notes?.plan_type || 'basic';
      const planCycle = subscription.notes?.plan_cycle || 'monthly';

      let initialStatus = 'created';
      if (context.eventType === 'subscription.authenticated') {
        initialStatus = 'authenticated';
      } else if (context.eventType === 'subscription.activated') {
        initialStatus = 'active';
      }

      const { data: newSubscription, error: createError } = await this.supabase
        .from('payment_subscriptions')
        .insert({
          business_profile_id: businessProfileId,
          razorpay_subscription_id: context.subscriptionId,
          plan_id: planType,
          plan_cycle: planCycle,
          subscription_status: initialStatus,
          razorpay_customer_id: subscription.customer_id || null,
          subscription_start_date: subscription.current_start ? new Date(subscription.current_start * 1000).toISOString() : null,
          subscription_expiry_time: subscription.current_end ? new Date(subscription.current_end * 1000).toISOString() : null,
          subscription_charge_time: subscription.charge_at ? new Date(subscription.charge_at * 1000).toISOString() : null,
          last_webhook_timestamp: context.webhookTimestamp ? new Date(context.webhookTimestamp * 1000).toISOString() : null
        })
        .select()
        .single();

      if (createError) {
        console.error(`[SubscriptionManager] Error creating missing subscription record:`, createError);
        return null;
      }

      return newSubscription;

    } catch (error) {
      console.error(`[SubscriptionManager] Error in createMissingSubscriptionRecord:`, error);
      return null;
    }
  }

  async updateSubscriptionStatus(
    subscriptionId: string,
    newStatus: SubscriptionStatus,
    additionalData: Record<string, unknown> = {},
    webhookTimestamp?: number
  ): Promise<{ success: boolean; message: string }> {
    try {
      const currentState = await this.getCurrentSubscriptionState(subscriptionId);
      if (!currentState) {
        console.warn(`[SubscriptionManager] Subscription ${subscriptionId} not found during status update`);
        return { success: false, message: "Subscription not found" };
      }

      const hasActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(
        newStatus,
        (additionalData.plan_id as string) || currentState.plan_id || 'free'
      );

      const { data: result, error } = await this.supabase.rpc('update_subscription_atomic', {
        p_subscription_id: subscriptionId,
        p_new_status: newStatus,
        p_business_profile_id: currentState.business_profile_id,
        p_has_active_subscription: hasActiveSubscription,
        p_additional_data: additionalData,
        p_webhook_timestamp: webhookTimestamp ? new Date(webhookTimestamp * 1000).toISOString() : null
      });

      if (error) {
        console.error(`[SubscriptionManager] RPC error updating subscription:`, error);
        return { success: false, message: error.message };
      }

      if (!result?.success) {
        console.error(`[SubscriptionManager] RPC function returned error:`, result);
        return { success: false, message: result?.error || 'Unknown RPC error' };
      }

      return {
        success: true,
        message: `Atomically updated subscription to ${newStatus} with has_active_subscription=${hasActiveSubscription}`
      };

    } catch (error) {
      console.error(`[SubscriptionManager] Exception updating subscription:`, error);
      return {
        success: false,
        message: `Exception: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }
}

export const subscriptionManager = await SubscriptionManager.build();