jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      insert: mockInsert.mockReturnThis(),
      select: mockSelect.mockReturnThis(),
    })),
  },
}));

import { createCustomerProfile } from '@/backend/supabase/services/customer/customerProfileService';
import { supabase } from '@/lib/supabase';

const mockInsert = jest.fn();
const mockSelect = jest.fn();
const mockSingle = jest.fn();

describe('customerProfileService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mocks for each test
    mockInsert.mockClear();
    mockSelect.mockClear();
    mockSingle.mockClear();

    // Default mock implementations
    mockInsert.mockReturnThis(); // Ensure insert returns `this` for chaining
    mockSelect.mockReturnThis(); // Ensure select returns `this` for chaining
    mockSingle.mockResolvedValue({ data: {}, error: null }); // Default successful response
  });

  it('should create a customer profile successfully', async () => {
    const mockProfileData = { name: 'Test User', email: '<EMAIL>' };
    const mockUserId = 'user-123';
    const mockResponseData = { id: mockUserId, ...mockProfileData };

    mockSingle.mockResolvedValueOnce({ data: mockResponseData, error: null });

    const result = await createCustomerProfile(mockUserId, mockProfileData);

    expect(supabase.from).toHaveBeenCalledWith('customer_profiles');
    expect(mockInsert).toHaveBeenCalledWith({
      id: mockUserId,
      name: mockProfileData.name,
      email: mockProfileData.email,
    });
    expect(mockSelect).toHaveBeenCalled();
    expect(mockSingle).toHaveBeenCalled();
    expect(result).toEqual({ data: mockResponseData, error: null });
  });

  it('should return an error if profile creation fails', async () => {
    const mockProfileData = { name: 'Test User', email: '<EMAIL>' };
    const mockUserId = 'user-123';
    const mockError = { message: 'Database error', code: '23505' };

    mockSingle.mockResolvedValueOnce({ data: null, error: mockError });

    const result = await createCustomerProfile(mockUserId, mockProfileData);

    expect(supabase.from).toHaveBeenCalledWith('customer_profiles');
    expect(mockInsert).toHaveBeenCalledWith({
      id: mockUserId,
      name: mockProfileData.name,
      email: mockProfileData.email,
    });
    expect(mockSelect).toHaveBeenCalled();
    expect(mockSingle).toHaveBeenCalled();
    expect(result).toEqual({ data: null, error: mockError });
  });
});