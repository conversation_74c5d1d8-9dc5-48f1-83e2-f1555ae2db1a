import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import ProductsTab from '@/src/components/business/ProductsTab';
import { ProductSortOption } from '@/backend/supabase/services/business/businessCardDataService';
import { Tables } from '@/src/types/supabase';

// Mock external components and modules
jest.mock('@/src/components/ui/AnimatedLoader', () => ({
  AnimatedLoader: 'AnimatedLoader',
}));
jest.mock('@/src/components/shared/ui', () => ({
  ProductCard: 'ProductCard',
}));
jest.mock('@/styles/PublicCardViewStyles', () => ({
  createPublicCardViewStyles: jest.fn(() => ({
    section: {},
    searchContainer: {},
    searchInputContainer: {},
    searchIcon: {},
    searchInput: {},
    clearButton: {},
    sortButton: {},
    sortButtonText: {},
    flatListContent: {},
    productsGrid: { testID: 'products-grid' }, // Added testID
    productGridItem: {},
    infiniteScrollTrigger: {},
    loadMoreContainer: {},
    loadMoreText: {},
    emptyText: {},
    modalOverlay: { testID: 'modal-overlay' }, // Added testID
    sortModal: {},
    sortModalTitle: {},
    sortSection: {},
    sortSectionTitle: {},
    sortOption: {},
    sortOptionSelected: {},
    sortOptionText: {},
    sortOptionTextSelected: {},
    productRow: {},
  })),
}));
jest.mock('@/src/components/ui/ProductSkeleton', () => ({
  ProductGridSkeleton: 'ProductGridSkeleton',
}));

const mockProducts: Tables<'products_services'>[] = [
  {
    id: '1',
    name: 'Product A',
    description: 'Description A',
    base_price: 100, // Changed from 'price' to 'base_price'
    is_available: true,
    image_url: null,
    images: [],
    created_at: '2023-01-01T00:00:00Z',
    business_id: 'biz1',
    product_type: 'physical',
    discounted_price: null,
    slug: 'product-a',
    featured_image_index: null,
    updated_at: '2023-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: 'Product B',
    description: 'Description B',
    base_price: 200, // Changed from 'price' to 'base_price'
    is_available: true,
    image_url: null,
    images: [],
    created_at: '2023-01-02T00:00:00Z',
    business_id: 'biz1',
    product_type: 'physical',
    discounted_price: null,
    slug: 'product-b',
    featured_image_index: null,
    updated_at: '2023-01-02T00:00:00Z',
  },
];

const defaultProps = {
  products: mockProducts,
  businessId: 'test-business-id',
  isDark: false,
  loadingMore: false,
  hasMore: false,
  searchQuery: '',
  sortBy: 'newest' as ProductSortOption,
  onLoadMore: jest.fn(),
  onSearch: jest.fn(),
  onSort: jest.fn(),
  useScrollView: false,
  searchSortLoading: false,
};

describe('ProductsTab', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('renders correctly with products', () => {
    render(<ProductsTab {...defaultProps} />);
    expect(screen.getByPlaceholderText('Search products...')).toBeTruthy();
    expect(screen.getByText('Sort')).toBeTruthy();
    expect(screen.getByText('Product A')).toBeTruthy();
    expect(screen.getByText('Product B')).toBeTruthy();
  });

  it('renders empty state when no products', () => {
    render(<ProductsTab {...defaultProps} products={[]} />);
    expect(screen.getByText('No products available')).toBeTruthy();
  });

  it('handles search input change and debounces onSearch', async () => {
    const mockOnSearch = jest.fn();
    render(<ProductsTab {...defaultProps} onSearch={mockOnSearch} />);

    const searchInput = screen.getByPlaceholderText('Search products...');
    fireEvent.changeText(searchInput, 'test');

    expect(mockOnSearch).not.toHaveBeenCalled(); // Should not be called immediately
    jest.advanceTimersByTime(500); // Advance timers by debounce delay
    expect(mockOnSearch).toHaveBeenCalledWith('test');
  });

  it('clears search input and calls onSearch with empty string', async () => {
    const mockOnSearch = jest.fn();
    render(<ProductsTab {...defaultProps} onSearch={mockOnSearch} searchQuery="initial" />);

    const searchInput = screen.getByPlaceholderText('Search products...');
    fireEvent.changeText(searchInput, 'some query'); // Set a value to show clear button
    jest.advanceTimersByTime(500); // Ensure debounce fires

    const clearButton = screen.getByLabelText('Clear search'); // Assuming X icon has an accessible label
    fireEvent.press(clearButton);

    expect(searchInput.props.value).toBe('');
    expect(mockOnSearch).toHaveBeenCalledWith('');
  });

  it('shows and hides sort modal', () => {
    render(<ProductsTab {...defaultProps} />);
    const sortButton = screen.getByText('Sort');
    fireEvent.press(sortButton);
    expect(screen.getByText('Sort Products')).toBeTruthy();

    const modalOverlay = screen.getByTestId('modal-overlay'); // Assuming a testID for the overlay
    fireEvent.press(modalOverlay);
    expect(screen.queryByText('Sort Products')).toBeNull();
  });

  it('calls onSort with selected option when sort option is pressed', () => {
    const mockOnSort = jest.fn();
    render(<ProductsTab {...defaultProps} onSort={mockOnSort} />);

    fireEvent.press(screen.getByText('Sort')); // Open modal
    fireEvent.press(screen.getByText('Price: Low to High')); // Select sort option

    expect(mockOnSort).toHaveBeenCalledWith('price_low');
    expect(screen.queryByText('Sort Products')).toBeNull(); // Modal should close
  });

  it('displays "Newest First" as default sort label', () => {
    render(<ProductsTab {...defaultProps} sortBy="newest" />);
    fireEvent.press(screen.getByText('Sort'));
    expect(screen.getByText('Newest First')).toHaveStyle({ fontWeight: '600' }); // Assuming selected style
  });

  it('calls onLoadMore when FlatList reaches end and hasMore is true', () => {
    const mockOnLoadMore = jest.fn();
    render(<ProductsTab {...defaultProps} hasMore={true} onLoadMore={mockOnLoadMore} />);

    fireEvent(screen.getByTestId('FlatList'), 'onEndReached');
    expect(mockOnLoadMore).toHaveBeenCalledTimes(1);
  });

  it('does not call onLoadMore when FlatList reaches end and hasMore is false', () => {
    const mockOnLoadMore = jest.fn();
    render(<ProductsTab {...defaultProps} hasMore={false} onLoadMore={mockOnLoadMore} />);

    fireEvent(screen.getByTestId('FlatList'), 'onEndReached');
    expect(mockOnLoadMore).not.toHaveBeenCalled();
  });

  it('shows loading indicator in footer when loadingMore and hasMore', () => {
    render(<ProductsTab {...defaultProps} loadingMore={true} hasMore={true} />);
    expect(screen.getByText('Loading more products...')).toBeTruthy();
    expect(screen.getByTestId('AnimatedLoader')).toBeTruthy(); // Assuming AnimatedLoader has this testID
  });

  it('renders ProductGridSkeleton when searchSortLoading is true', () => {
    render(<ProductsTab {...defaultProps} searchSortLoading={true} />);
    expect(screen.getByTestId('ProductGridSkeleton')).toBeTruthy();
    expect(screen.queryByText('Product A')).toBeNull(); // Products should not be rendered
  });

  it('renders products in a View when useScrollView is true', () => {
    render(<ProductsTab {...defaultProps} useScrollView={true} />);
    expect(screen.getByTestId('products-grid')).toBeTruthy(); // Assuming a testID for the products grid View
    expect(screen.queryByTestId('FlatList')).toBeNull(); // FlatList should not be rendered
  });
});