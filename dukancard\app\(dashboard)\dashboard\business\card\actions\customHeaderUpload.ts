"use server";

import { createClient } from "@/utils/supabase/server";

import { getCustomHeaderImagePath } from "@/lib/utils/storage-paths";
import { BUCKETS } from "@/lib/supabase/constants";

export interface CustomHeaderUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface CustomHeaderUpdateResult {
  success: boolean;
  error?: string;
}

/**
 * Upload custom header image with compression and auto-save to database
 */
export async function uploadCustomHeaderImage(
  formData: FormData
): Promise<CustomHeaderUploadResult> {
  try {
    // Create admin client for storage operations
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // Extract the image file from FormData
    const imageFile = formData.get("image") as File;
    if (!imageFile) {
      return {
        success: false,
        error: "No image file provided",
      };
    }

    // Validate file type
    if (!imageFile.type.startsWith("image/")) {
      return {
        success: false,
        error: "Please upload a valid image file",
      };
    }

    // Validate file size (5MB limit)
    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    if (imageFile.size > maxSizeBytes) {
      const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(1);
      return {
        success: false,
        error: `Image size (${fileSizeMB}MB) is too large. Please choose an image smaller than 5MB for optimal performance and faster loading.`,
      };
    }

    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const imagePath = getCustomHeaderImagePath(user.id, timestamp);

    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    // Upload to Supabase Storage using admin client
    const { error: uploadError } = await supabase.storage
      .from(BUCKETS.BUSINESS)
      .upload(imagePath, fileBuffer, {
        contentType: imageFile.type, // Use original file type (already compressed)
        upsert: true
      });

    if (uploadError) {
      console.error("Custom Header Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError.message}`,
      };
    }

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(BUCKETS.BUSINESS)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    // Auto-save to database - update custom_branding field
    const { data: profile, error: fetchError } = await supabase
      .from("business_profiles")
      .select("custom_branding")
      .eq("id", user.id)
      .single();

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current branding data",
      };
    }

    const currentBranding = (profile?.custom_branding as Record<string, unknown>) || {};

    const { error: updateError } = await supabase
      .from("business_profiles")
      .update({
        custom_branding: {
          ...currentBranding,
          custom_header_image_url: urlData.publicUrl,
          hide_dukancard_branding: true, // Auto-enable when image is uploaded
        }
      })
      .eq("id", user.id);

    if (updateError) {
      console.error("Database update error:", updateError);
      // Image uploaded successfully but database update failed
      // We could delete the image here, but let's keep it and return success
      // The user can try again
    }

    return {
      success: true,
      url: urlData.publicUrl,
    };

  } catch (error) {
    console.error("Custom header upload error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during upload.",
    };
  }
}

/**
 * Delete custom header image and reset data
 */
export async function deleteCustomHeaderImage(): Promise<CustomHeaderUpdateResult> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // First, get the current custom branding data to extract the image URL
    const { data: profile, error: fetchError } = await supabase
      .from("business_profiles")
      .select("custom_branding")
      .eq("id", user.id)
      .single();

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current branding data",
      };
    }

    const currentBranding = (profile?.custom_branding as Record<string, unknown>);
    const imageUrl = currentBranding?.custom_header_image_url as string;

    // Delete the image from storage if it exists
    if (imageUrl) {
      try {
        // Extract the file path from the URL
        const urlParts = imageUrl.split('/storage/v1/object/public/business/');
        if (urlParts.length === 2) {
          const filePath = urlParts[1];

          // Use admin client to delete from storage
          const supabase = await createClient();
          const { error: deleteError } = await supabase.storage
            .from(BUCKETS.BUSINESS)
            .remove([filePath]);

          if (deleteError) {
            console.error("Storage deletion error:", deleteError);
            // Continue with database update even if storage deletion fails
          }
        }
      } catch (storageError) {
        console.error("Error deleting custom header from storage:", storageError);
        // Continue with database update even if storage deletion fails
      }
    }

    // Reset custom_header_image_url in database
    const { error: updateError } = await supabase
      .from("business_profiles")
      .update({
        custom_branding: {
          ...currentBranding,
          custom_header_image_url: "",
        }
      })
      .eq("id", user.id);

    if (updateError) {
      return {
        success: false,
        error: "Failed to delete custom header image",
      };
    }

    return { success: true };

  } catch (error) {
    console.error("Custom header delete error:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}
